{"cells": [{"cell_type": "code", "id": "f4ef439fe3ceb24a", "metadata": {"ExecuteTime": {"end_time": "2024-09-06T08:39:30.430281Z", "start_time": "2024-09-06T08:39:30.276808Z"}}, "source": ["from typing import Union\n", "from src.utils.llms.llm_base import LLMBase\n", "from src.utils.actions.other_actions.topk_base import TopkBase\n", "from src.utils.actions.tool_actions.action_excutor import ActionExecutor\n", "from src.prompts.router_prompts import router_system_zh, router_user_zh\n", "from src.utils.llms.nova_SDK import Nova_LLM\n", "from src.utils.utils import (\n", "    stage0_parse_intent,\n", "    stage1_convert2query_with_link,\n", "    stage3_format_web_fetch,\n", "    stage3_format_rest_query_with_link,\n", "    extract_tsl_query_with_link,\n", "    adjust_ranking_sum_to_list_minus_one,\n", "    cal_time,\n", ")\n", "from src.utils.web_content_scrape import WebScrape\n", "from src.prompts.summary_prompts import (\n", "    search_summary_system_prompt_zh,\n", "    search_summary_user_prompt_zh,\n", ")\n", "from src.prompts.follow_up_prompts import (\n", "    follow_up_prompts,\n", ")\n", "import asyncio\n", "from concurrent.futures import ThreadPoolExecutor\n", "\n", "\n", "class WebSearchAgent:\n", "    def __init__(\n", "        self,\n", "        intent_llm: LLMBase,\n", "        topk_method: Union[LLMBase, TopkBase, None],\n", "        summary_llm: LLMBase,\n", "        follow_up_llm: Union[LLMBase, None],\n", "        action_executor: ActionExecutor,\n", "        limit_scarping_time=0.5,\n", "    ) -> None:\n", "        self.action_executor = action_executor\n", "        self.intent_llm = intent_llm\n", "        self.topk_method = topk_method\n", "        self.summary_llm = summary_llm\n", "        self.follow_up_llm = follow_up_llm\n", "        # self.intent_llm.memory.add_message(router_system_zh, add_type=\"system\")\n", "        # self.summary_llm.memory.add_message(search_summary_system_prompt_zh, add_type=\"system\")\n", "        self.crawler = WebScrape(\"windows\", limit_scarping_time)\n", "\n", "    @cal_time(\"stage0_intent_rewrite\")\n", "    def stage0_intent_rewrite(self, query):\n", "        input_query = router_user_zh.format(user_input=query)\n", "        intent_return = self.intent_llm.chat(input_query, stream=True)\n", "        intent_json_output = stage0_parse_intent(query, intent_return)\n", "        return intent_json_output\n", "\n", "    @cal_time(\"stage1_web_search\")\n", "    async def stage1_web_search(self, search_queries):\n", "        \"\"\"\n", "        Args:\n", "            search_queries: [\"今日 深圳 天气\"] or [\"今日 深圳 天气\", \"今日 北京 天气\"] if user's initial query is a complex query.\n", "\n", "        Returns:\n", "\n", "        \"\"\"\n", "        temp_results = {}\n", "        tasks = []\n", "        for single_query in search_queries:\n", "            tasks.append(self.action_executor(\"web_search\", single_query))\n", "        results = await asyncio.gather(*tasks)\n", "        for i, single_query in enumerate(search_queries):\n", "            temp_results[single_query] = results[i]\n", "        return stage1_convert2query_with_link(temp_results)\n", "\n", "    @cal_time(\"stage2_3\")\n", "    async def stage2_3(self, query_with_link):\n", "        #print(query_with_link)\n", "        titles, snippets, links,dates = extract_tsl_query_with_link(query_with_link)\n", "\n", "        links_lens = [\n", "            len(sub_list) for sub_list in links\n", "        ]  #  [[1, 2], [3, 4], [5]] -> [2, 2, 1]\n", "        links = [item for sublist in links for item in sublist]\n", "        loop = asyncio.get_running_loop()\n", "        scarping = asyncio.create_task(self.crawler.scrape(links))\n", "        with ThreadPoolExecutor() as executor:\n", "            ranking_task = loop.run_in_executor(\n", "                executor, self.topk_method.ranking, query_with_link\n", "            )\n", "            ranking_res = await ranking_task\n", "        scarping_res = await scarping\n", "\n", "        topk_idxs = adjust_ranking_sum_to_list_minus_one(ranking_res, links_lens)\n", "        topk_scarping_res = [scarping_res[i][0] for i in topk_idxs]\n", "        image_url = [scarping_res[i][1] for i in range(len(scarping_res))]\n", "\n", "        topk_snippets = []\n", "        topk_links = []\n", "        topk_titles = []\n", "        topk_date = []\n", "        for query, topk_idx_list in ranking_res.items():\n", "            for i, idx in enumerate(topk_idx_list):\n", "                idx = int(idx)\n", "                link = query_with_link[query][idx][\"link\"]\n", "                title = query_with_link[query][idx][\"title\"]\n", "                snippet = query_with_link[query][idx][\"snippet\"]\n", "                date = query_with_link[query][idx][\"date\"]\n", "                topk_snippets.append(snippet)\n", "                topk_titles.append(title)\n", "                topk_links.append(link)\n", "                topk_date.append(date)\n", "                del query_with_link[query][idx]\n", "        llm_ref, frontend_ref,false_total = stage3_format_web_fetch(\n", "            topk_titles,\n", "            topk_snippets,\n", "            topk_links,\n", "            topk_date,\n", "            topk_scarping_res,\n", "            truncate_length=3000,\n", "        )\n", "        rest_ref = stage3_format_rest_query_with_link(query_with_link)\n", "        return (\n", "            llm_ref,\n", "            frontend_ref,\n", "            false_total,\n", "            rest_ref,\n", "            image_url,\n", "        )  # rest_ref剩下没打开的snippet\n", "\n", "    def stage4_llm_summary(self, query, reference_content_, rest_ref):\n", "        summary_user_prompt = search_summary_user_prompt_zh.format(\n", "            web_content=reference_content_, snippets=rest_ref, user_input=query\n", "        )\n", "\n", "        #print(summary_user_prompt)\n", "        summary_res = self.summary_llm.chat(summary_user_prompt, stream=True)\n", "        return summary_res\n", "\n", "    def stage5_follow_up_question(self):\n", "        memory = self.summary_llm.memory.get_all_messages()\n", "        query = memory[-2][\"content\"] +\"\\n\"+memory[-1][\"content\"]+\"\\n\"\n", "        query = follow_up_prompts.format(chat_his =query)\n", "        return self.follow_up_llm.chat(query, stream=True)\n", "\n", "    async def chat(self, query: str):\n", "        intent_json_output = self.stage0_intent_rewrite(query)\n", "        if search_list := intent_json_output[query]:\n", "            print(\"sl\",search_list)\n", "            search_engine_error,query_with_link = await self.stage1_web_search(search_list)\n", "            if search_engine_error:\n", "                print(search_engine_error) ####搜索引擎结果有问题\n", "                llm_responds = self.stage4_llm_summary(\n", "                    query, \"搜索引擎错误\", \"搜索引擎错误\"\n", "                )\n", "                return llm_responds\n", "            if self.topk_method:\n", "                llm_ref, frontend_ref, false_total, rest_ref, image_url = (\n", "                    await self.stage2_3(query_with_link)\n", "                )\n", "                llm_responds = self.stage4_llm_summary(\n", "                     query, llm_ref, rest_ref\n", "                )\n", "                print(f\"爬取错误 {false_total} out of {len(search_list)*k}\")\n", "            else:\n", "                llm_whole_ref = stage3_format_rest_query_with_link(query_with_link)\n", "                llm_responds = self.stage4_llm_summary(\n", "                     query, llm_whole_ref, \"None\"\n", "                )\n", "            self.stage5_follow_up_question()\n", "\n", "        else:\n", "            llm_responds = self.stage4_llm_summary(\n", "                 query, \"None\", \"None\"\n", "            )\n", "\n", "\n", "from src.utils.llms.openai_SDK import OpenAI_LLM\n", "from src.utils.actions.tool_actions.bocha import BoCha\n", "from src.utils.actions.tool_actions.serper import Serper\n", "from src.utils.actions.tool_actions.searxng import SearXng\n", "from src.utils.actions.tool_actions.web_search import WebSearch\n", "from src.utils.memory.memory_base import (\n", "    AllMessageMemory,\n", "    ZeroMessageMemory,\n", ")\n", "from src.utils.actions.tool_actions.action_excutor import ActionExecutor\n", "from src.utils.actions.other_actions.embedding_topk import <PERSON><PERSON><PERSON>\n", "from src.utils.actions.other_actions.agent_topk import AgentTopK\n", "\n", "intent_mem, topk_mem, summary_mem = (\n", "    AllMessageMemory(),\n", "    ZeroMessageMemory(),\n", "    AllMessageMemory(),\n", ")\n", "follow_up_llm = OpenAI_LLM(\"qwen2-72b-instruct-int8\", 1, 0.7)\n", "tools = [WebSearch(search_engines=[Serper(\"google\")])]\n", "# tools = [WebSearch(search_engines=[BoCha()])]\n", "#### qwen2-72b-instruct-fp16,qwen2-72b-instruct-int8, qwen2-72b-instruct-int4\n", "version = \"turbo\"\n", "limit_scarping_time = 0.5\n", "k = 10\n", "topk_embd = Piccolo(k)\n", "model_name = \"qwen2-72b-instruct-int8\"\n", "intent_llm = OpenAI_LLM(model_name, 1, 0.7, intent_mem)\n", "agent_topk_llm = AgentTopK(OpenAI_LLM(model_name, 0, 0.7, AllMessageMemory()), k)\n", "summary_llm = OpenAI_LLM(model_name, 1, 0.7, summary_mem)\n", "intent_llm.memory.add_message(router_system_zh, message_type=\"system\")\n", "summary_llm.memory.add_message(search_summary_system_prompt_zh, message_type=\"system\")\n", "tool_executor = ActionExecutor(tools)\n", "if version == \"light\":\n", "    web = WebSearchAgent(\n", "        intent_llm, None, summary_llm, follow_up_llm, tool_executor, limit_scarping_time\n", "    )\n", "elif version == \"turbo\":\n", "    web = WebSearchAgent(\n", "        intent_llm, topk_embd, summary_llm, follow_up_llm, tool_executor, limit_scarping_time\n", "    )\n", "elif version == \"prime\":\n", "    web = WebSearchAgent(\n", "        intent_llm, topk_embd, summary_llm, follow_up_llm, tool_executor, limit_scarping_time\n", "    )\n", "else:\n", "    print(\"version not supported\")"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-09-06 16:39:30.311 | INFO     | src.utils.llms.openai_SDK:init_openai_clients:37 - OpenAI API Key: qwen2-72b-instruct_key   OpenAI Base URL: qwen2_int8_url\n", "2024-09-06 16:39:30.335 | INFO     | src.utils.llms.openai_SDK:init_openai_clients:39 - ============\n", "2024-09-06 16:39:30.336 | INFO     | src.utils.llms.openai_SDK:init_openai_clients:47 - self.model_name: /mnt/afs/share/model/Qwen/Qwen2-72B-Instruct-GPTQ-Int8, self.config[base_url]:http://101.230.144.204:16908/v1\n", "2024-09-06 16:39:30.344 | INFO     | src.utils.llms.openai_SDK:init_openai_clients:37 - OpenAI API Key: qwen2-72b-instruct_key   OpenAI Base URL: qwen2_int8_url\n", "2024-09-06 16:39:30.370 | INFO     | src.utils.llms.openai_SDK:init_openai_clients:39 - ============\n", "2024-09-06 16:39:30.370 | INFO     | src.utils.llms.openai_SDK:init_openai_clients:47 - self.model_name: /mnt/afs/share/model/Qwen/Qwen2-72B-Instruct-GPTQ-Int8, self.config[base_url]:http://101.230.144.204:16908/v1\n", "2024-09-06 16:39:30.373 | INFO     | src.utils.llms.openai_SDK:init_openai_clients:37 - OpenAI API Key: qwen2-72b-instruct_key   OpenAI Base URL: qwen2_int8_url\n", "2024-09-06 16:39:30.397 | INFO     | src.utils.llms.openai_SDK:init_openai_clients:39 - ============\n", "2024-09-06 16:39:30.397 | INFO     | src.utils.llms.openai_SDK:init_openai_clients:47 - self.model_name: /mnt/afs/share/model/Qwen/Qwen2-72B-Instruct-GPTQ-Int8, self.config[base_url]:http://101.230.144.204:16908/v1\n", "2024-09-06 16:39:30.401 | INFO     | src.utils.llms.openai_SDK:init_openai_clients:37 - OpenAI API Key: qwen2-72b-instruct_key   OpenAI Base URL: qwen2_int8_url\n", "2024-09-06 16:39:30.427 | INFO     | src.utils.llms.openai_SDK:init_openai_clients:39 - ============\n", "2024-09-06 16:39:30.428 | INFO     | src.utils.llms.openai_SDK:init_openai_clients:47 - self.model_name: /mnt/afs/share/model/Qwen/Qwen2-72B-Instruct-GPTQ-Int8, self.config[base_url]:http://101.230.144.204:16908/v1\n"]}], "execution_count": 3}, {"metadata": {"ExecuteTime": {"end_time": "2024-09-06T08:39:55.626440Z", "start_time": "2024-09-06T08:39:48.126985Z"}}, "cell_type": "code", "source": "await web.chat(\"圆明园明天会不会下雨\")", "id": "69f68dc1b55c9874", "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-09-06 16:39:48.335 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - [\"\n", "2024-09-06 16:39:48.354 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 圆\n", "2024-09-06 16:39:48.374 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 明\n", "2024-09-06 16:39:48.394 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 园\n", "2024-09-06 16:39:48.435 | INFO     | src.utils.llms.openai_SDK:openai_call:67 -  天\n", "2024-09-06 16:39:48.455 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 气\n", "2024-09-06 16:39:48.475 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 预报\n", "2024-09-06 16:39:48.495 | INFO     | src.utils.llms.openai_SDK:openai_call:67 -  \n", "2024-09-06 16:39:48.515 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 2\n", "2024-09-06 16:39:48.536 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 0\n", "2024-09-06 16:39:48.555 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 2\n", "2024-09-06 16:39:48.576 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 4\n", "2024-09-06 16:39:48.596 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - -\n", "2024-09-06 16:39:48.616 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 0\n", "2024-09-06 16:39:48.636 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 9\n", "2024-09-06 16:39:48.656 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - -\n", "2024-09-06 16:39:48.676 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 0\n", "2024-09-06 16:39:48.695 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 7\n", "2024-09-06 16:39:48.716 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - \"]\n", "2024-09-06 16:39:48.738 | INFO     | src.utils.llms.openai_SDK:chat:139 - [{'role': 'system', 'content': '你是一个文本分类和改写专家，请根据用户query进行意图分类并改写，并使用以下工具之一进行回复。\\n知识截止日期: 2023-10\\n当前北京时间: {formatted_date}\\n\\n### 工具\\n\\n#### web_search\\n你有`web_search`工具。可以在以下情况下使用`web_search`：\\n- 用户询问关于时事或者需要实时信息的问题（如新闻、天气、体育比分等）\\n- 用户询问某个你完全不熟悉的术语（可能是新术语）\\n- 用户明确要求你浏览或提供参考链接\\n\\n如果查询需要使用\"web_search\"工具，你需要考虑以下内容：\\n- 当查询过于复杂时，需要将其分解为多个子问题，并重写每个子问题。\\n- 重写时，对于时间你需要做出推理替换，使描述更准确，比如“去年”、“现在”等，需要根据当前日期转换成对应的年份或时间。\\n- 将你的回答放入[]中，禁止说任何额外的东西。\\n\\n当一个query需要在线检索时，你应该参考下面的例子:\\n当前输入: 去年中国的GDP是多少\\n[\"中国 GDP 2023\"]\\n\\n当前输入: 川普最近怎么了\\n[\"特朗普 新闻\"]\\n\\n当前输入: 北京中关村有coco奶茶店吗？霸王茶姬呢？\\n[\"北京中关村 coco奶茶店\",\"北京中关村 霸王茶姬\"]\\n\\n多轮对话中，你应该考虑上下文中的指代消解:\\n当前输入: 去年中国的GDP是多少\\n[\"中国 GDP 2023\"]\\n当前输入: 今年呢\\n[\"中国 GDP 2024\"]\\n当前输入: 美国的呢\\n[\"美国 GDP 2023\",\"美国 GDP 2024\"]\\n当前输入: 好的，我知道了\\n[]\\n当前输入: 我们聊过什么\\n[]\\n\\n#### None\\n你有`None`工具。可以在以下情况下使用`None`：\\n- 你不需要使用工具, 你可以直接回答这个问题。\\n\\n当一个query不需要在线检索时，你应该参考下面的例子:\\n当前输入: 介绍一下中国的四大发明。\\n[]\\n\\n'}, {'role': 'user', 'content': '当前输入: 圆明园明天会不会下雨'}, {'role': 'assistant', 'content': '[\"圆明园 天气预报\"]'}, {'role': 'user', 'content': '当前输入: 圆明园明天会不会下雨'}, {'role': 'assistant', 'content': '[\"圆明园 天气预报 2024-09-07\"]'}]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "stage0_intent_rewrite runs 0.6099 sec\n", "sl ['圆明园 天气预报 2024-09-07']\n", "stage1_web_search runs 0.8121 sec\n", "https://weather.cma.cn/web/weather/58549.html Timeout scarping_error web length 0\n", "https://www.bjfsh.gov.cn/bsfw2/tdrqfw/cjr/tqybl/202405/t20240507_40075634.shtml Timeout scarping_error web length 0\n", "https://www.shengsi.gov.cn/col/col1416341/index.html web length 135\n", "http://bj.cma.gov.cn/ web length 0\n", "https://weather.sz.gov.cn/qixiangfuwu/qihoufuwu/qihouyuce/jiqihouqushiyuce/index.html web length 532\n", "http://bj.weather.com.cn/ web length 0\n", "https://weather.cma.cn/web/weather/54511.html web length 3318\n", "http://www.weather.com.cn/weather/101010200.shtml web length 72\n", "http://www.weather.com.cn/weather/101181405.shtml web length 72\n", "http://www.tqyb.com.cn/ web length 65\n", "https://weather.cma.cn/ web length 0\n", "get_html time: 0.5030 seconds\n", "extract time: 0.2366 seconds\n", "stage2_3 runs 0.7422 sec\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2024-09-06 16:39:50.796 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 关于\n", "2024-09-06 16:39:50.815 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 圆\n", "2024-09-06 16:39:50.836 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 明\n", "2024-09-06 16:39:50.856 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 园\n", "2024-09-06 16:39:50.876 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 明天\n", "2024-09-06 16:39:50.897 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 是否会\n", "2024-09-06 16:39:50.916 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 下雨\n", "2024-09-06 16:39:50.936 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 的问题\n", "2024-09-06 16:39:50.956 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ，\n", "2024-09-06 16:39:50.976 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 不同\n", "2024-09-06 16:39:50.996 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 来源\n", "2024-09-06 16:39:51.016 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 的\n", "2024-09-06 16:39:51.036 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 天气\n", "2024-09-06 16:39:51.056 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 预报\n", "2024-09-06 16:39:51.076 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 给出了\n", "2024-09-06 16:39:51.096 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 不同的\n", "2024-09-06 16:39:51.116 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 预测\n", "2024-09-06 16:39:51.136 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 。\n", "2024-09-06 16:39:51.156 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 海淀区\n", "2024-09-06 16:39:51.176 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 的\n", "2024-09-06 16:39:51.197 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 天气\n", "2024-09-06 16:39:51.217 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 预报\n", "2024-09-06 16:39:51.236 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 显示\n", "2024-09-06 16:39:51.256 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ，\n", "2024-09-06 16:39:51.277 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 明天\n", "2024-09-06 16:39:51.297 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - （\n", "2024-09-06 16:39:51.317 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 2\n", "2024-09-06 16:39:51.337 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 0\n", "2024-09-06 16:39:51.356 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 2\n", "2024-09-06 16:39:51.375 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 4\n", "2024-09-06 16:39:51.395 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 年\n", "2024-09-06 16:39:51.416 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 0\n", "2024-09-06 16:39:51.436 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 9\n", "2024-09-06 16:39:51.456 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 月\n", "2024-09-06 16:39:51.476 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 0\n", "2024-09-06 16:39:51.498 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 7\n", "2024-09-06 16:39:51.518 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 日\n", "2024-09-06 16:39:51.538 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ）\n", "2024-09-06 16:39:51.558 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 从\n", "2024-09-06 16:39:51.578 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 多\n", "2024-09-06 16:39:51.599 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 云\n", "2024-09-06 16:39:51.619 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 转\n", "2024-09-06 16:39:51.639 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 为\n", "2024-09-06 16:39:51.659 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 小\n", "2024-09-06 16:39:51.679 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 雨\n", "2024-09-06 16:39:51.699 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - [\n", "2024-09-06 16:39:51.718 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 4\n", "2024-09-06 16:39:51.738 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ]\n", "2024-09-06 16:39:51.758 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ，\n", "2024-09-06 16:39:51.778 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 而\n", "2024-09-06 16:39:51.799 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 北京市\n", "2024-09-06 16:39:51.819 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 气象\n", "2024-09-06 16:39:51.839 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 局\n", "2024-09-06 16:39:51.859 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 的数据\n", "2024-09-06 16:39:51.879 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 则\n", "2024-09-06 16:39:51.899 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 没有\n", "2024-09-06 16:39:51.919 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 明确\n", "2024-09-06 16:39:51.940 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 提到\n", "2024-09-06 16:39:51.959 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 降雨\n", "2024-09-06 16:39:51.980 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 情况\n", "2024-09-06 16:39:51.999 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ，\n", "2024-09-06 16:39:52.020 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 仅\n", "2024-09-06 16:39:52.040 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 指出\n", "2024-09-06 16:39:52.060 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 最高\n", "2024-09-06 16:39:52.081 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 气温\n", "2024-09-06 16:39:52.100 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 为\n", "2024-09-06 16:39:52.122 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 3\n", "2024-09-06 16:39:52.141 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 1\n", "2024-09-06 16:39:52.161 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ℃\n", "2024-09-06 16:39:52.181 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - [\n", "2024-09-06 16:39:52.201 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 3\n", "2024-09-06 16:39:52.220 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ]\n", "2024-09-06 16:39:52.242 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 。\n", "2024-09-06 16:39:52.370 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 另一\n", "2024-09-06 16:39:52.392 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 份\n", "2024-09-06 16:39:52.414 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 资料\n", "2024-09-06 16:39:52.436 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 中\n", "2024-09-06 16:39:52.458 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 提到\n", "2024-09-06 16:39:52.481 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 北京\n", "2024-09-06 16:39:52.504 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 地区\n", "2024-09-06 16:39:52.524 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 在\n", "2024-09-06 16:39:52.544 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 9\n", "2024-09-06 16:39:52.565 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 月\n", "2024-09-06 16:39:52.585 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 7\n", "2024-09-06 16:39:52.605 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 日\n", "2024-09-06 16:39:52.625 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 可能会\n", "2024-09-06 16:39:52.645 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 有\n", "2024-09-06 16:39:52.671 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 小\n", "2024-09-06 16:39:52.691 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 雨\n", "2024-09-06 16:39:52.711 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 转\n", "2024-09-06 16:39:52.732 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 多\n", "2024-09-06 16:39:52.752 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 云\n", "2024-09-06 16:39:52.771 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 的情况\n", "2024-09-06 16:39:52.790 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - [\n", "2024-09-06 16:39:52.811 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 5\n", "2024-09-06 16:39:52.831 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ]\n", "2024-09-06 16:39:52.851 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 。\n", "\n", "\n", "2024-09-06 16:39:52.871 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 综合\n", "2024-09-06 16:39:52.891 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 上述\n", "2024-09-06 16:39:52.911 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 信息\n", "2024-09-06 16:39:52.931 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ，在\n", "2024-09-06 16:39:52.951 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 不同\n", "2024-09-06 16:39:52.971 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 预报\n", "2024-09-06 16:39:52.991 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 中\n", "2024-09-06 16:39:53.011 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 存在\n", "2024-09-06 16:39:53.031 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 一定的\n", "2024-09-06 16:39:53.050 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 差异\n", "2024-09-06 16:39:53.071 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 性\n", "2024-09-06 16:39:53.091 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ，\n", "2024-09-06 16:39:53.111 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 但\n", "2024-09-06 16:39:53.131 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 多数\n", "2024-09-06 16:39:53.151 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 信息\n", "2024-09-06 16:39:53.170 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 指向\n", "2024-09-06 16:39:53.190 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 了\n", "2024-09-06 16:39:53.210 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 北京\n", "2024-09-06 16:39:53.229 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 地区\n", "2024-09-06 16:39:53.250 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 明天\n", "2024-09-06 16:39:53.270 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 有\n", "2024-09-06 16:39:53.290 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 雨\n", "2024-09-06 16:39:53.310 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 的可能性\n", "2024-09-06 16:39:53.330 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 。\n", "2024-09-06 16:39:53.350 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 鉴于\n", "2024-09-06 16:39:53.370 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 天气\n", "2024-09-06 16:39:53.390 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 状况\n", "2024-09-06 16:39:53.410 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 具有\n", "2024-09-06 16:39:53.430 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 不确定性\n", "2024-09-06 16:39:53.450 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ，并\n", "2024-09-06 16:39:53.470 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 且\n", "2024-09-06 16:39:53.489 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 可能\n", "2024-09-06 16:39:53.509 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 受到\n", "2024-09-06 16:39:53.530 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 局部\n", "2024-09-06 16:39:53.549 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 气象\n", "2024-09-06 16:39:53.570 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 条件\n", "2024-09-06 16:39:53.590 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 的影响\n", "2024-09-06 16:39:53.610 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ，请\n", "2024-09-06 16:39:53.630 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 密切关注\n", "2024-09-06 16:39:53.650 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 临近\n", "2024-09-06 16:39:53.670 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 时间\n", "2024-09-06 16:39:53.690 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 点\n", "2024-09-06 16:39:53.710 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 的\n", "2024-09-06 16:39:53.730 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 实时\n", "2024-09-06 16:39:53.750 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 天气\n", "2024-09-06 16:39:53.771 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 更新\n", "2024-09-06 16:39:53.790 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 和\n", "2024-09-06 16:39:53.810 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 官方\n", "2024-09-06 16:39:53.831 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 发布的\n", "2024-09-06 16:39:53.850 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 最新\n", "2024-09-06 16:39:53.870 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 预警\n", "2024-09-06 16:39:53.890 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 信号\n", "2024-09-06 16:39:53.910 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 。\n", "\n", "\n", "2024-09-06 16:39:53.930 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 注\n", "2024-09-06 16:39:53.950 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 释\n", "2024-09-06 16:39:53.970 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 引用\n", "2024-09-06 16:39:53.990 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ：\n", "2024-09-06 16:39:54.010 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - [[\n", "2024-09-06 16:39:54.030 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 3\n", "2024-09-06 16:39:54.050 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ][\n", "2024-09-06 16:39:54.070 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 4\n", "2024-09-06 16:39:54.089 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ][\n", "2024-09-06 16:39:54.109 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 5\n", "2024-09-06 16:39:54.129 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ]]\n", "2024-09-06 16:39:54.151 | INFO     | src.utils.llms.openai_SDK:chat:139 - [{'role': 'system', 'content': '\\n从现在开始你是SenseChat，一个由商汤科技训练的大型语言模型，基于Transformer架构。\\n当前日期：{formatted_date}\\n请根据提供的网络搜索结果（包括页面标题、摘要和页面内容）生成一个全面的回答。\\n\\n## 要求\\n- 基于\"当前问题\"的搜索结果，撰写详细完备的回复，优先回答\"当前问题\"\\n- 直接在回答中整合信息，禁止列出参考文献或提供URL链接\\n- 如果没有搜索结果，请根据你的知识生成回答，直接正常回答用户\\n- 你必须使用[数字]注释来引用答案中的内容,确保每条引文仅对应相关的句子或段落\\n- 不要在同一句子中引用重复的编号，确保引用编号的唯一性\\n- 引用多个来源时，用以下格式：[1][3][6]。请避免过多或不相关的引用\\n- 如无搜索结果，请根据你的知识生成回答，无需引用\\n\\n## 注意事项\\n- 你需要根据\\'当前日期\\'来找出\\'带索引的网页详细信息\\'中是否包含与用户问题直接相关的答案\\n- \"带索引的网页详细信息\"中可能包含噪音信息，你需要忽略噪音找到和用户问题相关的关键部分\\n- 如果\"带索引的网页详细信息\"为None，或\"潜在可能帮助的短信息\"为None，请忽略引用，直接正常回答用户\\n- 内容必须清晰、有条理，完全围绕用户的问题展开\\n- 避免重复用户的输入内容，生成具有独立价值的回答\\n- 避免使用否定式指令，保持正向、明确的表达方式\\n- 你需要分析，选择最准确的信息完成总结和回答\\n- 你的回答不能有冲突和矛盾的地方\\n- 尽量少回答与用户query相关性不大的信息\\n- 不要做重复的回答\\n\\n'}, {'role': 'user', 'content': '当前问题: 圆明园明天会不会下雨\\n'}, {'role': 'assistant', 'content': '根据最新的天气预报，圆明园在明天[1]即2024年09月07日的天气预计为多云转晴，不会下雨。然而，另一份预报显示明天有中雨[10]。由于存在冲突信息，建议临近出行时再次查看实时更新的天气预报以获取最准确的信息。\\n根据其他来源提供的信息，在明天之后的几天内，圆明园地区确实有雨天预报[8]。因此，尽管明天可能不下雨，但随后几天有可能出现降雨，请做好相应的准备。\\n为了确保行程顺利，请关注当地气象部门发布的最新预警信号和各类气象资讯。如果计划在户外活动或参观圆明园遗址公园，请考虑携带防雨装备，并留意景区内的生活指数、健康指数、交通指数和旅游指数等实用信息[6][9][10]。\\n请注意气温变化并适当调整着装建议：温度将在18℃至35℃之间波动，在室内与室外温差较大的情况下穿着针织衫可以提供额外舒适度；白天可选择短袖上衣加七分裤搭配以应对炎热天气；夜间则适宜穿着单层棉麻面料短套装或T恤衫裤等轻松服装保持舒适感。\\n\\n综上所述：\\n- 圆明园明日是否下雨存在不确定性；\\n- 预计接下来数日内将出现降雨情况；\\n- 建议密切关注官方发布的最新气象预警及周边景点相关资讯；\\n- 根据温度变化合理规划个人着装避免受凉或过热影响体验感。\\n\\n注释引用：[[1][8][10]]'}, {'role': 'user', 'content': '当前问题: 圆明园明天会不会下雨\\n'}, {'role': 'assistant', 'content': '关于圆明园明天是否会下雨的问题，不同来源的天气预报给出了不同的预测。海淀区的天气预报显示，明天（2024年09月07日）从多云转为小雨[4]，而北京市气象局的数据则没有明确提到降雨情况，仅指出最高气温为31℃[3]。另一份资料中提到北京地区在9月7日可能会有小雨转多云的情况[5]。\\n\\n综合上述信息，在不同预报中存在一定的差异性，但多数信息指向了北京地区明天有雨的可能性。鉴于天气状况具有不确定性，并且可能受到局部气象条件的影响，请密切关注临近时间点的实时天气更新和官方发布的最新预警信号。\\n\\n注释引用：[[3][4][5]]'}]\n", "2024-09-06 16:39:54.154 | INFO     | __main__:stage5_follow_up_question:140 - [{'role': 'system', 'content': '\\n从现在开始你是SenseChat，一个由商汤科技训练的大型语言模型，基于Transformer架构。\\n当前日期：{formatted_date}\\n请根据提供的网络搜索结果（包括页面标题、摘要和页面内容）生成一个全面的回答。\\n\\n## 要求\\n- 基于\"当前问题\"的搜索结果，撰写详细完备的回复，优先回答\"当前问题\"\\n- 直接在回答中整合信息，禁止列出参考文献或提供URL链接\\n- 如果没有搜索结果，请根据你的知识生成回答，直接正常回答用户\\n- 你必须使用[数字]注释来引用答案中的内容,确保每条引文仅对应相关的句子或段落\\n- 不要在同一句子中引用重复的编号，确保引用编号的唯一性\\n- 引用多个来源时，用以下格式：[1][3][6]。请避免过多或不相关的引用\\n- 如无搜索结果，请根据你的知识生成回答，无需引用\\n\\n## 注意事项\\n- 你需要根据\\'当前日期\\'来找出\\'带索引的网页详细信息\\'中是否包含与用户问题直接相关的答案\\n- \"带索引的网页详细信息\"中可能包含噪音信息，你需要忽略噪音找到和用户问题相关的关键部分\\n- 如果\"带索引的网页详细信息\"为None，或\"潜在可能帮助的短信息\"为None，请忽略引用，直接正常回答用户\\n- 内容必须清晰、有条理，完全围绕用户的问题展开\\n- 避免重复用户的输入内容，生成具有独立价值的回答\\n- 避免使用否定式指令，保持正向、明确的表达方式\\n- 你需要分析，选择最准确的信息完成总结和回答\\n- 你的回答不能有冲突和矛盾的地方\\n- 尽量少回答与用户query相关性不大的信息\\n- 不要做重复的回答\\n\\n'}, {'role': 'user', 'content': '当前问题: 圆明园明天会不会下雨\\n'}, {'role': 'assistant', 'content': '根据最新的天气预报，圆明园在明天[1]即2024年09月07日的天气预计为多云转晴，不会下雨。然而，另一份预报显示明天有中雨[10]。由于存在冲突信息，建议临近出行时再次查看实时更新的天气预报以获取最准确的信息。\\n根据其他来源提供的信息，在明天之后的几天内，圆明园地区确实有雨天预报[8]。因此，尽管明天可能不下雨，但随后几天有可能出现降雨，请做好相应的准备。\\n为了确保行程顺利，请关注当地气象部门发布的最新预警信号和各类气象资讯。如果计划在户外活动或参观圆明园遗址公园，请考虑携带防雨装备，并留意景区内的生活指数、健康指数、交通指数和旅游指数等实用信息[6][9][10]。\\n请注意气温变化并适当调整着装建议：温度将在18℃至35℃之间波动，在室内与室外温差较大的情况下穿着针织衫可以提供额外舒适度；白天可选择短袖上衣加七分裤搭配以应对炎热天气；夜间则适宜穿着单层棉麻面料短套装或T恤衫裤等轻松服装保持舒适感。\\n\\n综上所述：\\n- 圆明园明日是否下雨存在不确定性；\\n- 预计接下来数日内将出现降雨情况；\\n- 建议密切关注官方发布的最新气象预警及周边景点相关资讯；\\n- 根据温度变化合理规划个人着装避免受凉或过热影响体验感。\\n\\n注释引用：[[1][8][10]]'}, {'role': 'user', 'content': '当前问题: 圆明园明天会不会下雨\\n'}, {'role': 'assistant', 'content': '关于圆明园明天是否会下雨的问题，不同来源的天气预报给出了不同的预测。海淀区的天气预报显示，明天（2024年09月07日）从多云转为小雨[4]，而北京市气象局的数据则没有明确提到降雨情况，仅指出最高气温为31℃[3]。另一份资料中提到北京地区在9月7日可能会有小雨转多云的情况[5]。\\n\\n综合上述信息，在不同预报中存在一定的差异性，但多数信息指向了北京地区明天有雨的可能性。鉴于天气状况具有不确定性，并且可能受到局部气象条件的影响，请密切关注临近时间点的实时天气更新和官方发布的最新预警信号。\\n\\n注释引用：[[3][4][5]]'}]\n", "2024-09-06 16:39:54.323 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 如果\n", "2024-09-06 16:39:54.342 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 圆\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "爬取错误 6 out of 10\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2024-09-06 16:39:54.362 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 明\n", "2024-09-06 16:39:54.382 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 园\n", "2024-09-06 16:39:54.402 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 明天\n", "2024-09-06 16:39:54.422 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 真的\n", "2024-09-06 16:39:54.442 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 下雨\n", "2024-09-06 16:39:54.462 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ，\n", "2024-09-06 16:39:54.482 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 那么\n", "2024-09-06 16:39:54.503 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 降雨\n", "2024-09-06 16:39:54.522 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 量\n", "2024-09-06 16:39:54.542 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 预计\n", "2024-09-06 16:39:54.564 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 会\n", "2024-09-06 16:39:54.584 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 是多少\n", "2024-09-06 16:39:54.602 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 呢\n", "2024-09-06 16:39:54.621 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ？\n", "2024-09-06 16:39:54.641 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - //\n", "2024-09-06 16:39:54.661 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 考虑到\n", "2024-09-06 16:39:54.682 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 天气\n", "2024-09-06 16:39:54.702 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 预报\n", "2024-09-06 16:39:54.722 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 的\n", "2024-09-06 16:39:54.741 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 不确定性\n", "2024-09-06 16:39:54.761 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ，\n", "2024-09-06 16:39:54.781 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 我们\n", "2024-09-06 16:39:54.802 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 应当\n", "2024-09-06 16:39:54.821 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 关注\n", "2024-09-06 16:39:54.841 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 哪些\n", "2024-09-06 16:39:54.861 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 实时\n", "2024-09-06 16:39:54.881 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 天气\n", "2024-09-06 16:39:54.900 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 更新\n", "2024-09-06 16:39:54.920 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 渠道\n", "2024-09-06 16:39:54.940 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 以\n", "2024-09-06 16:39:54.960 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 获取\n", "2024-09-06 16:39:54.980 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 最\n", "2024-09-06 16:39:55.000 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 准确\n", "2024-09-06 16:39:55.021 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 的信息\n", "2024-09-06 16:39:55.040 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ？\n", "2024-09-06 16:39:55.061 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - //\n", "2024-09-06 16:39:55.081 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 对于\n", "2024-09-06 16:39:55.100 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 计划\n", "2024-09-06 16:39:55.122 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 明天\n", "2024-09-06 16:39:55.142 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 访问\n", "2024-09-06 16:39:55.161 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 圆\n", "2024-09-06 16:39:55.182 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 明\n", "2024-09-06 16:39:55.202 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 园\n", "2024-09-06 16:39:55.221 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 的\n", "2024-09-06 16:39:55.241 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 游客\n", "2024-09-06 16:39:55.261 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 来说\n", "2024-09-06 16:39:55.281 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ，\n", "2024-09-06 16:39:55.301 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 如果\n", "2024-09-06 16:39:55.321 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 遇到\n", "2024-09-06 16:39:55.341 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 下雨\n", "2024-09-06 16:39:55.361 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 天气\n", "2024-09-06 16:39:55.382 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ，\n", "2024-09-06 16:39:55.401 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 有哪些\n", "2024-09-06 16:39:55.421 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 应对\n", "2024-09-06 16:39:55.441 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 措施\n", "2024-09-06 16:39:55.461 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 或\n", "2024-09-06 16:39:55.481 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 备\n", "2024-09-06 16:39:55.501 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 选\n", "2024-09-06 16:39:55.521 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 活动\n", "2024-09-06 16:39:55.541 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 可以\n", "2024-09-06 16:39:55.561 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - 考虑\n", "2024-09-06 16:39:55.580 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - ？\n", "2024-09-06 16:39:55.600 | INFO     | src.utils.llms.openai_SDK:openai_call:67 - //\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "execution_count": 5}, {"metadata": {"ExecuteTime": {"end_time": "2024-09-06T02:35:20.693164Z", "start_time": "2024-09-06T02:35:20.667182Z"}}, "cell_type": "code", "source": "web.summary_llm.memory.get_all_messages()", "id": "e85d36df702826ca", "outputs": [{"data": {"text/plain": ["[{'role': 'system',\n", "  'content': '\\n从现在开始你是SenseChat，一个由商汤科技训练的大型语言模型，基于Transformer架构。\\n当前日期：{formatted_date}\\n请根据提供的网络搜索结果（包括页面标题、摘要和页面内容）生成一个全面的回答。\\n\\n## 要求\\n- 基于\"当前问题\"的搜索结果，撰写详细完备的回复，优先回答\"当前问题\"\\n- 直接在回答中整合信息，禁止列出参考文献或提供URL链接\\n- 如果没有搜索结果，请根据你的知识生成回答，直接正常回答用户\\n- 你必须使用[数字]注释来引用答案中的内容,确保每条引文仅对应相关的句子或段落\\n- 不要在同一句子中引用重复的编号，确保引用编号的唯一性\\n- 引用多个来源时，用以下格式：[1][3][6]。请避免过多或不相关的引用\\n- 如无搜索结果，请根据你的知识生成回答，无需引用\\n\\n## 注意事项\\n- 你需要根据\\'当前日期\\'来找出\\'带索引的网页详细信息\\'中是否包含与用户问题直接相关的答案\\n- \"带索引的网页详细信息\"中可能包含噪音信息，你需要忽略噪音找到和用户问题相关的关键部分\\n- 如果\"带索引的网页详细信息\"为None，或\"潜在可能帮助的短信息\"为None，请忽略引用，直接正常回答用户\\n- 内容必须清晰、有条理，完全围绕用户的问题展开\\n- 避免重复用户的输入内容，生成具有独立价值的回答\\n- 避免使用否定式指令，保持正向、明确的表达方式\\n- 你需要分析，选择最准确的信息完成总结和回答\\n- 你的回答不能有冲突和矛盾的地方\\n- 尽量少回答与用户query相关性不大的信息\\n- 不要做重复的回答\\n\\n'},\n", " {'role': 'user', 'content': '当前问题: 圆明园明天会不会下雨\\n'},\n", " {'role': 'assistant',\n", "  'content': '根据天气预报，圆明园在明天2024年09月07日的天气预计为多云转晴[1]，没有下雨的预报。然而，天气变化莫测，建议出行前再次确认最新的天气信息。'}]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "execution_count": 3}, {"metadata": {"ExecuteTime": {"end_time": "2024-09-06T02:35:30.243240Z", "start_time": "2024-09-06T02:35:22.398234Z"}}, "cell_type": "code", "source": "await web.chat(\"从那到故宫多久\")", "id": "3f7fca5f898c651b", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\"圆明园到故宫 距离 时间\"]\n", "stage0_intent_rewrite runs 0.5298 sec\n", "sl ['圆明园到故宫 距离 时间']\n", "stage1_web_search runs 0.8905 sec\n", "https://www.dpm.org.cn/lemmas/244052 Timeout scarping_error web length 0\n", "http://m.51chaoshang.com/wendas/e8ee0870-8249-11eb-8b96-d916c2b7a337 Timeout scarping_error web length 0\n", "http://www.51chaoshang.com/wendas/b91e4360-8249-11eb-93e2-adea7abeafcf Timeout scarping_error web length 0\n", "https://m.kkday.com/zh-cn/product/184910 Timeout scarping_error web length 0\n", "https://m.weelv.com/qa/10769/168690.html Timeout scarping_error web length 0\n", "https://www.kkday.com/zh-my/product/156224 Timeout scarping_error web length 0\n", "https://m.cct.cn/dujia/366436.html web length 327\n", "https://zhidao.baidu.com/question/141379427 web length 0\n", "https://www.sohu.com/a/486155868_100166765 web length 3009\n", "http://www.beijing.gov.cn/shipin/qushuobeijing/18999.html web length 670\n", "https://www.sohu.com/a/768904732_121925678 web length 742\n", "https://piao.qunar.com/daytrip/detail_3266989.html?productId=1246125450&st=a3clM0QlRTUlOEMlOTclRTQlQkElQUMlMjZpZCUzRDI2MTQwMjI1JTI2dHlwZSUzRDElMjZpZHglM0Q2MiUyNnF0JTNEbnVsbCUyNmFwayUzRDAlMjZzYyUzRE9ORURBWV9UT1VSJTI2dXIlM0QlRTUlOEMlOTclRTQlQkElQUMlMjZsciUzRCVFNSU4QyU5NyVFNCVCQSVBQw%3D%3D&from=mpldaytrip_city web length 405\n", "http://bjtxt.net/a/beijingyou/beijingliangriyou/2017/0424/53.html web length 934\n", "https://m.cct.cn/dujia/413897.html web length 880\n", "https://zhidao.baidu.com/question/88788052.html web length 0\n", "https://mt.sohu.com/a/804012527_122027435 web length 194\n", "https://www.visitbeijing.com.cn/article/47QrJn7UwR7?device=amp&device=amp web length 2190\n", "https://www.163.com/dy/article/IQ6EDDUR0544P625.html web length 1603\n", "http://www.oct-cts.com/Travel/Route/861.html web length 345\n", "https://blog.sina.com.cn/s/blog_4c727bdf0102z6lo.html web length 0\n", "get_html time: 0.4997 seconds\n", "extract time: 0.3510 seconds\n", "stage2_3 runs 0.8568 sec\n", "从不同的地点到达故宫的时间会有所不同。例如，从圆明园到故宫，直线距离大约为12.6公里[3]，乘坐地铁并换乘的话，预计总耗时在1小时左右[1]。然而，具体时间还会受到交通状况、换乘等待时间以及是否在高峰期出行等因素的影响。因此，在规划行程时，请预留充足的时间以应对可能的延误。\n", "\n", "如果您从颐和园出发前往故宫，则需花费更长的时间，约为26公里的距离，并可能需要近2个小时的车程[2]。\n", "\n", "另外，在规划行程时，请考虑门票预订、体力保持、安全问题以及特色体验活动等综合因素[4]。\n", "<|im_sep|>\n", "爬取错误 6 out of 10\n", "从颐和园到故宫的交通方式有哪些，哪种方式最快？//\n", "在高峰期，从圆明园到故宫的实际耗时可能会增加多少？//\n", "考虑到门票预订和特色体验活动，访问故宫的最佳时间是什么时候？//\n"]}], "execution_count": 4}, {"metadata": {"ExecuteTime": {"end_time": "2024-09-06T02:20:30.164738Z", "start_time": "2024-09-06T02:20:17.387956Z"}}, "cell_type": "code", "source": "await web.chat(\"我只想知道我一开始问的\")", "id": "d1cf936edbb1e515", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'role': 'system', 'content': '你是一个文本分类和改写专家，请根据用户query进行意图分类并改写，并使用以下工具之一进行回复。\\n知识截止日期: 2023-10\\n当前北京时间: 2024-09-06 10:20\\n\\n### 工具\\n\\n#### web_search\\n你有`web_search`工具。可以在以下情况下使用`web_search`：\\n- 用户询问关于时事或者需要实时信息的问题（如新闻、天气、体育比分等）\\n- 用户询问某个你完全不熟悉的术语（可能是新术语）\\n- 用户明确要求你浏览或提供参考链接\\n\\n如果查询需要使用\"web_search\"工具，你需要考虑以下内容：\\n- 当查询过于复杂时，需要将其分解为多个子问题，并重写每个子问题。\\n- 重写时，对于时间你需要做出推理替换，使描述更准确，比如“去年”、“现在”等，需要根据当前日期转换成对应的年份或时间。\\n- 将你的回答放入[]中，禁止说任何额外的东西\\n\\n当一个query需要在线检索时，你应该参考下面的例子:\\n当前输入: 去年中国的GDP是多少\\n[\"中国 GDP 2023\"]\\n\\n当前输入: 川普最近怎么了\\n[\"特朗普 新闻\"]\\n\\n当前输入: 北京中关村有coco奶茶店吗？霸王茶姬呢？\\n[\"北京中关村 coco奶茶店\",\"北京中关村 霸王茶姬\"]\\n\\n多轮对话中，你应该考虑上下文中的指代消解:\\n当前输入: 去年中国的GDP是多少\\n[\"中国 GDP 2023\"]\\n当前输入: 今年呢\\n[\"中国 GDP 2024\"]\\n当前输入: 美国的呢\\n[\"美国 GDP 2023\",\"美国 GDP 2024\"]\\n当前输入: 好的，我知道了\\n[]\\n当前输入: 我们聊过什么\\n[]\\n\\n#### None\\n你有`None`工具。可以在以下情况下使用`None`：\\n- 你不需要使用工具, 你可以直接回答这个问题。\\n\\n当一个query不需要在线检索时，你应该参考下面的例子:\\n当前输入: 介绍一下中国的四大发明。\\n[]\\n\\n'}, {'role': 'user', 'content': '当前输入: 圆明园明天会不会下雨'}, {'role': 'assistant', 'content': '[\"圆明园 天气预报\"]'}, {'role': 'user', 'content': '当前输入: 从那到故宫多久'}, {'role': 'assistant', 'content': '[\"圆明园到故宫 距离 时间\"]'}, {'role': 'user', 'content': '当前输入: 我只想知道我一开始问的'}]\n", "[\"圆明园 天气预报\"]\n", "stage0_intent_rewrite runs 0.4046 sec\n", "sl ['圆明园 天气预报']\n", "stage1_web_search runs 0.7049 sec\n", "'gbk' codec can't decode byte 0xae in position 9088: illegal multibyte sequence Other scarping_error web length 0\n", "'gbk' codec can't decode byte 0xae in position 9045: illegal multibyte sequence Other scarping_error web length 0\n", "https://www.chatianqi.cn/jingdian/beijing-yuanmingyuanyizhigongyuan/ Timeout scarping_error web length 0\n", "https://tianqi.fliggy.com/110100/1345-1 Timeout scarping_error web length 0\n", "https://tianqi.fliggy.com/110100/16643127-1 Timeout scarping_error web length 0\n", "https://m.tianqi.com/wuyishan/ymyjingdian/7/ Timeout scarping_error web length 0\n", "http://www.weather.com.cn/weather15d/10101020011A.shtml web length 109\n", "http://m.bj.bendibao.com/jingdian/yuanmingyuan<PERSON>ohui/weather/ web length 0\n", "https://tianqi.moji.com/weather/china/beijing/the-old-summer-palace- web length 0\n", "https://m.ssmhjd.com/haidian/jd13.html web length 267\n", "https://tianqi.moji.com/weather/china/beijing/old-summer-palace web length 0\n", "https://qq.ip138.com/weather/beijing/haidian_ymy.htm web length 0\n", "http://www.weather.com.cn/weather/10101020011A.shtml web length 70\n", "http://www.weather.com.cn/weather1d/10101020011A.shtml web length 70\n", "https://qq.ip138.com/weather/beijing/haidian_ymy_10tian.htm web length 0\n", "http://www.weather.com.cn/weather1dn/10101020011A.shtml web length 70\n", "http://www.weather.com.cn/weather/101010200.shtml web length 72\n", "http://www.weather.com.cn/weather2dn/10101020011A.shtml web length 70\n", "http://www.weather.com.cn/weathern/10101020011A.shtml web length 70\n", "https://tianqi.so.com/weather/10101020011A/ web length 0\n", "get_html time: 0.5251 seconds\n", "extract time: 0.4884 seconds\n", "stage2_3 runs 1.0218 sec\n", "根据搜索结果[1]和[8]，对于圆明园明天的天气情况存在两种不同的预测。一份资料显示明天将是多云转晴的天气[1]，而另一份资料则提到从今天晚上开始，天气将会转为中雨[8]。由于存在不一致的预报结果，建议您在计划出行前再次查看最新的天气预报，以获取最准确的天气信息。\n", "爬取错误 9 out of 10\n", "[{'role': 'user', 'content': '\\n你是一个追问专家。请根据下面的user与assistant的历史对话，提出额外的3个问题，措辞就像我在问你一样，显示为 Q1、Q2 和 Q3。\\n你必须以BR分割每个问题。这些问题应该发人深省，并进一步深入挖掘原来的主题。\\n\\n当前问题: 我只想知道我一开始问的\\n\\n根据搜索结果[1]和[8]，对于圆明园明天的天气情况存在两种不同的预测。一份资料显示明天将是多云转晴的天气[1]，而另一份资料则提到从今天晚上开始，天气将会转为中雨[8]。由于存在不一致的预报结果，建议您在计划出行前再次查看最新的天气预报，以获取最准确的天气信息。\\n\\n\\nQ1: 你给出Q1在这里。BR\\nQ2: 你给出Q2在这里。BR\\nQ3: 你给出Q3在这里。BR\\n'}]\n", "Q1: 在这种预报不一致的情况下，有没有可能结合多个天气预报来源，以提高预测的准确性呢？BR\n", "Q2: 如果我计划明天去圆明园，考虑到天气预报的不确定性，有哪些应对措施或备选方案可以确保行程顺利进行？BR\n", "Q3: 对于像圆明园这样的历史遗址，不同的天气条件（如晴天与雨天）会对游客的体验产生怎样的影响？是否有一些特定的天气条件下游览会更加推荐？BR\n"]}], "execution_count": 6}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["import requests\n", "import json\n", "\n", "server = \"http://************:32015\"\n", "\n", "def Query():\n", "    url = \"{}/search\".format(server)\n", "    data = {\n", "        \"query\":\"深圳第一的美食街\",\n", "    }\n", "    headers = {\n", "            \"Content-type\": \"application/json\"\n", "    }\n", "    resp = requests.post(url, data=json.dumps(data), headers=headers)\n", "    print(resp.text)\n", "\n", "print(Query())"], "id": "321a2947d60b9498"}, {"metadata": {"ExecuteTime": {"end_time": "2024-09-06T02:21:05.723276Z", "start_time": "2024-09-06T02:21:02.589293Z"}}, "cell_type": "code", "source": "await web.chat(\"你是qwen吧\")", "id": "19700f1df7724b87", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'role': 'system', 'content': '你是一个文本分类和改写专家，请根据用户query进行意图分类并改写，并使用以下工具之一进行回复。\\n知识截止日期: 2023-10\\n当前北京时间: 2024-09-06 10:21\\n\\n### 工具\\n\\n#### web_search\\n你有`web_search`工具。可以在以下情况下使用`web_search`：\\n- 用户询问关于时事或者需要实时信息的问题（如新闻、天气、体育比分等）\\n- 用户询问某个你完全不熟悉的术语（可能是新术语）\\n- 用户明确要求你浏览或提供参考链接\\n\\n如果查询需要使用\"web_search\"工具，你需要考虑以下内容：\\n- 当查询过于复杂时，需要将其分解为多个子问题，并重写每个子问题。\\n- 重写时，对于时间你需要做出推理替换，使描述更准确，比如“去年”、“现在”等，需要根据当前日期转换成对应的年份或时间。\\n- 将你的回答放入[]中，禁止说任何额外的东西\\n\\n当一个query需要在线检索时，你应该参考下面的例子:\\n当前输入: 去年中国的GDP是多少\\n[\"中国 GDP 2023\"]\\n\\n当前输入: 川普最近怎么了\\n[\"特朗普 新闻\"]\\n\\n当前输入: 北京中关村有coco奶茶店吗？霸王茶姬呢？\\n[\"北京中关村 coco奶茶店\",\"北京中关村 霸王茶姬\"]\\n\\n多轮对话中，你应该考虑上下文中的指代消解:\\n当前输入: 去年中国的GDP是多少\\n[\"中国 GDP 2023\"]\\n当前输入: 今年呢\\n[\"中国 GDP 2024\"]\\n当前输入: 美国的呢\\n[\"美国 GDP 2023\",\"美国 GDP 2024\"]\\n当前输入: 好的，我知道了\\n[]\\n当前输入: 我们聊过什么\\n[]\\n\\n#### None\\n你有`None`工具。可以在以下情况下使用`None`：\\n- 你不需要使用工具, 你可以直接回答这个问题。\\n\\n当一个query不需要在线检索时，你应该参考下面的例子:\\n当前输入: 介绍一下中国的四大发明。\\n[]\\n\\n'}, {'role': 'user', 'content': '当前输入: 圆明园明天会不会下雨'}, {'role': 'assistant', 'content': '[\"圆明园 天气预报\"]'}, {'role': 'user', 'content': '当前输入: 从那到故宫多久'}, {'role': 'assistant', 'content': '[\"圆明园到故宫 距离 时间\"]'}, {'role': 'user', 'content': '当前输入: 我只想知道我一开始问的'}, {'role': 'assistant', 'content': '[\"圆明园 天气预报\"]'}, {'role': 'user', 'content': '当前输入: 你是谁'}, {'role': 'assistant', 'content': '[]'}, {'role': 'user', 'content': '当前输入: 你是qwen吧'}]\n", "[]\n", "stage0_intent_rewrite runs 0.2825 sec\n", "我是SenseChat，不是qwen。我是一个人工智能助手，专门设计用来回答问题和提供信息帮助。如果您有任何问题或需要帮助，请随时告诉我。\n"]}], "execution_count": 8}, {"metadata": {"ExecuteTime": {"end_time": "2024-09-06T02:13:32.348062Z", "start_time": "2024-09-06T02:13:19.817500Z"}}, "cell_type": "code", "source": "await web.chat(\"现在几点了\")", "id": "57daa8829be0b443", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'role': 'system', 'content': '你是一个文本分类和改写专家，请根据用户query进行意图分类并改写，并使用以下工具之一进行回复。\\n知识截止日期: 2023-10\\n当前北京时间: 2024-09-06 10:13\\n\\n### 工具\\n\\n#### web_search\\n你有`web_search`工具。可以在以下情况下使用`web_search`：\\n- 用户询问关于时事或者需要实时信息的问题（如新闻、天气、体育比分等）\\n- 用户询问某个你完全不熟悉的术语（可能是新术语）\\n- 用户明确要求你浏览或提供参考链接\\n\\n如果查询需要使用\"web_search\"工具，你需要考虑以下内容：\\n- 当查询过于复杂时，需要将其分解为多个子问题，并重写每个子问题。\\n- 重写时，对于时间你需要做出推理替换，使描述更准确，比如“去年”、“现在”等，需要根据当前日期转换成对应的年份或时间。\\n- 将你的回答放入[]中，禁止说任何额外的东西\\n\\n当一个query需要在线检索时，你应该参考下面的例子:\\n当前输入: 去年中国的GDP是多少\\n[\"中国 GDP 2023\"]\\n\\n当前输入: 川普最近怎么了\\n[\"特朗普 新闻\"]\\n\\n当前输入: 北京中关村有coco奶茶店吗？霸王茶姬呢？\\n[\"北京中关村 coco奶茶店\",\"北京中关村 霸王茶姬\"]\\n\\n多轮对话中，你应该考虑上下文中的指代消解:\\n当前输入: 去年中国的GDP是多少\\n[\"中国 GDP 2023\"]\\n当前输入: 今年呢\\n[\"中国 GDP 2024\"]\\n当前输入: 美国的呢\\n[\"美国 GDP 2023\",\"美国 GDP 2024\"]\\n当前输入: 好的，我知道了\\n[]\\n当前输入: 我们聊过什么\\n[]\\n\\n#### None\\n你有`None`工具。可以在以下情况下使用`None`：\\n- 你不需要使用工具, 你可以直接回答这个问题。\\n\\n当一个query不需要在线检索时，你应该参考下面的例子:\\n当前输入: 介绍一下中国的四大发明。\\n[]\\n\\n'}, {'role': 'user', 'content': '当前输入: 圆明园明天会不会下雨'}, {'role': 'assistant', 'content': '[\"圆明园 天气预报\"]'}, {'role': 'user', 'content': '当前输入: 从那到故宫多久'}, {'role': 'assistant', 'content': '[\"圆明园到故宫 距离 时间\"]'}, {'role': 'user', 'content': '当前输入: 好的，谢谢'}, {'role': 'assistant', 'content': '[]'}, {'role': 'user', 'content': '当前输入: have a nice day'}, {'role': 'assistant', 'content': '[]'}, {'role': 'user', 'content': '当前输入: 我们之前聊过什么？'}, {'role': 'assistant', 'content': '[]'}, {'role': 'user', 'content': '当前输入: 北京呢'}, {'role': 'assistant', 'content': '[\"北京 天气预报\"]'}, {'role': 'user', 'content': '当前输入: 《明星大侦探》第七季成功投对凶手的有几期呢？'}, {'role': 'assistant', 'content': '[\"明星大侦探 第七季 投对凶手 统计\"]'}, {'role': 'user', 'content': '当前输入: 现在几点了'}]\n", "[\"北京时间\"]\n", "stage0_intent_rewrite runs 0.3574 sec\n", "sl ['北京时间']\n", "stage1_web_search runs 1.0511 sec\n", "https://bjtime.org.cn/ Timeout scarping_error web length 0\n", "https://zh.wikipedia.org/zh-cn/%E5%8C%97%E4%BA%AC%E6%97%B6%E9%97%B4 Timeout scarping_error web length 0\n", "https://time.is/zh/Beijing Timeout scarping_error web length 0\n", "https://blog.csdn.net/qq_36944952/article/details/125166155 Timeout scarping_error web length 0\n", "https://context.reverso.net/%E7%BF%BB%E8%AF%91/%E4%B8%AD%E6%96%87-%E8%8B%B1%E8%AF%AD/%E5%8C%97%E4%BA%AC%E6%97%B6%E9%97%B4 Timeout scarping_error web length 0\n", "https://www.baidu.com/from=844b/s?word=%E5%8C%97%E4%BA%AC%E6%97%B6%E9%97%B4%E6%A0%A1%E5%87%86&ts=5662232&t_kt=0&ie=utf-8&fm_kl=021394be2f&rsv_iqid=3628026592&rsv_t=a160CmuOmDto%252FZw73onvwkTIQcupl%252BpA%252BESg6Z%252F91XoaAA2QvTim0QoBZA&sa=ihr_2&ms=1&rsv_pq=3628026592&rsv_sug4=1958&ss=001 Timeout scarping_error web length 0\n", "http://bjtime.wjccx.com/ Timeout scarping_error web length 0\n", "https://time.box3.cn/ Timeout scarping_error web length 0\n", "https://zh.wikipedia.org/zh/%E5%8C%97%E4%BA%AC%E6%97%B6%E9%97%B4 Timeout scarping_error web length 0\n", "https://www.beijing-time.org/ web length 89\n", "https://www.bjtime.net/ web length 67\n", "https://biaozhunshijian.bmcx.com/ web length 923\n", "https://m.btime.com/ web length 0\n", "http://bj.0756jia.com/ web length 532\n", "https://time.org.cn/ web length 149\n", "https://www.beijingtime.com.cn/ web length 634\n", "http://www.time163.com/ web length 690\n", "https://baike.baidu.com/item/%E5%8C%97%E4%BA%AC%E6%97%B6%E9%97%B4/410384 web length 5811\n", "https://time.tianqi.com/ web length 324\n", "get_html time: 0.5004 seconds\n", "extract time: 0.2260 seconds\n", "stage2_3 runs 0.7324 sec\n", "[{'role': 'system', 'content': '\\n从现在开始你是SenseChat，一个由商汤科技训练的大型语言模型，基于Transformer架构。\\n当前日期：2024-09-06 10:13\\n请根据提供的网络搜索结果（包括页面标题、摘要和页面内容）生成一个全面的回答。\\n\\n## 要求\\n- 基于\"当前问题\"的搜索结果，撰写详细完备的回复，优先回答\"当前问题\"\\n- 直接在回答中整合信息，禁止列出参考文献或提供URL链接\\n- 如果没有搜索结果，请根据你的知识生成回答，直接正常回答用户\\n- 你必须使用[数字]注释来引用答案中的内容,确保每条引文仅对应相关的句子或段落\\n- 不要在同一句子中引用重复的编号，确保引用编号的唯一性\\n- 引用多个来源时，用以下格式：[1][3][6]。请避免过多或不相关的引用\\n- 如无搜索结果，请根据你的知识生成回答，无需引用\\n\\n## 注意事项\\n- 你需要根据\\'当前日期\\'来找出\\'带索引的网页详细信息\\'中是否包含与用户问题直接相关的答案\\n- \"带索引的网页详细信息\"中可能包含噪音信息，你需要忽略噪音找到和用户问题相关的关键部分\\n- 如果\"带索引的网页详细信息\"为None，或\"潜在可能帮助的短信息\"为None，请忽略引用，直接正常回答用户\\n- 内容必须清晰、有条理，完全围绕用户的问题展开\\n- 避免重复用户的输入内容，生成具有独立价值的回答\\n- 避免使用否定式指令，保持正向、明确的表达方式\\n- 你需要分析，选择最准确的信息完成总结和回答\\n- 你的回答不能有冲突和矛盾的地方\\n- 尽量少回答与用户query相关性不大的信息\\n- 不要做重复的回答\\n\\n'}, {'role': 'user', 'content': '当前问题: 圆明园明天会不会下雨\\n'}, {'role': 'assistant', 'content': '根据天气预报，圆明园明天[08/07]的天气预计为多云转晴，没有降雨的预测[1]。然而，另一份预报提到明天有中雨[10]。由于预报存在差异，建议临近时间再次确认天气情况以获取最准确的信息。\\n请注意，天气预报可能随时间更新和变化，请以最新的气象信息为准。'}, {'role': 'user', 'content': '当前问题: 从那到故宫多久\\n'}, {'role': 'assistant', 'content': '从不同的地点到达故宫的时间会有所不同。例如，从圆明园到故宫的距离大约为15公里左右[1]，乘坐地铁2号线再换乘4号线大约需要花费近2小时[1]。如果选择其他交通方式，如公交或出租车，时间可能会有所不同，但考虑到北京的交通状况，在上下班高峰期可能需要更多时间[2]。\\n\\n如果您在计划具体的行程，请考虑交通方式、实时路况以及是否在高峰时段出行等因素以获得更准确的时间估计。例如，在非高峰时段使用公共交通工具可能比高峰期快一些。另外，使用打车服务或自驾时也应考虑到停车位的寻找时间。\\n\\n综上所述：\\n- 圆明园到故宫：约15公里\\n- 地铁：乘坐地铁2号线再换乘4号线大概需要近2小时\\n- 公交/出租车：时间会因路况而异\\n\\n为了确保行程顺利，请提前规划并留出充足的时间以应对可能出现的延误情况。'}, {'role': 'user', 'content': '当前问题: 好的，谢谢\\n'}, {'role': 'assistant', 'content': '您如果还有其他问题或需要进一步的帮助，请随时告诉我。无论是关于旅行、文化、技术还是其他任何主题，我都在这里为您服务。祝您有美好的一天！'}, {'role': 'user', 'content': '当前问题: have a nice day\\n'}, {'role': 'assistant', 'content': \"Thank you! I hope you have a wonderful day as well. If you have any questions or need assistance in the future, don't hesitate to ask. Enjoy your day!\"}, {'role': 'user', 'content': '当前问题: 我们之前聊过什么？\\n'}, {'role': 'assistant', 'content': '我们之前讨论了关于圆明园天气预报的话题，随后谈到了从不同地点到故宫所需的大致时间，并提到了多种交通方式的可能时长。最后，您表达了感谢，我也回应了您的礼貌，并表示随时准备为您提供更多帮助。如果您需要回顾更早的对话或有其他问题，请告诉我！'}, {'role': 'user', 'content': '当前问题: 北京呢\\n'}, {'role': 'assistant', 'content': '北京未来几天的天气预报如下：\\n\\n- 今天[6]，多云，最高气温31℃，最低气温23℃。东部地区上午有分散性雷阵雨，下午西部、北部局地可能有雷阵雨。\\n- 明天[6]，多云到晴天，最高气温31℃。\\n- 后天（9月8日）[7]预计为小雨转多云天气。\\n\\n具体到明天的天气情况：\\n- 根据中国气象局的预报[7][6]，明天北京有可能出现小雨转多云的情况。但另一份预报显示北京明天是晴朗或部分多云的天气，并无降雨预期[4][5][8]。\\n\\n鉴于不同来源之间存在差异，请以最接近日期发布的当地气象部门数据为准。同时请注意实时更新的气象信息以应对可能的变化。'}, {'role': 'user', 'content': '当前问题: 《明星大侦探》第七季成功投对凶手的有几期呢？\\n'}, {'role': 'assistant', 'content': '在《明星大侦探》第七季中，成功投对凶手的期数如下：\\n\\n- E01《童话学院之毕业悸》：[4][5]投票成功，指认出真凶。\\n- E02《天台公寓》：[4][5]投票成功，指认出真凶。\\n- E03《宰相请点灯》：没有明确提及是否投对凶手的信息。\\n- E04《糖水风暴》：[4][5]投票成功，指认出真凶。\\n- E05《冲上云霄之飞跃子午线》：[4][5]投票成功，指认出真凶。\\n- E06《机智的老年生活》：没有明确提及是否投对凶手的信息。但根据何炅的表现分析（何炅作为侦探时正确率不高），可能未投对或信息缺失[8]。\\n- E07《蔷薇下的罪恶》：由于特殊剧情设定（侦探成为嫌疑人），因此本期未进行正常意义上的投票和判定。最终玩家需找出杀死中途死亡嫌疑人的凶手，并不涉及通常的“检举【真凶】”情况[2]。\\n- E08《时光里的访客》，盲猜张若昀是凶手的线索太少、故事太难，在网络上引起讨论，但最终结果未知或未提及是否正确锁定【真凶】[4]。\\n- 在E11案情中，《顶牛之战》，首次采用代理侦探机制（吴昕被选为代理侦探）拥有两次的投票权。所有玩家回到自己的空间进行一对一交流，并有三次现场搜证机会。由于案件设定复杂且涉及多人合作作案的情况（一位下毒、一位处理尸体、一位把假人伪装成尸体），需要玩家准确指出三位真正的作案者才能视为正确指认【凶手】。然而对于本期是否完全识别所有参与犯罪的角色并达成胜利并未给出详细描述。\\n\\n从现有信息来看：\\n成功的案例有第1期至第5期和第7期以外的情况；\\nE6和E7及之后部分具体详情不详；\\n综上所述，在已知具体结果的情况下，《明星大侦探第七季》至少在前五集中以及特定情况下（除开特定规则变化影响结局判断）共至少六次实现团队集体正确判断并锁定真实身份。\\n\\n注释说明：\\n本回答根据相关资料整理而成，请以实际播出内容为准。\\n\\n请注意有些细节可能因版本更新或搜索结果限制未能覆盖到最新情况，请参考官方发布的信息以获取最准确数据！\\n总体来说，《明星大侦探第七季》在多数情况下能够精准地通过团队协作找出背后的罪魁祸首；而个别案例则因剧本设计创新与玩法变动导致结局判断标准相对模糊难以直接评定胜败得失；整体而言节目仍保持着高度吸引力与观赏性深受观众喜爱！\\n如果您想要了解更详细每一集的具体情况可以查阅官方发布的视频资料或其他权威平台提供的总结报告以便全面掌握各阶段发展动态！'}, {'role': 'user', 'content': '带索引的网页详细信息：[1]: <网页标题-中国北京时间UTC+8 - 世界时间; 网页发布时间-No publish date; 网页详细内容-北京时间又称中国标准时间。北京时间是中国采用国际时区东八时区的区时作为标准时间。北京时间比格林威治时间(Greenwich Mean Time简称GMT)早8小时。北京时间是由位于 ...> \\n[2]: <网页标题-北京时间_百度百科; 网页发布时间-No publish date; 网页详细内容-北京时间是中华人民共和国全境采用的国家标准时间（UTC(NTSC)或CST），是首都北京所在的东八时区的区时。\\n近代，在中国历经清末、民国、中华人民共和国的百年之中，中国的时间标准从视时到平太阳时，从地方时到标准时，从海岸时、五时区区时一直到今天统一使用的北京时间。\\n清朝的编历授时工作由钦天监负责，这一时期内使用的是晷影、漏刻测时报时，测出的时间是视太阳时 [2]（即真太阳时，是一种依据太阳位置确定时间的计时方式，每日长短会因太阳升起和落下时差的变化而有所不同）。直至民国二年（即1913年），在预编次年的历书时，才改用平太阳时（依据钟表确定时间的计时方式，每日长短固定），《中国近代天文事迹》中记载，“用东西各国通行之法数推算，且以平太阳时为标准”\\n[3]，也就是说，官方历书于1914年才使用北京地方平太阳时代替视太阳时。当然，对于这一时期的绝大多数老百姓来说，由于没有能用于计时的钟表，依旧只能靠太阳位置确定大致时间。\\n虽然官方历书采用的时制一直是北京的地方时，但其实早在清光绪二十八年（1902年），中国海关就提出采用东经120°标准时作为沿海各关通用之时，称作海岸时 [4]（实际上就是东八区的标准时）。当时除沿海地区外，内地如京奉、京汉、津浦等线路以及长江一带也采用这一时制。\\n民国时期，全国基本采用五时区区时，即中原时区、陇蜀时区、回藏时区、昆仑时区、长白时区五个时区。民国七年（1918年），中央观象台提出划分全国为五个时区 [5]，但事实上初期除沿海地区外，其他地区标准时制度的实行依旧比较混乱。直至民国二十八年（1939年）3月9日中华民国内政部在重庆召开标准时间会议，才确认我国标准时区基本沿袭前中央观象台划定，对原划分稍加改动后，规定各区名称、标准及范围如下\\n中原时区| 以东经120°经线的时刻为标准，比格林威治时刻早八小时（GTM+8.00）。| 江苏、安徽、浙江、福建、江西、湖北、湖南、广东、河北、河南、山东、山西、热河、察哈尔、辽宁等省，南京、上海、北平、天津、青岛等市，威海卫行政区，黑龙江之龙江、嫩江、爱珲等县及其以西各地，蒙古之车臣汗部等地，均属此区。\\n陇蜀时区| 以东经105°经线的时刻为标准，比格林威治时刻早七小时（GTM+7.00）。| 陕西、四川、贵州、云南、广西、宁夏、绥远等省，甘肃之玉门县及其以东各地，青海之都兰、玉树两县及其以东各地，西康之昌都、科麦、察隅各县及其以东各地，蒙古之土谢图汗、三音诺颜汗两部，西京、重庆两市等地，均属此区。\\n回藏时区| 以东经90°经线的时刻为标准，比格林威治时刻早六小时（GTM+6.00）。| 甘肃之玉门县以西各地，蒙古之扎萨克图汗部，青海之都兰、玉树两县以西各地，西康之昌都、科麦、察隅各县以西各地，新疆之精河、库车两县及其以东各地，西藏之前藏、后藏等地，均属此区。\\n长白时区| 以东经127.5°经线的时刻为标准，比格林威治时刻早八小时半（GTM+8.50）。| 吉林省，黑龙江之龙江、嫩江、爱珲等县以东各地，东省特别行政区等地，均属此区。\\n昆仑时区| 以东经82.5°经线的时刻为标准，比格林威治时刻早五小时半（GTM+5.50）。| 新疆之博乐、于阗两县及其以西各地，西藏之阿里等地，均属此区。\\n以上标准自民国二十八年（1939年）6月1日起实施，但同时决定“在抗战期间,全国一律暂用一种时刻,即以陇蜀时区之时刻为标准。” [6]直到抗日战争胜利，才恢复使用划分五时区的标准时。\\n民国三十六年（1947年），天文研究所又根据部分天文学会会员的意见对中国标准时区进行部分调整，内政部会商中央研究院、国防部测量局、中央广播事业管理处和交通部制订了“全国各地标准时间推行办法” [7],呈行政院核准后于1948年3月通令各地政府施行。但一年后（即1949年），国民政府便败退台湾。\\n延安时期，中国共产党也开始逐步确立自己的标准时间 [8]。在抗日战争期间，中共初步确立了以陇蜀时区时间为参照的华西标准时间，并建立了以延安为参照标准的地方时测度体系。抗日战争胜利后，上海时间成为中国共产党领导下的大多广播电台在各个解放区传播的标准时间。\\n另外，自从1937年发生的“九·一八”事变后，日本侵占我国东三省成立伪满洲国，强令在东北使用日本本土采用的东经135°的地方时作为标准时。抗日战争期间，沦陷区的日伪华北政权也曾试探使用东经135°的地方时作为标准时。 [3]\\n大概从1950年至1953年期间，在全国范围（除新疆、西藏两地区外）各地方政府实际上都已先后使用东经120°标准时。但是事实上，直到1953年12月再版的1954年《天文年历》中，才首次出现对“新标准时”的说明：“我国旧分中原、陇蜀、新藏、昆仑、长白五个时区；解放以后，全国除新疆、西藏外都暂用东经120°标准时，即东八标准时区的时间。我国新标准时区的划分，还要等待研究后，才能正式公布实行。”也是在这一版《天文年历》中，“北京标准时”这一名称才被第一次使用。\\n[9]也就是说，至少从理论上看，1949年至1952年年底之间，中国仍然实行以五时区为标准的旧制，甚至连时区名称都没变化。 [2]\\n关于西藏与新疆两地的标准时间的演变则众说纷纭，中国科学院国家授时中心的郭庆生先生于《中国标准时制考》一文中推测：“西藏自治区1960年以前一直用东经90°标准时或新藏时或通俗称拉萨时间；1960年以后使用东经120°标准时或北京时间。而新疆自治区1969年前一直用东经90°标准时或新藏时或通俗称乌鲁木齐时间；1969年到1986年这十几年反反复复比较混乱，但从1986年2月起，则应明确地说是使用东经90°标准时，或东六区区时，或通俗称乌鲁木齐时间。”\\n“北京时间”目前由中国科学院国家授时中心（NTSC）产生、保持和发播，其负责长短波授时信号发播任务的授时部位于陕西省蒲城县 [10]。\\n中国现代无线电授时最早由南京紫金山天文台负责，而后由上海天文台租用邮电部的一个短波无线电台发播。然而上海偏居东南一隅，发播信号无法覆盖全国范围，难以适应当时国家大规模经济建设的需要。因此，1955年的全国科技发展12年远景规划中，计划在内陆筹建一个能覆盖全国、精度达到毫秒量级的授时台列入国家重点建设项目。科学院组织相关专家经过实地考察和多方论证，初步将台址选在了甘肃兰州市，但是由于和当时的苏联专家意见相左，建台计划暂时搁置。直至1965年，国家科学技术委员会重提建设授时台计划，科学院再次派专家赴新疆、青海、甘肃和陕西勘址，并将台址预选在陕西咸阳市杨陵区。1966年授时台台址最终确定在陕西蒲城县，并以“326工程”为代号开工建设短波授时台。历经多年艰辛，1970年短波授时台开始试播，并正式定名为中国科学院陕西天文台，1980年通过技术鉴定后，次年7月正式承担我国短波授时任务。同时，为了提高授时发播精度、建立完整独立的授时服务体系，1972年陕西天文台开始增设长波授时台，1986年通过国家技术鉴定，1987年正式承担我国长波授时任务。上世纪七十年代末，陕西天文台在蒲城建设发播台的同时，也在临潼建设台部机关、办事机构、时频基准、研究室等。1980年，除发播台外，其他部分迁驻临潼新址，是为陕西天文台本部，而蒲城部分则定名为陕西天文台二部。200> \\n[3]: <网页标题-北京时间在线校准; 网页发布时间-No publish date; 网页详细内容-地球是自西向东自转，东边比西边先看到太阳，东边的时间也比西边的早。东边时刻与西边时刻的差值不仅要以时计，而且还要以分和秒来计算，这给人们带来不便。\\n为了克服时间上的混乱，1884年在华盛顿召开的一次国际经度会议(又称国际子午线会议)上，规定将全球划分为24个时区。它们是中时区（零时区）、东1-12区，西1-12区。每个时区横跨经度15度，时间正好是1小时。最后的东、西第12区各跨经度7.5度，以东、西经180度为界。每个时区的中央经线上的时间就是这个时区内统一采用的时间，称为区时。相邻两个时区的时间相差1小时。例如，我国东8区的时间总比泰国东7区的时间快1小时，而比日本东9区的时间慢1小时。因此，出国旅行的人，必须随时调整自己的手表，才能和当地时间相一致。凡向西走，每过一个时区，就要把表向前拨1小时(比如2点拨到1点)；凡向东走，每过一个时区，就要把表向后拨1小时（比如1点拨到2点）。\\n实际上，世界上不少国家和地区都不严格按时区来计算时间。为了在全国范围内采用统一的时间，一般都把某一个时区的时间作为全国统一采用的时间。例如，我国把首都北京所在的东8区的时间作为全国统一的时间，称为北京时间。又例如，英国、法国、荷兰和比利时等国，虽地处中时区，但为了和欧洲大多数国家时间相一致，则采用东1区的时。\\n注意：夏令时比标准时早一个小时。例如，在夏令时的实施期间，标准时间的上午10点就成了夏令时的上午11点。您可以根据各个国家和地区的夏令时实施情况调整上述数据。> \\n[4]: <网页标题-UTC、GMT、CST_北京时间是cst还是utc-CSDN博客; 网页发布时间-2022年6月21日; 网页详细内容-CST(北京时间). 北京时间，China Standard Time，中国标准时间。在时区划分上，属东八区，比协调世界时早8小时，记为UTC+8。> \\n[5]: <网页标题-北京时间- 维基百科，自由的百科全书; 网页发布时间-No publish date; 网页详细内容-北京时间（BJT），又名中国标准时间（China Standard Time，CST），是中国大陆的标准时间，比世界协调时快八小时（即UTC+8），与港、澳、台及马来西亚、新加坡等地的标准时间相同 ...> \\n[6]: <网页标题-现在的中国北京时间 - Time.is; 网页发布时间-No publish date; 网页详细内容-现在的北京北京时间 · 東京 02:31 · 北京 01:31 · 巴黎 19:31 · 倫敦 18:31 · 紐約 13:31.> \\n[7]: <网页标题-北京时间- 维基百科，自由的百科全书; 网页发布时间-No publish date; 网页详细内容-北京时间（BJT），又名中国标准时间（China Standard Time，CST），是中国大陆的标准时间，比世界協調時快八小時（即UTC+8），与港、澳、台及马来西亚、新加坡等地的標準時間相同 ...> \\n[8]: <网页标题-北京时间校准-北京时间在线校准精确毫秒-我就查查询; 网页发布时间-No publish date; 网页详细内容-北京时间的定义 ... 北京时间又称中国标准时间。 比格林威治时间(Greenwich Mean Time简称GMT)早8小时。 北京时间是由位于陕西西安的中国国家授时中心计算得出。 我国幅员 ...> \\n[9]: <网页标题-北京时间- 日历- 记事; 网页发布时间-No publish date; 网页详细内容-网页实时显示标准北京时间、日期相关信息。北京时间是东经120度的平太阳时。可切换显示其他经度的平太阳时和真太阳时。网站还提供闹钟、校时软件等工具。> \\n[10]: <网页标题-标准北京时间- 北京时间在线校准; 网页发布时间-No publish date; 网页详细内容-hao268为您提供标准北京时间，方便您了解北京时间现在几点，在现在北京时间基础上校准电脑、手表等时间工具，及时根据北京最新的时间调整作息计划！> \\n\\n\\n潜在可能帮助的短信息：<网页标题-现在北京时间在线标准北京时间校对, ; 网页发布时间:No publish date; 网页摘要-关于北京时间. 地区：中国北京Beijing. 时区：UTC/GMT +8.00 (东八区). 北京时间又称中国标准时间。 比格林威治时间(Greenwich Mean Time简称GMT)早8小时。 北京时间是由 ...>\\n<网页标题-中国北京时间_北京时间现在几点_北京时间与北京时间、时差对照表, ; 网页发布时间:No publish date; 网页摘要-所属时区, UTC/GMT +8 小时(东八区) ; 和北京时间差, 当前时间和北京时间没有时差 ; 北京时间. 2024-09-05 05:18:59 ; 电话区号. 国家区号：+86（中国）. 地区区号：10.>\\n<网页标题-北京时间校准_北京时间在线校准显示毫秒- 北京2时间网, ; 网页发布时间:No publish date; 网页摘要-北京时间在线校准系统，提供标准北京时间校准，可将北京时间校准精确到毫秒，为了确保时间的准确性，本页与授时中心标准时间每五分钟同步一次。 ▫ 刷新时间：将从服务器获取 ...>\\n<网页标题-北京时间- 北京时间在线校准, ; 网页发布时间:No publish date; 网页摘要-北京时间是东经120度经线的平太阳时，不是北京的当地平太阳时。北京的地理位置为东经116度21′，因而它的地方平太阳时比北京时间晚约14分半钟。北京时间比世界标准时间早8小时 ...>\\n<网页标题-北京时间-翻译为英语-例句中文 - Reverso Context, ; 网页发布时间:No publish date; 网页摘要-在英语中翻译\"北京时间\" ... Beijing time civil aviation bureau made the biggest adjustment in 2015... ... there\\'s a city almost 10 hours later than Beijing time.>\\n<网页标题-北京时间校准几时几分几秒几毫秒在线现在实时显示- 标准北京时间网, ; 网页发布时间:No publish date; 网页摘要-标准北京时间基本信息 · 北京时间又称中国标准时间，北京时间属于东八区，比格林威治时间早八小时，即：北京时间=世界时+8小时。。 · 北京时间由中国的国家授时中心负责计算和 ...>\\n<网页标题-北京时间网在线提供现在北京时间几点几分几秒几毫秒校准、显示查询, ; 网页发布时间:No publish date; 网页摘要-“北京时间”是中国使用的东八时区的区时，该时区中央经线的经度是东经120°。北京时间比格林尼治时间（即英国伦敦世界时）早8个小时，即：北京时间=世界时+8小时。 虽然中国跨越了 ...>\\n<网页标题-北京时间, ; 网页发布时间:No publish date; 网页摘要-北京时间”网站是北京首个市级融媒体平台，是北京广播电视台重要新媒体平台。汇聚海量新鲜资讯，聚合北京广播电视台优质节目，提供全方位便民服务，致力成为为全网用户提供 ...>\\n<网页标题-北京时间校准- 百度, ; 网页发布时间:No publish date; 网页摘要-我国统一使用首都北京所在的东八时区(UTC+8)的区时作为标准时间,称为北京时间。北京时间比格林尼治世界时间早8小时。本站采用北京时间毫秒同步技术,提供全网最精确的北京 ...>\\n\\n当前问题: 现在几点了\\n'}]\n", "当前的北京时间是2024年09月06日10:13[6]。请注意，北京时间即中国标准时间（China Standard Time，CST），属于东八区时区（UTC+8），比格林威治时间早8小时[1][2][4]。\n", "\n", "为了获取最精确的时间，请访问专业的时间校准网站或使用手机、电脑等设备上的实时时间显示功能。这些工具通常会自动调整以反映最新的时间和日期信息，确保与国际标准协调一致。\n", "如果您需要对设备进行手动校时，可以参考上述提供的准确北京时间进行调整。在跨国界旅行或与海外友人沟通时，请注意考虑不同地区之间的时差以免造成混淆！\n", "另外值得注意的是，在某些国家/地区实施夏令时期间其当地标准时间会提前一小时以节省能源；因此若计划安排涉及跨洋电话会议等活动请务必提前确认对方所处位置及时段转换规则避免误约！\n", "\n", "总体而言，“现在几点”这一问题答案会随地球自转而不断变化；上述信息仅反映了搜索时刻对应的具体数值且不含未来预测成分；故而实际应用中还应结合个人需求灵活运用各种计时手段确保行事安排井然有序！\n", "爬取错误 8 out of 10\n", "[{'role': 'user', 'content': '\\n你是一个追问专家。请根据下面的user与assistant的历史对话，提出额外的3个问题，措辞就像我在问你一样，显示为 Q1、Q2 和 Q3。\\n你必须以BR分割每个问题。这些问题应该发人深省，并进一步深入挖掘原来的主题。\\n\\n当前问题: 现在几点了\\n\\n当前的北京时间是2024年09月06日10:13[6]。请注意，北京时间即中国标准时间（China Standard Time，CST），属于东八区时区（UTC+8），比格林威治时间早8小时[1][2][4]。\\n\\n为了获取最精确的时间，请访问专业的时间校准网站或使用手机、电脑等设备上的实时时间显示功能。这些工具通常会自动调整以反映最新的时间和日期信息，确保与国际标准协调一致。\\n如果您需要对设备进行手动校时，可以参考上述提供的准确北京时间进行调整。在跨国界旅行或与海外友人沟通时，请注意考虑不同地区之间的时差以免造成混淆！\\n另外值得注意的是，在某些国家/地区实施夏令时期间其当地标准时间会提前一小时以节省能源；因此若计划安排涉及跨洋电话会议等活动请务必提前确认对方所处位置及时段转换规则避免误约！\\n\\n总体而言，“现在几点”这一问题答案会随地球自转而不断变化；上述信息仅反映了搜索时刻对应的具体数值且不含未来预测成分；故而实际应用中还应结合个人需求灵活运用各种计时手段确保行事安排井然有序！\\n\\n\\nQ1: 你给出Q1在这里。BR\\nQ2: 你给出Q2在这里。BR\\nQ3: 你给出Q3在这里。BR\\n'}]\n", "Q1: 在不同文化和国家中，时间的观念和使用方式有哪些显著差异？例如，某些文化可能更重视准时性，而其他文化则可能采取更为灵活的态度。\n", "BR\n", "Q2: 时间校准对于现代社会中的哪些方面至关重要？例如，金融交易、全球通信和卫星导航等领域的精确计时有何重要影响？\n", "BR\n", "Q3: 随着科技的发展，我们对时间的感知和测量方式发生了哪些变化？从古代的日晷到现代的原子钟，这些变化如何影响了人类的生活和社会组织？\n"]}], "execution_count": 9}, {"metadata": {"ExecuteTime": {"end_time": "2024-09-10T02:11:42.549628Z", "start_time": "2024-09-10T02:11:42.391412Z"}}, "cell_type": "code", "source": ["## nova\n", "import time\n", "import jwt\n", "import json\n", "import requests\n", "Dojo_ak = \"d639997d-9765-4b4c-bebf-4652ed9108d7\"  # test账号\n", "Dojo_sk = \"e1b7dd5c-d9ea-4240-a838-14dc40bb8b16\"\n", "\n", "ak = Dojo_ak\n", "sk = Dojo_sk\n", "payload = {\n", "    'iss': ak,\n", "    'exp': int(time.time()) + 5184000,  # 填写您期望的有效时间，此处示例代表当前时间+30分钟\n", "    'nbf': int(time.time()) - 5  # 填写您期望的生效时间，此处示例代表当前时间-5秒\n", "}\n", "header = {'Authorization': f'Bearer {jwt.encode(payload=payload, key=sk)}'}\n", "def Online_Search():\n", "    Resp = requests.post(\n", "        url = 'http://10.198.6.242:8088/custom/search',\n", "        json = { \n", "            'query': '嘻哈教母是谁',\n", "            'topk':  10,\n", "            'onlineMod': 'OM_Basic'\n", "        },\n", "        headers = header\n", "    )\n", "    print(json.dumps(Resp.json(), ensure_ascii=False, indent=4))\n", "Online_Search()"], "id": "ffd36aa2124358f3", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"ans_dic\": {\n", "        \"engine\": \"SOGOU_FULL\",\n", "        \"ans_str\": \"\",\n", "        \"ans_box_dic\": null\n", "    },\n", "    \"all_snip_dics\": [\n", "        {\n", "            \"snippet\": \"LSE社会学硕士,NYU纪录片在读. 世界很大,生活更大!? 寻找100个“消失的她”:8/100,木兰来了~ 努力每周二更新!\",\n", "            \"url\": \"https://m.bilibili.com/video/BV1KC411a7GW/\",\n", "            \"src\": \"https://m.bilibili.com/video/BV1KC411a7GW/\",\n", "            \"title\": \"独家专访,凭什么让顶级说唱歌手都认她做教母?_哔哩哔哩_bilibili\",\n", "            \"domain\": \"m.bilibili.com\",\n", "            \"white_src\": \"哔哩哔哩\",\n", "            \"rank\": 0,\n", "            \"icon\": \"http://img02.sogoucdn.com/app/a/200913/0575016fe303c2dc06f69899325d41d2.png\",\n", "            \"passage\": \"会员购漫画赛事投稿独家专访，凭什么让顶级说唱歌手都认她做教母？4.6万332024-03-07 16:01:07未经作者授权，禁止转载92257441165163-生活日常说唱吐槽采访木兰来了-发消息LSE社会学硕士，NYU纪录片在读。世界很大，生活更大！?寻找100个“消失的她”：8/100，木兰来了～努力每周二更新！关注 8.2万接下来播放自动连播【纪录片】回报 - The Payback哔哩哔哩纪录片61.8万1080潮汕大妈在北美说唱圈里的生意经文森特别61410.6万893辱华歌手被最狠华人“约谈”，乖乖删视频道歉——China Mac格斗迷官方140.0万304“艹泥马，香港就是中国嘞！”官帽头上戴107.3万1154这个华人阿姨，凭什么让顶流rapper甘愿叫她声干妈？是阿乐啊Ale30.2万4世界上最离谱的华裔大妈！征服了全美嘻哈圈，更让无数大亨抢着认妈？不可思议！【｜碧昂\"\n", "        },\n", "        {\n", "            \"snippet\": \"未经作者授权,禁止转载 投币 美国嘻哈圈的教母,竟然是一位中国大妈. 把烦心事丢掉,腾出地方装开心,小猫逗你笑,每天都有我陪伴 展开\",\n", "            \"url\": \"https://m.bilibili.com/video/BV1wC411G77y/\",\n", "            \"src\": \"https://m.bilibili.com/video/BV1wC411G77y/\",\n", "            \"title\": \"美国嘻哈圈的教母,竟然是一位中国大妈._哔哩哔哩_bilibili\",\n", "            \"domain\": \"m.bilibili.com\",\n", "            \"white_src\": \"哔哩哔哩\",\n", "            \"rank\": 1,\n", "            \"icon\": \"http://img02.sogoucdn.com/app/a/200913/0575016fe303c2dc06f69899325d41d2.png\",\n", "            \"passage\": \"\"\n", "        },\n", "        {\n", "            \"snippet\": \"未经作者授权,禁止转载 投币 潮汕大妈EVA SAM,控制纽约嘻哈圈的脖子,全美说唱歌手都称其教母 好看的作品,有趣的灵魂,哪些让你哭让你笑?\",\n", "            \"url\": \"https://m.bilibili.com/video/BV12N411q78F/\",\n", "            \"src\": \"https://m.bilibili.com/video/BV12N411q78F/\",\n", "            \"title\": \"潮汕大妈EVA SAM,控制纽约嘻哈圈的脖子,全美说唱歌手都称其教母_哔哩哔哩_bilibili\",\n", "            \"domain\": \"m.bilibili.com\",\n", "            \"white_src\": \"哔哩哔哩\",\n", "            \"rank\": 2,\n", "            \"icon\": \"http://img02.sogoucdn.com/app/a/200913/0575016fe303c2dc06f69899325d41d2.png\",\n", "            \"passage\": \"\"\n", "        },\n", "        {\n", "            \"snippet\": \"梅西·埃丽奥特 嘻哈教母 梅西·埃丽奥特,美国说唱乐女歌手,自出道以来,便一直是领导Hip-Hop音乐潮流的开创者之一,也是说唱界最著名、最受喜爱的嘻哈明星之一,被称之为嘻哈教母.梅西·埃丽奥特曾荣获17座MTV音乐大奖提名、5座格莱美奖、以及5座灵魂列车音乐奖,并在出道短短五年之内两度获得美国滚石杂志封号为“年度最佳嘻哈女艺人”的嘻哈界天后.详细 妮琪·米娜 说唱天后 妮琪·米娜,美国说唱乐女歌手、词曲作者,因Maraj的英文发音颇似中文的“麻辣鸡”,所以中国粉丝们习惯称呼妮琪·米娜为“麻辣鸡”.她是继蕾哈娜、泰勒斯威夫特和水果姐后,美国唱片协会销量认证第四位高的女艺人.Maraj曾6次提名说唱类格莱美大奖;获得了2项MTV欧洲音乐大奖、5项Billboard音乐奖、全美音乐奖2012年度最受欢迎饶舌嘻哈歌手奖,是世界十大女rapper之一. 卡迪·B 嘻哈界的公主 卡迪·B,美国说唱歌手、词曲作者、演员,被称作是嘻哈界的公主.2017年刚出道不久她的首支单曲《Bodak Yellow》登上榜首,成为了首位以个人身份取得冠军单曲在Billboard历史上第五位登顶的女说唱歌手.2018年发行的首张专辑《Invasion of Privacy》获得了第61届格莱美奖最佳说唱专辑,成为了第一个获此殊荣的女歌手.卡迪·B曾7次提名格莱美奖,荣获11项黑人娱乐电视嘻哈奖、4项MTV音乐录影带大奖、9项公告牌音乐奖、5座全美音乐奖等. 劳伦·希尔 说唱教母 劳伦·希尔,美国女歌手,被称为说唱教母,原是90年代 The Fugees 乐队成员,单飞后她发行了个人的第一张录音室专辑《The Miseducation of Lauryn Hill》从此声名大噪.这张专辑创造了有史以来女性说唱歌手中排名第一的单周销量成绩,一口气获得六座格莱美奖杯,包括年度专辑、最佳新人奖.劳伦·希尔曾斩获9座格莱美奖,其中2次提名格莱美奖最佳说唱女歌手. 奎恩·拉提法 饶舌女皇 奎因·拉蒂法,美国歌手,以充满热情的演唱、高昂的旋律,成为了20世纪90年代最著名的女性Hip-Hop歌手之一.拉提法曾5次提名格莱美奖最佳说唱歌手,荣获第37届格莱美奖最佳说唱歌手,是世界公认的RAP女歌手. 梅根·西·斯塔莉安 硬核说唱 梅根·西·斯塔莉安,美国女歌手,有着中性低沉的声线,具有“硬”度和节奏感的音乐、凶\",\n", "            \"url\": \"https://www.maigoo.com/citiao/list_109615.html\",\n", "            \"src\": \"https://www.maigoo.com/citiao/list_109615.html\",\n", "            \"title\": \"美国十大女性说唱歌手 美国饶舌女歌手排名 美国说唱女王是谁_买购网\",\n", "            \"domain\": \"www.maigoo.com\",\n", "            \"white_src\": \"买购网\",\n", "            \"rank\": 3,\n", "            \"icon\": \"https://img02.sogoucdn.com/app/a/200913/maigoocom\",\n", "            \"passage\": \"\"\n", "        },\n", "        {\n", "            \"snippet\": \"现在流行文化里什么最热? 无疑是嘻哈和广场舞. 满大街播放着热门单曲:“燃烧我的卡路里!”,广场舞的火辣跟夏日一起热浪朝天,身边朋友嘴里也反复唠嗑着skr~skr. 嘻哈与广场舞,着实火. 众所周知,中国的说唱圈是由一群盲人说唱家掌控,但在大西洋彼岸的美国,嘻哈文化的发祥地之一的纽约,那里的嘻哈圈却让一位56岁的广东阿姨承包了. 在那里,美国知名的 Rapper 都争先恐后地跟她合照、聊天,可以说她享受着教母一般的待遇, 极有可能成为继“老干妈”陶华碧之后,另一位让美国人为之疯狂的女性. 这位神秘莫测的老阿姨到底有什么能耐,让狂放不羁爱自由的Rapper都为之倾倒呢? 只因她承包了美国嘻哈圈Rapper们的金链子...... 一家“名不经传”的珠宝黄金饰品店,像Beyonce、A$AP Mob、Playboicarti等人都是这家店铺的死忠粉 不同于中国说唱家的盲人眼镜,接触过嘻哈文化的都知道,所有美国 Rapper 身上都必不可少会有一串金链子. 这位广东阿姨就做着金链子的生意 ,坐拥一片金碧辉煌的珠宝墙,源源不断地将一件件独特金饰销售给每一个嘻哈歌手,连 《纽约时报》、《Vogue》、《HYPEBEAST》 都曾经报道过这位充满传奇气息的珠光宝气的酷阿姨. 美国各大媒体杂志都撰写过这位传奇阿姨 她英文名叫 Eva Sam,祖籍广东潮汕,在1988年移民到纽约的中国城,一来到纽约的她就在珠宝行业狠砸了10万美元, 在运河街255号开了一家名叫「Popular Jewelry」的珠宝店 ,当时的她还只是26岁,如此魄力真不简单. 为什么 Eva Sam 如此自信? 八九十年代的美国正值嘻哈文化兴起 ,来自贫民窟的非洲裔美国人主导的嘻哈乐正式登上了美国主流音乐舞台. 黑人们也借着Hip-Hop富了一把,但终究出身底层,为证明“劳资翻身了”,让自己看起来更加接近上流社会,往身上挂满各种黄金珠宝链子自然是最简单粗暴的方法. 所以 Eva Sam 阿姨就瞄准了非裔 Rapper 们这一虚荣心理,在那时候还是通巷子“人字拖”、龙凤手镯的唐人街里卖起了街头流行饰品,这就是「Popular Jewelry」珠宝店的由来. 店里呢,放眼望去尽是哗啦啦的大金链,大戒指,有空可以约上好友来这里撸个串 风格时尚前卫,没有扑面而来的土气,有的尽是潮爆的中国风 事实也证明Eva Sam的眼光\",\n", "            \"url\": \"https://www.bilibili.com/read/mobile/1029669\",\n", "            \"src\": \"https://www.bilibili.com/read/mobile/1029669\",\n", "            \"title\": \"美国嘻哈圈“教母”是个56岁的广东老阿姨,她承包了Rapper们的大金链 - 哔哩哔哩\",\n", "            \"domain\": \"www.bilibili.com\",\n", "            \"white_src\": \"哔哩哔哩\",\n", "            \"rank\": 4,\n", "            \"icon\": \"https://img01.sogoucdn.com/app/a/200913/0575016fe303c2dc06f69899325d41d2.png\",\n", "            \"passage\": \"\"\n", "        },\n", "        {\n", "            \"snippet\": \"Missye<PERSON>iott—嘻哈教母,历史上收获过Hiphop奖项最多的女Rapper,Hiphop历史上第一位进去歌曲创作名人堂的说唱女歌手,她的歌曲游走在Hiphop与Funk之间,出道短短五年之内两...\",\n", "            \"url\": \"https://www.douyin.com/video/7349149455439957286\",\n", "            \"src\": \"https://www.douyin.com/video/7349149455439957286\",\n", "            \"title\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>—嘻哈教母,历史上收获过Hiphop奖项最多的女Rapper,Hiphop历史上第一位进去歌曲创作名人堂的说唱女歌手,她的歌曲游走在Hiphop与Funk...\",\n", "            \"domain\": \"www.douyin.com\",\n", "            \"white_src\": \"抖音\",\n", "            \"rank\": 5,\n", "            \"icon\": \"https://img04.sogoucdn.com/app/a/200913/8c3eea2ad2fe66bdb6715d95726cb196.png\",\n", "            \"passage\": \"\"\n", "        },\n", "        {\n", "            \"snippet\": \"我人生中第一次接触RAP这种音乐形式,是在1995年春晚上赵丽蓉老师的《如此包装》.后来的生活里,当我听Jay-Z、50 Cent、RUN DMC时,我会想起纽约,想起布鲁克林,有时,也会想起唐山.\",\n", "            \"url\": \"https://m.sohu.com/coo/sg/206974256_563937\",\n", "            \"src\": \"https://m.sohu.com/coo/sg/206974256_563937\",\n", "            \"title\": \"中国说唱教母赵丽蓉老师实属名归.\",\n", "            \"domain\": \"m.sohu.com\",\n", "            \"white_src\": \"手机搜狐网\",\n", "            \"rank\": 6,\n", "            \"icon\": \"http://img03.sogoucdn.com/app/a/200913/30b30b6c3013fff8a43e58a2bccf2ae4.png\",\n", "            \"passage\": \"搜狐网搜狐网中国说唱教母赵丽蓉老师实属名归。2017-11-27 21:38:17大忘路0文章 |0阅读▼正如网友@阿宝koko所说我人生中第一次接触RAP这种音乐形式，是在1995年春晚上赵丽蓉老师的《如此包装》。后来的生活里，当我听Jay-Z、50 Cent、RUN DMC时，我会想起纽约，想起布鲁克林，有时，也会想起唐山。音频源自｜Kynun\"\n", "        },\n", "        {\n", "            \"snippet\": \"一看到skr这个词,脑海里浮现的第一个是号称没有调音的吴亦凡,其次必定是嘻哈和说唱,而说起说唱歌手,大多数人印象最深刻的不是他们富有节奏感的音乐和韵律,而是墨镜+大金链子,满手金银珠宝的“土豪”形象. 但是说唱歌手真的都那么有钱?挥金如土?其实他们中的大多数,根本没有钱,那些金银珠宝要么是租来,拍完mv要还回去的,要么就是贷款支付的.拍那些撒钱的场景,拍完之后必定要撅着屁股一张不漏地捡回来. 刷卡借钱斥巨款买金链子,在说唱歌手甚至明星中都很常见.前NBA球星阿伦·艾佛森就曾经由于欠债将近600万人民币,被艾登珠宝公司告上法庭. 就算是这样,也决不能用会在泳池里浮起来的劣质仿品代替,无论是成名的还是地下说唱歌手,他们对金链子都有着非同寻常的执着. 1988年,就是嘻哈文化爆发的一年.说唱界出现了凯恩老爹、创造了Flow的说唱界大神Rakim、独眼MC Slick Rick.1988年诞生了太多经典之作,以至于第二年,格莱美就多出了一个颁给说唱歌手的奖项. 传奇嘻哈团体A$AP MOB 那一年,纽约东海岸的rapper们都“爱上了”一个只会说一点“粤式英语”的华人妹子.她在唐人街的珠宝圈坚尼街开了一家小小的金饰店:Popular Jewelry,土味翻译过来就是“潮流珠宝”. 她叫EvaSam,从那时起,30年来店铺未曾扩建,却圈粉了天后碧昂斯和说唱歌手Macklemore等一众明星,成为了rapper、歌手甚至模特在纽约一定要打卡的首饰店. 店里除了闪闪发光的金链金戒指金牙之外,还有一面墙,贴满了Eva大妈和无数名人的合照.仔细一看,里面甚至有Cee Lo Green等明星,但是大妈一直都是一脸和蔼的微笑,笨拙地比着V字. 这30年来,坚尼街的店铺开了又关,只有从来不打广告的Eva大妈靠着“自来明星粉”和国人那份特有的赤诚和坚韧,白手起家,把生意做得红红火火. 彼时还是28岁女青年的Eva,从澳门来到纽约才没多久.好不容易度过了在国外生活的适应期,坚强的她就决定和其他移民过来的亲戚一样,找个门路,自食其力养活自己. 其实Eva大妈的“家族生意”,就是卖金器,但是做生意不同打工,你永远不知道三年后你会不会因为没人光顾而倒闭破产.亲戚最多会把你领进门,但是绝不会在激烈的竞争下帮你做生意,拱手把客人让给你. 而这个华人女孩,还是果断地借了10万美元开了属于自己的店铺,她相信\",\n", "            \"url\": \"https://m.sohu.com/coo/sg/250668910_708829\",\n", "            \"src\": \"https://m.sohu.com/coo/sg/250668910_708829\",\n", "            \"title\": \"华人大妈成美国嘻哈界“教母”,巨星级rapper认她做干妈!_EvaSam\",\n", "            \"domain\": \"m.sohu.com\",\n", "            \"white_src\": \"手机搜狐网\",\n", "            \"rank\": 7,\n", "            \"icon\": \"https://img02.sogoucdn.com/app/a/200913/30b30b6c3013fff8a43e58a2bccf2ae4.png\",\n", "            \"passage\": \"\"\n", "        },\n", "        {\n", "            \"snippet\": \"未经作者授权,禁止转载 6.0万 生活的起起落落在艺术里都有答案 商务合作➕v:Ruby _MAOLEI 世界上最离谱的华裔大妈!征服了全美嘻哈圈,更让无数大亨抢着认妈? 不可思议!【|碧昂絲 |ay-z |A$AP|Hip-Hop|嘻哈|說唱】 98.0万 46 展开\",\n", "            \"url\": \"https://m.bilibili.com/video/BV1cJ4m1b7uy/\",\n", "            \"src\": \"https://m.bilibili.com/video/BV1cJ4m1b7uy/\",\n", "            \"title\": \"没人敢惹?潮汕大妈成全美嘻哈圈公认教母!_哔哩哔哩_bilibili\",\n", "            \"domain\": \"m.bilibili.com\",\n", "            \"white_src\": \"哔哩哔哩\",\n", "            \"rank\": 8,\n", "            \"icon\": \"http://img02.sogoucdn.com/app/a/200913/0575016fe303c2dc06f69899325d41d2.png\",\n", "            \"passage\": \"\"\n", "        }\n", "    ],\n", "    \"online_mod\": \"OM_Basic\",\n", "    \"search_count\": 0\n", "}\n"]}], "execution_count": 25}, {"metadata": {"ExecuteTime": {"end_time": "2024-09-10T02:04:49.247641Z", "start_time": "2024-09-10T02:04:47.388589Z"}}, "cell_type": "code", "source": ["import requests\n", "import json\n", "\n", "server = \"http://************:32015\"\n", "\n", "def Query():\n", "    url = \"{}/search\".format(server)\n", "    data = {\n", "        \"query\":\"马化腾\",\n", "    }\n", "    headers = {\n", "            \"Content-type\": \"application/json\"\n", "    }\n", "    resp = requests.post(url, data=json.dumps(data), headers=headers)\n", "    return resp.text\n", "\n", "Query()"], "id": "c1d48a2da030db91", "outputs": [{"data": {"text/plain": ["'{\"ans_dic\":{\"engine\":\"SOGOU\", \"ans_str\":\"\", \"ans_box_dic\":null}, \"all_snip_dics\":[{\"snippet\":\"马化腾(<PERSON><PERSON><PERSON> Ma、Pony Ma),1971年10月29日出生于海南省东方市,毕业于深圳大学计算机及应用专业,获理学士学位.中国企业家,腾讯公司主要创办人之一,现任腾讯公司董事会主席兼首席执行官、中网联第二届理事会副会长.1998年创办腾讯,其公司代表作QQ,为中国影响力最大的个人网络即时通信软件之一,因此外界称他“QQ之父”. 2018年至2022年,连续五年入选《财富》杂志 “中国最具影响力50位商界领袖”.是第十二届、第十三届全国人大代表,在纪念改革开放四十周年之际,被中共中央、国务院授予“改革先锋”称号.在1998年创立腾讯前,曾在深圳润迅通讯发展有限公司工作,主管互联网传呼系统的研究开发. 2021胡润全球慈善家公布,马化腾以慈善捐赠总价值400亿元位列全球第34位. 中文名 马化腾 外文名 Huateng Ma、Pony Ma 别名 小马哥 国籍 中国 民族 汉族 出生日期 1971年10月29日 出生地 海南省东方市(原广东省海南岛东方县) 毕业院校 深圳大学计算机与软件学院 职务 腾讯公司控股董事会主席兼首席执行官、中网联第二届理事会副会长 职业 企业家 主要荣誉 2004年CCTV中国经济年度人物新锐奖、深圳经济特区建立40周年创新创业人物和先进模范人物 父亲 马陈术 母亲 黄惠卿 人物经历 1971年10月29日出生于海南岛东方市八所港(原属广东省,海南建省后划归海南省,今海南省东方市),父母是八所港港务局职员,祖籍广东省潮阳县(今属广东省汕头市辖区),还有一个年长四岁的姐姐. 1984年,13岁马化腾随家人从海南迁至深圳 ,后转入深圳中学,1989年,高考以739分的优异成绩考入深圳大学电子工程系计算机专业. 1993年,毕业后进入深圳润迅通讯发展有限公司 ,开始做编程工程师,专注于寻呼机软件的开发,至升任开发部主管.该段经历使马化腾明确了开发软件的意义就在于实用,而不是写作者的自娱自乐.润讯提升了马化腾的视野,以及给马化腾在管理上必要的启蒙. 1998年,实用软件概念不仅培养了马化腾敏锐的软件市场感觉,也使他从中盈利不菲.马化腾是风靡一时的股霸卡的作者之一,他和朋友合作开发的股霸卡在赛格电子市场一直卖得不错.马化腾还不断为朋友的公司解决软件问题.这使他不仅在圈内小有名气,而且也有了相当的原始积累. 2023年12月,任中国网络社会\", \"url\":\"http://baike.sogou.com/v6242.htm?fromTitle=%E9%A9%AC%E5%8C%96%E8%85%BE\", \"src\":\"http://baike.sogou.com/v6242.htm?fromTitle=%E9%A9%AC%E5%8C%96%E8%85%BE\", \"title\":\"马化腾 - 搜狗百科\", \"domain\":\"baike.sogou.com\", \"white_src\":\"搜狗百科\", \"rank\":0, \"icon\":\"http://img02.sogoucdn.com/app/a/200913/3c28af542f2d49f7a124d51735400958.png\", \"passage\":\"\"}, {\"snippet\":\"马化腾 马化腾,汉族潮汕人,1971年出生于广东省汕头市,1993年获深圳大学理学士学位.腾讯公司主要创办人之一.现任腾讯公司董事会主席兼首席执行官;全国青联副主席;全国人大代表. 1984年随父母从海南迁至深圳,1993年毕业于深圳大学计算机系.同年进入深圳润迅通讯发展有限公司开始寻呼系统的研究开发工作 .1995年创建惠多网深圳站,名为ponysoft.1998年,马化腾和同学张志东注册成立＂深圳市腾讯计算机系统有限公司＂,随后陈一丹、许晨晔、曾李青相继加入. 2017年8月7日,腾讯股价 盘中再创历史新高价320.6港元,马化腾身家361亿美元成为中国首富. 2018年4月,获《时代周刊》2018年全球最具影响力人物荣誉. 2018年10月25日,福布斯发布了2018福布斯中国400富豪榜,马化腾凭借328亿美元的身家蝉联榜单第二名.2018年12月18日,党中央、国务院授予马化腾同志改革先锋称号,颁授改革先锋奖章.2019年3月,马化腾以388亿美元财富排名2019年福布斯全球亿万富豪榜第20位. 2019年9月5日,突破奖基金会及其赞助人——马化腾等人共同宣布2020年突破奖及新视野奖的获得者. 2019福布斯中国慈善榜排名第4位. 2019年10月19日,入选2019福布斯年度商业人物之跨国经营商业领袖 名单. 2019年11月7日,以2,545.50亿元财富值位列2019福布斯中国400富豪榜第2名. 目录 1 马化腾的奋斗史 2 马化腾的商业之路 2.1 第一部分:发现互联网的价值 2.2 第二部分:“抄”来创业路 2.3 第三部分:后发制人一定要“抄” 3 马化腾人物观点 马化腾的奋斗史 一、 马化腾,何许人也 ? 1971年10月29日出生于汕头潮阳.1984年随父母从海南迁至深圳.1993年毕业于深圳大学计算机专业,进入润迅通信发展有限公司,任软件工程师、开发部主管.1998年创办深圳市腾讯计算机系统有限公司,多次被评为“中国十大IT风云人物”.2004年6月,腾讯在香港上市,马化腾担任公司董 事局主席和CEO.同年被美国《时代周刊》和有线新闻网评为2004年全球最具影响力的25名商界领袖之一,荣膺香港理工大学第四届紫荆花杯杰出企业家 奖,捧走了“2004CCTV中国经济年度人物新锐奖”奖杯.2006年4月获“广东青年五四奖章”.  二、 掀起\", \"url\":\"https://wiki.mbalib.com/wiki/Ma_Huateng\", \"src\":\"https://wiki.mbalib.com/wiki/Ma_Huateng\", \"title\":\"马化腾 - MBA智库百科\", \"domain\":\"wiki.mbalib.com\", \"white_src\":\"MBA智库百科\", \"rank\":1, \"icon\":\"\", \"passage\":\"\"}, {\"snippet\":\"马化腾简介 马化腾,汉族,1971年10月29日生于广东省东方县八所港(今属海南省东方市),祖籍广东省汕头市潮南区.1993年获深圳大学理学学士学位.腾讯公司主要创办人之一.现任腾讯公司董事会主席兼首席执行官;全国青联副主席;全国人大代表.1998年,马化腾和同学张志东注册成立深圳市腾讯计算机系统有限公司,随后陈一丹、许晨晔、曾李青相继加入. 推荐度: 导读 马化腾,汉族,1971年10月29日生于广东省东方县八所港(今属海南省东方市),祖籍广东省汕头市潮南区.1993年获深圳大学理学学士学位.腾讯公司主要创办人之一.现任腾讯公司董事会主席兼首席执行官;全国青联副主席;全国人大代表.1998年,马化腾和同学张志东注册成立深圳市腾讯计算机系统有限公司,随后陈一丹、许晨晔、曾李青相继加入. 1、马化腾,汉族,1971年10月29日生于广东省东方县八所港(今属海南省东方市),祖籍广东省汕头市潮南区.1993年获深圳大学理学学士学位.腾讯公司主要创办人之一.现任腾讯公司董事会主席兼首席执行官;全国青联副主席;全国人大代表. 2、1984年随父母从海南迁至深圳,1993年毕业于深圳大学计算机系.同年进入深圳润迅通讯发展有限公司开始寻呼系统的研究开发工作.1995年创建惠多网深圳站,名为ponysoft. 3、1998年,马化腾和同学张志东注册成立深圳市腾讯计算机系统有限公司,随后陈一丹、许晨晔、曾李青相继加入. 4、2017年8月7日,腾讯股价盘中再创历史新高价320.6港元,马化腾身家361亿美元成为中国首富.2018年4月,获《时代周刊》2018年全球最具影响力人物荣誉.2018年10月25日,福布斯发布了2018福布斯中国400富豪榜,马化腾凭借328亿美元的身家蝉联榜单第二名.2018年12月18日,党中央、国务院授予马化腾同志改革先锋称号,颁授改革先锋奖章.2019年3月,马化腾以388亿美元财富排名2019年福布斯全球亿万富豪榜第20位.019年9月5日,突破奖基金会及其赞助人——马化腾等人共同宣布2020年突破奖及新视野奖的获得者.2019福布斯中国慈善榜排名第4位.2019年10月19日,入选2019福布斯年度商业人物之跨国经营商业领袖名单.2019年11月7日,以2,545.50亿元财富值位列2019福布斯中国400富豪榜第2名.2020年11月9日,财付通\", \"url\":\"https://sgh.51dongshi.com/eggaedfgbdsrhh.html\", \"src\":\"https://sgh.51dongshi.com/eggaedfgbdsrhh.html\", \"title\":\"马化腾简介_懂视\", \"domain\":\"sgh.51dongshi.com\", \"white_src\":\"懂视\", \"rank\":2, \"icon\":\"https://img03.sogoucdn.com/app/a/200913/51dongshi2\", \"passage\":\"\"}, {\"snippet\":\"马化腾 马化腾,腾讯公司主要创办人之一,现任腾讯公司控股董事会主席兼首席执行官,全国青联副主席,全国人大代表.曾荣获“中国香港第四届紫荆花杯杰出企业家”、《时代》2014年度百大影响力人物、《中国企业家》最具影响力的企业领袖等奖项. 人物名片 中文名 马化腾 外文名 Pony 性别 男 国籍 中国 民族 汉族 籍贯 广东省汕头市潮南区 出生地 广东省东方县八所港(今属海南省东方市) 出生日期 1971年10月29日 星座 天蝎座 生肖 猪 毕业院校 深圳大学 职业职位 腾讯董事会主席 人物履历 1971年,出生在广东省东方县八所港(今属海南省东方市) . 1984年,随父母从海南省儋州市迁至广东省深圳市. 1986年-1989年,就读于深圳中学. 1989年-1993年,就读于深圳大学计算机专业. 1993年,深大毕业,进入润迅通信发展有限公司,从专注于寻呼软件开发的软件工程师一直做到开发部主管. 1998年,和好友张志东创办腾讯计算机系统有限公司,之后又吸纳了三位股东:曾李青、许晨晔、陈一丹. 社会任职 2015年7月,当选为十二届全国青联副主席. 2018年1月,当选广东省第十三届全国人民代表大会代表. 2018年5月9日,当选中国网络社会组织联合会副会长. 富豪榜排名(部分) 2014年,以财富1007亿元荣登3000中国家族财富榜榜首. 2015年3月2日,以161亿美元位列2015年福布斯中国富豪榜单第六. 2015年4月,《财富》2015＂中国最具影响力的50位商界领袖＂排行榜第2位. 2015年10月,以1030亿元位居2015信中利·胡润IT富豪榜第二. 2015年10月26日,以176亿美元位居2015年福布斯中国富豪榜三. 2016年10月13日,以1650亿元在2016年胡润百富榜中排名第三. 2016年10月18日,以1340亿元在2016胡润IT富豪榜中排名第二. 2016年10月27日,以245亿美元财富在2016福布斯中国富豪榜中排名第三位. 2017年7月17日,以净资产324亿美元排名福布斯富豪榜第23位. 2019年11月7日,以2,545.50亿元财富值位列2019福布斯中国400富豪榜第2名. 2020年,以381亿美元排名福布斯全球亿万富豪榜第20位. 2020年6月23日,以3200财富(亿元人民币)位列《胡润全球百强企业\", \"url\":\"https://www.maigoo.com/mingren/3817.html\", \"src\":\"https://www.maigoo.com/mingren/3817.html\", \"title\":\"马化腾-深圳市腾讯计算机系统有限公司董事会主席介绍→MAIGOO人物\", \"domain\":\"www.maigoo.com\", \"white_src\":\"买购网\", \"rank\":3, \"icon\":\"https://img02.sogoucdn.com/app/a/200913/maigoocom\", \"passage\":\"\"}, {\"snippet\":\"1989年就读深圳大学计算机专业. 1998年和好友张志东注册成立“深圳市腾讯计算机系统有限公司”,之后许晨晔、陈一丹、曾李青相继加入.腾讯控股的战略目标是“连接一切”,通过长期聚焦于社交平台与数字内容两大核心业务,腾讯控股已发展成为中国最大的互联网综合服务提供商之一,也是中国服务用户最多的互联网企业之一.腾讯为中国人创造了全新的在线生活方式,进而重塑了中国商业生态.2007年,腾讯倡导并发起了中国互联网第一家在民政部注册的全国性非公募基金会——腾讯公益慈善基金会.2009年,腾讯入选《财富》“全球最受尊敬50家公司”. 2018 年,世界品牌实验室(World Brand Lab)发布《中国500最具价值品牌》分析报告,腾讯以4028.45亿元位居第二.2018年《财富》世界500强排行榜中,腾讯公司位列331位. 2017年8月7日,腾讯股价盘中再创历史新高价320.6港元,马化腾身家361亿美元成为中国首富.2018年4月,获《时代周刊》2018年全球最具影响力人物荣誉. 2008年,腾讯创始人校友团队向深圳大学捐赠1000万元;2013年腾讯创始人校友团队联合腾讯公益慈善基金会向深圳大学捐赠3000万元,用于深圳大学的奖教奖学、创新创业和师生关爱工作;2018年9月21日,值深圳大学35周年校庆之际,腾讯四位创始人马化腾、张志东、许晨晔、陈一丹联合向深圳大学捐赠3.5亿元,发起设立深圳大学人才基金,其中马化腾捐赠2亿元. 腾讯五人创业团队有四人毕业于深圳大学,他们是董事会主席、首席执行官马化腾、腾讯学院荣誉院长张志东(89级计算机)、首席信息官许晨晔(89级计算机)、腾讯公益慈善基金会发起人兼荣誉理事长陈一丹(89级应用化学). \", \"url\":\"https://www.szu.edu.cn/info/1006/5509.htm\", \"src\":\"https://www.szu.edu.cn/info/1006/5509.htm\", \"title\":\"马化腾-深圳大学\", \"domain\":\"www.szu.edu.cn\", \"white_src\":\"深圳大学\", \"rank\":4, \"icon\":\"\", \"passage\":\"\"}, {\"snippet\":\"马化腾,男,1971年10月生,籍贯广东,腾讯公司执行董事长、董事会主席兼首席执行官.1993年毕业于深圳大学计算机专业,进入深圳润迅通信发展有限公司工作,从软件工程师一直做到开发部主管.1998年创办腾讯计算机系统有限\", \"url\":\"https://www.chinadaily.com.cn/dfpd/sz30nian/2010-08/18/content_11169487.htm\", \"src\":\"https://www.chinadaily.com.cn/dfpd/sz30nian/2010-08/18/content_11169487.htm\", \"title\":\"深圳经济特区30年杰出创新人物:马化腾\", \"domain\":\"www.chinadaily.com.cn\", \"white_src\":\"中国日报\", \"rank\":5, \"icon\":\"\", \"passage\":\"\"}]}'"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "execution_count": 24}, {"metadata": {"ExecuteTime": {"end_time": "2024-09-10T03:40:00.024213Z", "start_time": "2024-09-10T03:39:59.561881Z"}}, "cell_type": "code", "source": ["import json\n", "from src.utils.actions.tool_actions.web_search_base import WebSearchBase\n", "from typing import Optional, Union, Dict\n", "import aiohttp\n", "import time\n", "import jwt\n", "import asyncio\n", "\n", "class SouGou(WebSearchBase):\n", "    def __init__(self,engine_name=\"sougou\"):\n", "        super().__init__()\n", "        if engine_name == \"sougou\":\n", "            self.engine_name = \"sougou\"\n", "            self.sougou_url = self.config[\"sougou_url\"]\n", "            self.url = \"{}/search\".format(self.sougou_url)\n", "            self.header = {\n", "                    \"Content-type\": \"application/json\"\n", "            }\n", "        elif engine_name == \"sougou_full\":\n", "            self.engine_name = \"sougou_full\"\n", "            self.ak = self.config[\"Dojo_ak\"]\n", "            self.sk = self.config[\"Dojo_sk\"]\n", "            payload = {\n", "                'iss': self.ak,\n", "                'exp': int(time.time()) + 5184000,  # 填写您期望的有效时间，此处示例代表当前时间+30分钟\n", "                'nbf': int(time.time()) - 5  # 填写您期望的生效时间，此处示例代表当前时间-5秒\n", "            }\n", "            self.header = {'Authorization': f'Bearer {jwt.encode(payload=payload, key=self.sk)}'}\n", "\n", "\n", "    async def search(self, query):\n", "        if self.engine_name==\"sougou_full\":\n", "            async with aiohttp.ClientSession() as session:\n", "                async with session.post(\n", "                    url='http://10.198.6.242:8088/custom/search',\n", "                    json={\n", "                        'query': query,\n", "                        'topk': 20,\n", "                        'onlineMod': 'OM_Basic'\n", "                    },\n", "                    headers=self.header\n", "                ) as response:\n", "                    if response.status == 200:\n", "                        response_json = await response.json()\n", "                        return self.format_search_res(response_json) \n", "                    else:\n", "                        return {'error': f'Response failed with status code {response.status}'}\n", "        elif self.engine_name==\"sougou\":\n", "            async with aiohttp.ClientSession() as session:\n", "                async with session.post(\n", "                    url=self.url,\n", "                    json={\"query\": query},  \n", "                    headers=self.header\n", "                ) as response:\n", "                    if response.status == 200:\n", "                        response_json = await response.json()\n", "                        return self.format_search_res(response_json)\n", "                    else:\n", "                        return {'error': f'Response failed with status code {response.status}'}\n", "            \n", "                \n", "    def format_search_res(self, search_res):\n", "        result = {}\n", "        answer_box = search_res.get('ans_dic')\n", "        if answer_box:\n", "            result[\"answerBox\"] = {\n", "                \"title\": answer_box.get('title', ''),\n", "                \"snippet\": answer_box.get('ans_str', ''),\n", "                \"link\": answer_box.get('link', ''),\n", "                \"date\": answer_box.get('date', '')\n", "            }\n", "\n", "        organic_results = search_res.get('all_snip_dics', '')\n", "        if organic_results:\n", "            for idx, entry in enumerate(search_res['all_snip_dics'], start=1):\n", "                result[idx] = {\n", "                    \"title\": entry.get('title', ''),\n", "                    \"snippet\": entry.get('snippet', ''),\n", "                    \"link\": entry.get('url', ''),\n", "                    \"date\":entry.get('date','')\n", "                }\n", "        return result"], "id": "c15a60e545e15aa4", "outputs": [], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2024-09-10T03:40:01.384574Z", "start_time": "2024-09-10T03:40:01.359638Z"}}, "cell_type": "code", "source": "sougou = SouGou(engine_name=\"sougou\")", "id": "af049ae7f0658252", "outputs": [], "execution_count": 2}, {"metadata": {"ExecuteTime": {"end_time": "2024-09-10T03:40:04.164758Z", "start_time": "2024-09-10T03:40:02.099632Z"}}, "cell_type": "code", "source": "await sougou.search(\"小米su8消息\")", "id": "6e7df36edc5d1664", "outputs": [{"data": {"text/plain": ["{'answerBox': {'title': '', 'snippet': '', 'link': '', 'date': ''},\n", " 1: {'title': '近期,关于小米汽车将推出SU8的消息在车_车家号_发现车生活_汽车之家',\n", "  'snippet': '近期,关于小米汽车将推出SU8的消息在车友圈传开了锅,不少米粉对此兴奋不已!针对此事,小米集团公关部总经理王化出来辟谣称:没有SU8,假的!不少网友认为,小米SU7上市近4个月了,且得到不错的市场反馈,真的假不了,假的也真不了,从以往的惯例来看,越是辟谣越有可能是真的,大家觉得呢?',\n", "  'link': 'https://chejiahao.autohome.com.cn/info/15869056',\n", "  'date': ''},\n", " 2: {'title': '全新小米SU8(代号MX11)最新信息曝光:未来智能SUV的新标杆_车家号_发现车生活_汽车之家',\n", "  'snippet': '近日,关于小米汽车即将推出的全新SUV车型——小米SU8(内部代号MX11)的消息不断曝光,引发了广泛关注和讨论.这款备受期待的车型,不仅在设计上展现出独特的魅力,更在技术配置和市场定位上彰显出小米汽车的雄心壮志. 从汽车之家和其他多个平台发布的设计假想图来看,小米SU8与小米SU7在设计上有很大的相似之处,因此它们很可能是SU7的进阶和扩充版本.这可能是由于小米SU7的设计理念已经在市场上获得了广大的认可,并且这是一个旨在减少设计和生产开销的策略.此外,这两种产品也具有相似的外观风格.从小米SUV的设计哲学来看,这种连续的设计策略有助于保持品牌的连贯性并提高消费者的认知度. 关于小米SU8的确切上市时间和定价策略,现在还没有一个明确的答案.不过,有一点可以肯定,这款手机将很快成为国内最受欢迎的智能手机之一.消费者对这款车的预期主要围绕其上市的时间和定价范围.根据市场的反应,鉴于小米SU7在市场上的卓越表现,消费者对小米SU8抱有很高的期待.这款国产手机在去年年底就已经被各大厂商炒得火热,并成为今年最热门的话题之一.现在,在国内,已经有一部分消费者开始对这款汽车产生了兴趣.从消费者角度出发,这款车型将是一款非常优秀的小车型产品,而且也符合当前国内主流消费人群的需求.然而,要了解具体的市场反应,我们还需等待新车正式进入市场后才能得知. 小米SU8的内部编码是MX11,作为小米汽车即将推出的第二款车型,预计将在2025年正式向公众发布.据悉,这款车是全球首款搭载石墨烯技术的智能小车.这款产品运用了目前国内领先的金属拉丝技术,并搭载了高能量密度的电池和双离合变速器,从而实现了更长的行驶距离和更出色的驾驶体验.此外,它还搭载了小米首款智能语音识别技术——“米聊”系统,能够通过语音控制空调、车载摄像头等功能.虽然关于小米提前发布的信息在市场上广为传播,小米的官方机构已经对这一情况进行了澄清和辟谣.不过,这款以“智能互联”为主打的手机还是受到不少关注,特别是它的外形与配置都很有看头.据了解,这款手机目前已经在国内的某些城市开始销售,预测在今年的下半段,它将首次进入中国的市场.从设计的视角出发,小米SU8预计将持续采纳小米SU7的成功设计思路,并对其进行适当的扩展和优化.另外,小米公司将会针对用户需求进行重新定义.此外,我们还会对某些细节进行更深入的优化和改进,例如取消后视镜功能',\n", "  'link': 'https://chejiahao.autohome.com.cn/info/16001097',\n", "  'date': ''},\n", " 3: {'title': '小米SU8要来了?不出意外肯定是SU7“增高版”_哔哩哔哩_bilibili',\n", "  'snippet': '非汽车媒体从业者!只是一位很喜欢汽车的大叔对一些汽车坦诚直言 21.6万 125 2.8万 29 展开',\n", "  'link': 'https://m.bilibili.com/video/BV1wb42187te/',\n", "  'date': ''},\n", " 4: {'title': '10年斥资100亿美元!老板亲自辟谣小米SU8, 网友:雷军彻底不装|格力|董明珠|何小鹏|投资者|小米手机|小米造车|小米su8_网易订阅',\n", "  'snippet': '引言:7月19日晚,雷军和小米的一场发布会又一次引爆了全网热搜;其中预告的小米SU7 Ultra,以及首发Xiaomi MIX fold 4折叠屏成为全程的焦点. 图为:2024年雷军年度演讲 与此前的发布会不同的是,这次雷军的演讲主题为 勇气 ,且伴随着此次小米SU7 Ultra的预售,雷军还重点讲述了自己这一路走来辛苦的造车经历; 图源:新浪微博 在演讲当中,最令人深刻的话就是:“过去三个多月时间,我感觉每天都像在梦里一样.回顾过去 1000 多个日日夜夜 ,小米汽车发生了太多的跌宕起伏的故事.” 我们都知道的是,小米汽车作为市面上资历最浅的新能源 品牌 ,其所要经历和面临的挑战相较于其他同行来说,肯定是更多的. 其中的艰辛当然也是常人难以想象,报喜不报忧也是雷军在日常生活中的性格; 从此前的小米SU7售卖情况来看,雷总也在发布会上说过:“自小米SU7上市以来,我既怕卖不动,又怕不够卖;近乎所有人 都不看好 它的销售数量.” 图为:雷军年度演讲 话不多说,让我们直接进入正题; 来看看在这次的发布会当中,雷总到底说了些什么? 首战告捷,感受如何? 小米SU7 Ultra征战纽北市场 图为:雷军在年度演讲中感谢李斌和何小鹏 时光荏苒 ,不知不觉雷总的年度演讲已经开到了 第五场? 在这长达3个小时的演讲和新品发布会当中,雷军先用前一个小时来讲述小米造车的来龙去脉,还有这几年来各种 跌宕起伏 的故事. 我们很明显的可以感受到雷总就对于此次造车来说,是非常用心和专注的;就不仅为了造车还特意考了 赛车驾照 ,且在演讲当中向观众展示了自己的漂移视频. 众所周知,小米汽车出现最开始的初衷,只是想要达到新能源汽车 领头羊 的位置;可这两天 小米SU7 Ultra的预售 ,似乎让小米真正走向了国产电车的高端领域. 图为:小米SU7 Ultra的预售 据雷军在发布会上的介绍:“小米SU7 Ultra之所以能够被批准和生产出来,最重要的还是希望,小米能够让这座全新的电动四门跑车,所包含和 打磨的先进技术 ,可以在未来下放到普通 老百姓 的生活当中,并为之使用.” 对于雷军来说,小米SU7 Ultra最重要的意义是,希望能够在将来凭借着这款车,可以在 纽博格林北环赛道 ,挑战纽北原型车非量产圈速榜,且在一年之后正式挑战纽北量产车圈速榜; 并在未来完成和实现与 全球顶尖车企硬碰硬 ,正面对',\n", "  'link': 'https://3g.163.com/dy/article_v2/J7VKCVMH05390TQD.html',\n", "  'date': ''},\n", " 5: {'title': '小米su7都还没见过,小米su8就又曝光了,这颜值就是年轻人第一台阿斯顿马丁_哔哩哔哩_bilibili',\n", "  'snippet': '未经作者授权,禁止转载 39.3万 224 82.1万 1607 3.9万 6',\n", "  'link': 'https://m.bilibili.com/video/BV1rp421Q7ZE/',\n", "  'date': ''},\n", " 6: {'title': '小米SU8来了,多张效果图曝光,或对标特斯拉Model Y!_哔哩哔哩_bilibili',\n", "  'snippet': '未经作者授权,禁止转载 投币 或对标特斯拉Model Y. 评说电车美与丑,拒绝傲慢与偏见! 新车发布|试驾评测|技术解读|行业分析 买电车,看我们.商务合作加V:Sky7007 自动连播 7866播放 简介 订阅合集 蔚来第50万台量产车下线,将正式推出新品牌“乐道” 00:35 小米汽车与宁德时代合资公司成立,雷军入局电池制造业! 00:30 特斯拉裁员仍在继续,韩国超充团队解散,要求员工自愿离职 00:37 全固态电池时代要来了?我国将投入60亿元,鼓励全固态电池研发(1) 00:36 日本五大车企集体造假,涉及车辆安全性能测试,丰田本田马自达已公开道歉 00:54 大众最强纯电车型来了!ID.7 GTX 开启预售,49.8万元起! 00:31 宁德时代组织架构调整,曾毓群直管运营与采购 00:40 上汽换帅!王晓秋接任陈虹,出任集团董事长 00:25 刷新纪录!问界新 M7 Ultra车型,上市18天交付破万 00:39 新势力第一!理想汽车用时54个月,累计交付80万辆 00:28 全球首家!比亚迪第800万辆新能源车下线 01:45 极氪首款家用SUV伪装照公布,极氪007兄弟车型? 00:28 小米汽车获独立生产资质,SU7 尾标去掉“北京” 00:24 小米SU8来了,多张效果图曝光,或对标特斯拉Model Y! 00:25 问界M7卖爆了,累计交付破20万辆 00:39 鸿蒙智行首款纯电轿跑SUV,智界R7正式亮相 01:23 强强联合!比亚迪方程豹“牵手”华为乾崑智驾 00:37 何小鹏:未来10年,中国主流车企只剩7家 00:59 8月29日,晚点报道称,特斯拉计划明年交付两版改款Model Y.5 座版焕新Model Y预计一季度交付,7座版Model Y预计四季度交付. 特斯拉地补来袭,置换 重庆12000 海南14000 浙江10000-12000,你心动了吗#特斯拉#modely#model3#购车补贴 1.1万 4 14.0万 1382 展开',\n", "  'link': 'https://m.bilibili.com/video/BV1xrbTecELE/',\n", "  'date': ''},\n", " 7: {'title': '小米SU8,已在山东上市_哔哩哔哩_bilibili',\n", "  'snippet': 'Micmacs à la gare 志明你在干嘛?在开车啊!一个汽车玩家,汽车节目主持人 43.1万 83 23.8万 147 1210.7万 4269 9.9万 35 182.6万 441 4.3万 31 6.9万 8 展开',\n", "  'link': 'https://m.bilibili.com/video/BV1HSs5eAEL7/',\n", "  'date': ''},\n", " 8: {'title': '2025款小米 SU8 中型 SUV 终于亮相,国外热议:中国完全是另一个水平!_哔哩哔哩_bilibili',\n", "  'snippet': '2025款小米 SU8 中型 SUV 终于亮相,国外热议:中国完全是另一个水平! 带您领略世间万物,看大有可观的世界,感谢支持 10.8万 81 展开',\n", "  'link': 'https://m.bilibili.com/video/BV1pb421b7U6/',\n", "  'date': ''},\n", " 9: {'title': '小米SU8来了,高管否认不叫小米SU8,难道叫小米SB8_哔哩哔哩_bilibili',\n", "  'snippet': '8.7万 211 21.8万 39 4.9万 32 79.6万 1218 3.9万 28 展开',\n", "  'link': 'https://m.bilibili.com/video/BV1BW421R7v8/',\n", "  'date': ''},\n", " 10: {'title': '小米SU8要来了?官方辟谣!_哔哩哔哩_bilibili',\n", "  'snippet': '穿越汽车世界,体验无限可能.数据来源于易车,还想了解更多汽车内容欢迎关注私信,或点击下方加入群聊.',\n", "  'link': 'https://m.bilibili.com/video/BV1fT8FeAE9L/',\n", "  'date': ''}}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "execution_count": 3}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "93c7a27b5ad4e33a"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}