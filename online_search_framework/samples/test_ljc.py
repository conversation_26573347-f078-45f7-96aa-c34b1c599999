import openai
from openai import OpenAI

if __name__ == '__main__':
    l = []
    if l is None:
        print("123")
    print(l)

    print(type({"深圳 天气 2024-09-05", "北京 天气 2024-09-05"}))
    print(list({"深圳 天气 2024-09-05", "北京 天气 2024-09-05"}))
    input_str = "{}".strip("{}")
    print(input_str)
    keywords = [item.strip(" \"") for item in input_str.split(",")]
    print(keywords)
    # openai.api_key = "ab9edab4-8780-11ef-a760-cbde10bdf191"
    # models = openai.Model.list()
    # for model in models['data']:
    #     print(model)

    client_sync = OpenAI(
        api_key="ab9edab4-8780-11ef-a760-cbde10bdf191", base_url="http://*************:8000/v1"
    )
    print(client_sync.models.list().data[0].id)
    print(client_sync.models.list())
