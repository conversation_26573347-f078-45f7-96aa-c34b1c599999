import sys
import os


project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)


import json
import secrets
import uuid
from concurrent.futures import Thread<PERSON>oolExecutor
from dataclasses import dataclass
from starlette.concurrency import run_in_threadpool
from datetime import datetime
from typing import Union, Dict, DefaultDict, List, Any
import time
from loguru import logger
import collections

import configuration
from nova.security_policy import TextModerationRequest, stream_output_moderate_text, moderate_text_generic
from src.utils.actions.tool_actions.serper import Serper
from src.utils.actions.tool_actions.web_search import WebSearch
from src.prompts.follow_up_prompts import follow_up_prompts
from src.prompts.guide_prompts import guide_user_prompt_zh, guide_system_prompt_zh
from src.prompts.summary_guide_prompts import search_summary_guide_user_prompt_zh, search_summary_guide_system_prompt_zh
from src.utils.actions.tool_actions.bing import Bing
from src.utils.actions.tool_actions.sougou import SouGou
#from src.utils.actions.tool_actions.bytedance import Bytedance
from src.utils.llms.llm_base import LLMBase
from src.utils.actions.other_actions.topk_base import TopkBase
from src.utils.actions.tool_actions.action_excutor import ActionExecutor
from src.prompts.router_prompts import router_system_zh, router_user_zh,router7b_system_prompt,router7b_user_prompt
from src.utils.utils import (
    stage0_parse_intent,
    stage1_format2query_with_link,
    stage3_format_web_fetch,
    stage3_format_rest_query_with_link,
    format_query_with_link,
    adjust_ranking_sum_to_list_minus_one,
    extract_tsl_query_with_link,
)
from src.prompts.summary_prompts import (
    search_summary_system_prompt_zh_v1,
    summary_system_prompt_zh,
    search_summary_user_prompt_zh,
    summary_user_prompt_zh,
)

from typing import Optional
from src.utils.llms.openai_SDK import OpenAI_LLM
from src.utils.multithreading import MultiThreadExecutor
import asyncio
from src.utils.memory.memory_base import MemoryBase
from service.logger_manager import fire_logger
from src.utils.web_content_scrape import WebScrape,WebScrapePy


@dataclass
class WebChatQueryRequest:
    query: str
    message_id: str
    request_id: Optional[str] = None
    model_name: Optional[str] = None
    resource: str = "default"


class WebSearchAgent:
    def __init__(
        self,
        intent_llm: OpenAI_LLM,
        summary_llm: OpenAI_LLM,
        topk_method: Union[TopkBase, None],
        action_executor: ActionExecutor,
        public_memory: MemoryBase,
        crawler: Union[WebScrape, WebScrapePy],
        detect: bool = False,
        sensitive_config: Optional[Dict[str, str]] = None,
        search_nums: int = 10,
        rest_k: int = 5,
        using_cached_link_first: bool = False,
        reranker_using_title: bool = False,
        truncate_length: int = 3000,
        router_system_prompt: str = router_system_zh,
        router_user_prompt: str = router_user_zh,
    ) -> None:
        self.action_executor = action_executor
        self.intent_llm = intent_llm
        self.summary_llm = summary_llm
        self.topk_method = topk_method
        self.crawler = crawler
        self.public_memory = public_memory
        self.detect = detect
        if sensitive_config is None:
            self.sensitive_config = {}
        else:
            self.sensitive_config = sensitive_config

        self.rest_k = rest_k
        self.search_nums = search_nums
        self.using_cached_link_first = using_cached_link_first
        self.reranker_using_title = reranker_using_title
        self.truncate_length = truncate_length
        self.router_system_prompt = router_system_prompt
        self.router_user_prompt = router_user_prompt

    async def stage0_intent_rewrite(self, query):
        input_query = self.router_user_prompt.format(user_input=query)

        router_system_prompt = self.router_system_prompt.format(
            formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        # logger.error(self.public_memory)
        messages = (
            [{"role": "system", "content": router_system_prompt}]
            + self.public_memory.get_past_messages()
            + [{"role": "user", "content": input_query}]
        )

        intent_return = await self.intent_llm.chat(
            messages,
            stream=False,
        )

        intent_json_output = stage0_parse_intent(query, intent_return)
        return intent_json_output
    
    async def stage0_guide(self, query):
        guide_user_prompt = guide_user_prompt_zh.format(user_input=query)

        guide_system_prompt = guide_system_prompt_zh.format(formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

        messages = [{"role": "system", "content": guide_system_prompt}] + self.public_memory.get_past_messages() + [{"role": "user", "content": guide_user_prompt}]
        logger.info(f"stage0_guide messages:   {messages}")

        res = await self.intent_llm.chat(
            messages,
            stream=False,
        )

        return res

    async def stage1_web_search(self, search_queries):
        """
        Perform asynchronous web searches for a list of queries, USING MULTI search engine if theres more than one.

        Args:
        - search_queries: A list of query strings. Each string represents a search query to be executed.

        e.g.
        search_queries: [商汤 股票 价格，商汤 股票 2024]

        Returns:
        - A dictionary where the keys are the original search queries and the values are the corresponding search results.

        self.action_executor("web_search", single_query) searchs each query using multiple search engine (if), see .
        """

        results = await asyncio.gather(*(self.action_executor("web_search", query, self.search_nums) for query in search_queries))

        temp_results = {query: result for query, result in zip(search_queries, results)}
        """
        temp_results:
        {
            "商汤 股票 价格": {
                "engine1": {
                    answerBox:{title:"",link:"",date,:"",from:""}, 1: {title:"",link:"",date,:"",from:""}, 2: {title:"",link:"",date,:"",from:""},... 
                },
                "engine2": {
                    answerBox:{title:"",link:"",date,:"",from:""}, 1: {title:"",link:"",date,:"",from:""}, 2: {title:"",link:"",date,:"",from:""},... 
                }
            },
            "商汤 股票 2024": {
                "engine1": {
                    answerBox:{title:"",link:"",date,:"",from:""}, 1: {title:"",link:"",date,:"",from:""}, 2: {title:"",link:"",date,:"",from:""},... 
                },
                "engine2": {
                    ### empty if engine 2 has an error
                }
            }
        }
        """
        # pprint(temp_results)
        query_with_link, fields_data, query_snippets, query_links = stage1_format2query_with_link(
            temp_results, cached_link_first=self.using_cached_link_first, include_title=self.reranker_using_title
        )
        # logger.error(query_with_link)
        return query_with_link, fields_data, query_snippets, query_links

    async def stage2_3(self, ori_query: str, query_with_link: dict, query_snippets: dict, query_links: dict):
        query_scrape_res_task = asyncio.create_task(self.crawler.scrape(ori_query, query_links))
        topk_query_with_link, res_query_with_link = await run_in_threadpool(self.topk_method.ranking, query_with_link, query_snippets)
        query_scrape_res, error_scrape_rate = await query_scrape_res_task
        llm_main_ref, llm_rest_ref = stage3_format_web_fetch(query_scrape_res, topk_query_with_link, res_query_with_link, truncate_length=self.truncate_length, rest_k=self.rest_k)
        return llm_main_ref, llm_rest_ref,error_scrape_rate

    async def stage4_llm_summary(self, query, reference_content_, rest_ref, request_id, request_data: TextModerationRequest, pre_text="", search=True):
        # if pre_text == "" or pre_text is None:

        if search:
            reference_content_ += rest_ref
            reference_content_ = reference_content_.replace("疫情", "")
            summary_user_prompt = search_summary_user_prompt_zh.format(web_content=reference_content_, user_input=query, formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M"))
            summary_system_prompt = search_summary_system_prompt_zh_v1.format(formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        else:
            summary_user_prompt = summary_user_prompt_zh.format(user_input=query, formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M"))
            summary_system_prompt = summary_system_prompt_zh.format(formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

        messages = [{"role": "system", "content": summary_system_prompt}] + self.public_memory.get_past_messages() + [{"role": "user", "content": summary_user_prompt}]
        logger.debug(f"stage4_llm_summary messages:   {messages}")

        full_response = ""

        async for chunk in self.summary_llm.yield_chat_from_db(
            messages,
            stream=True,
        ):
            full_response += chunk['content']

            if not self.detect:
                yield chunk['content']
            else:
                ###################################### 输出接入nova检测敏感词#######################################
                request_data.text = chunk['content']
                decision, tokens = await stream_output_moderate_text(str(request_id), request_data)
                if decision == "BLOCK":
                    yield "。(尊敬的用户您好，让我们换个话题再聊聊吧～)"
                    return
                elif decision == "Further":
                    continue
                elif decision == "PASS":
                    res = " ".join(tokens)
                    yield res
                    logger.debug(res, end="")
            ####################################### 输出接入nova检测敏感词#######################################

        if self.detect:
            ####################################### 输出接入nova检测敏感词,结束流式#######################################
            request_data.finish_detect = True
            decision, tokens = await stream_output_moderate_text(str(request_id), request_data)

            if decision == "BLOCK":
                yield "。(尊敬的用户您好，让我们换个话题再聊聊吧～)"
                return
            # elif decision == "Further":
            # continue
            elif decision == "PASS":
                res = " ".join(tokens)
                yield res
                logger.debug(res, end="")
            ###################################### 输出接入nova检测敏感词,结束流式#######################################

    async def stage5_follow_up_question(self):
        memory = self.public_memory.get_past_messages()
        query = memory[-2]["content"] + "\n" + memory[-1]["content"] + "\n"
        follow_up_prompt = follow_up_prompts.format(chat_his=query)
        # 没有system prompt

        messages = [{"role": "user", "content": follow_up_prompt}]
        # logger.info(f"messages:   {messages}")

        state5_result = await self.intent_llm.chat(
            messages,
            stream=True,
        )

        return state5_result

    async def stage5_follow_up_question_http(self):
        # memory = self.summary_llm.memory.get_all_messages()
        query = self.public_memory.get_past_messages()[-2]["content"] + "\n" + self.public_memory.get_past_messages()[-1]["content"] + "\n"
        query = follow_up_prompts.format(chat_his=query)
        full_response = ""
        async for chunk in self.intent_llm.yield_chat(query):
            full_response += chunk['content']
            yield chunk['content']

    def convert_query_with_link_to_frontend_ref(self, data):
        results = []
        i = 1
        for key, articles in data.items():
            for _, article in articles.items():
                result = {"index": i, "title": article["title"], "url": article["link"], "url_source": "", "icon": ""}
                if "wiki" in result["title"] or result["title"] == "":
                    continue
                results.append(result)
                i += 1
        return results

    @staticmethod
    async def pure_web_search(query: str, tools: List[WebSearch], search_nums=10):
        action_executor = ActionExecutor(tools)

        tasks = []
        tasks.append(action_executor("web_search", query, search_nums))
        results = await asyncio.gather(*tasks)

        temp_results = {}
        temp_results[query] = results[0]
        query_with_link, fields_data, query_snippets, query_links = stage1_format2query_with_link(temp_results, cached_link_first=False, include_title=False)
        return query_with_link

    async def chat(self, request: WebChatQueryRequest):
        try:
            query = request.query
            message_id = request.message_id
            request_id = request.request_id
            model_name = request.model_name
            resource = request.resource

            ####################################### 输入接入nova检测敏感词#######################################
            session_id = str(uuid.uuid4())
            user_id = "user_001"  # 车舱使用
            ext_info = {
                "model": model_name,
                "resource": resource,
                "user_id": user_id,
            }
            if self.detect:
                request_data = TextModerationRequest(
                    self.sensitive_config["ak"], self.sensitive_config["sk"], self.sensitive_config["app_id"], user_id, query, "LLMPrompt", session_id, ext_info=ext_info
                )
                if await moderate_text_generic(str(request_id), request_data, "输入"):
                    response = {"data": f"request_id:{request_id} 输入存在敏感词或调用频率过高", "type": "error", "messageId": message_id}
                    yield response
                    return

            ####################################### 输出接入nova检测敏感词，入参定义#######################################
            text_id = str(uuid.uuid4())
            request_data = TextModerationRequest(
                self.sensitive_config["ak"],
                self.sensitive_config["sk"],
                self.sensitive_config["app_id"],
                user_id,
                "",
                "LLMStreamResponse",
                session_id,
                text_id=text_id,
                finish_detect=False,
                ext_info=ext_info,
            )
            ####################################### 输出接入nova检测敏感词，入参定义#######################################

            llm_responds = ""
            query_with_link = None

            intent_json_output = await self.stage0_intent_rewrite(query)

            # stage0_guide_task = asyncio.create_task(self.stage0_guide(query))
            # stage0_intent_rewrite_task = asyncio.create_task(self.stage0_intent_rewrite(query))

            # 放到下面light中
            # guiding_res = await stage0_guide_task
            # logger.info(f"stage0_guide result: {guiding_res}")

            # intent_json_output = await stage0_intent_rewrite_task
            logger.info("stage0_intent_rewrite_result FINISH", extra={"content": {"result": intent_json_output}})
            if len(intent_json_output[query]) > 3:
                intent_json_output[query] = intent_json_output[query][:2]

            # intent_json_output = await self.stage0_intent_rewrite(query)
            # logger.info(f"stage0_intent_rewrite result: {intent_json_output}")
            if search_list := intent_json_output[query]:
                keywords = {"data": search_list, "type": "keywords", "messageId": message_id}

                yield keywords

                # loop = asyncio.get_running_loop()
                # with ThreadPoolExecutor() as executor:
                #     guiding_task = loop.run_in_executor(
                #         executor, self.stage0_guide, query
                #     )
                #     guiding_res = await guiding_task

                # guiding_res = await self.stage0_guide(query)
                # logger.info(f"stage0_guide result: {guiding_res}")

                stage1_task = asyncio.create_task(self.stage1_web_search(search_list))
                query_with_link, fields_data, query_snippets, query_links = await stage1_task
                # search_engine_error, query_with_link = await self.stage1_web_search(search_list)
                logger.info("stage1_web_search FINISH")
                if all(not value for value in query_with_link.values()):
                    logger.warning("所有搜索引擎都没搜到")

                    frontend_ref = self.convert_query_with_link_to_frontend_ref(query_with_link)
                    logger.debug(f"frontend_ref: {frontend_ref}")
                    sources = {"data": frontend_ref, "type": "sources", "messageId": message_id}

                    yield sources
                    llm_responds = ""
                    async for chunk in self.stage4_llm_summary(query, "None", "None", request_id, request_data, search=False):
                        llm_responds += chunk
                    result = {"data": llm_responds, "type": "message", "messageId": message_id}
                    yield result

                    result = {"type": "messageEnd", "messageId": message_id}
                    yield result
                    return
                    # 这个逻辑你自己改, 一个函数可以yield多次，用next()接收就行。你可以先yield llm_whole_ref, frontend_ref, title_link_str, rest_ref再 yield chunk
                if self.topk_method is None:  # mdoe 为 light
                    guiding_res = ""  # await stage0_guide_task
                    logger.info(f"stage0_guide result: {guiding_res}")

                    frontend_ref = self.convert_query_with_link_to_frontend_ref(query_with_link)
                    logger.info(f"frontend_ref: {frontend_ref}")
                    sources = {"data": frontend_ref, "type": "sources", "messageId": message_id}

                    yield sources
                    llm_whole_ref = stage3_format_rest_query_with_link(query_with_link, 0)
                    async for chunk in self.stage4_llm_summary(query, llm_whole_ref, "None", request_id, request_data, guiding_res, search=False):
                        # logger.info(chunk, end="")
                        llm_responds += chunk
                        result = {"data": chunk, "type": "message", "messageId": message_id}
                        yield result

                    logger.info(f"stage4 result: {llm_responds}")
                    # return llm_responds, query_with_link, intent_json_output
                else:
                    frontend_ref = self.convert_query_with_link_to_frontend_ref(query_with_link)
                    # logger.info(f"frontend_ref: {frontend_ref}")
                    sources = {"data": frontend_ref, "type": "sources", "messageId": message_id}

                    yield sources
                    llm_main_ref, llm_rest_ref,error_scrape_rate = await self.stage2_3(query, query_with_link, query_snippets, query_links)
                    llm_whole_ref = llm_main_ref + llm_rest_ref
                    fetch = {"data": llm_whole_ref, "type": "fetch", "messageId": message_id}
                    yield fetch
                    # logger.debug(
                    #     f"stage2_3 result: \n\n {llm_whole_ref} \n\n {rest_ref}")

                    
                    llm_responds = ""
                    async for chunk in self.stage4_llm_summary(query, llm_whole_ref, llm_rest_ref, request_id, request_data, search=True):
                        llm_responds += chunk  # 将每个chunk拼接到llm_responds中

                    result = {"query":query,"keywords":search_list,"data": llm_responds, "type": "message", "messageId": message_id,"error_scrape_rate":error_scrape_rate,"llm_whole_ref":llm_whole_ref}
                    yield result  


                    # logger.info(f"stage4 result: {llm_responds}")
                # result = await self.stage5_follow_up_question()
                # logger.info(f"stage5 result: {result}")
            else:
                sources = {"data": [], "type": "sources", "messageId": message_id}

                yield sources
                llm_responds = ""

                async for chunk in self.stage4_llm_summary(query, "None", "None", request_id, request_data, search=False):
                    llm_responds += chunk
                result = {"query":query,"keywords":search_list,"data": llm_responds, "type": "message", "messageId": message_id,"error_scrape_rate":"","llm_whole_ref":""}
                yield result

            result = {"type": "messageEnd", "messageId": message_id}
            yield result

        except Exception as e:
            logger.info(e)
            import traceback

            traceback.print_exc()



from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import asyncio
import secrets

from src.utils.llms.openai_SDK import OpenAI_LLM
from src.utils.actions.tool_actions.bocha import BoCha
from src.utils.actions.tool_actions.serper import Serper
from src.utils.actions.tool_actions.searxng import SearXng
from src.utils.actions.tool_actions.web_search import WebSearch
from src.utils.memory.memory_base import (
    AllMessageMemory,
    ZeroMessageMemory,
    KMessageMemory,
    MemoryBase,
)
from src.utils.actions.tool_actions.action_excutor import ActionExecutor
from src.utils.actions.other_actions.embedding_topk import Piccolo
from src.utils.actions.other_actions.agent_topk import AgentTopK
from src.utils.actions.other_actions.rerank_topk import Rerank


app = FastAPI()

class QueryRequest(BaseModel):
    query: str


global_config = {
    "rewrite": {"api_key": "EMPTY", "base_url": "http://101.230.144.204:9999/v1"}
}
selected_llm = {"api_key": "ab9edab4-8780-11ef-a760-cbde10bdf191", "base_url": "http://180.184.148.156:8000/v1"}

model_info = {
    "rewriter": {"api_key": global_config["rewrite"]["api_key"], "base_url": global_config["rewrite"]["base_url"], "temperature": 0, "top_p": 0.5, "max_tokens": 4096},
    "summary": {"api_key": selected_llm["api_key"], "base_url": selected_llm["base_url"], "temperature": 0.1, "top_p": 0.5, "max_tokens": 4096},
}

summary_mem = ZeroMessageMemory()
k = 5
rest_k = 0
global_config = configuration.config
sougou_se = SouGou(sougou_config=global_config["search_engine"]["sougou"], sougou_full_config=global_config["search_engine"]["sougou_full"], engine_name="sougou")
bing_se = Bing(bing_config=global_config["search_engine"]["bing"], engine_name="bing")
serper_se = Serper(global_config["search_engine"]["serper"], "google")
tools = [WebSearch(search_engines=[bing_se])]
limit_scraping_time = "1300ms"

crawler = WebScrape(global_config["scrape"], limit_scraping_time=limit_scraping_time)
tool_executor = ActionExecutor(tools)
public_memory = AllMessageMemory()

rerank_module = Rerank(k, global_config["rerank"]["base_url"])
intent_llm = OpenAI_LLM(model_info["rewriter"])
summary_llm = OpenAI_LLM(model_info["summary"])
web = WebSearchAgent(intent_llm, summary_llm, rerank_module, tool_executor, public_memory, crawler, sensitive_config=global_config["sensitive"],router_system_prompt=router7b_system_prompt,router_user_prompt = router7b_user_prompt)


@app.post("/query")
async def query(request: QueryRequest):
    query = request.query
    web_chat_query_request = WebChatQueryRequest(query=query, message_id=secrets.token_hex(7))

    try:
        async for response in web.chat(web_chat_query_request):
            if "type" in response and response["type"] == "message" and "data" in response:
                return {"query":query,"keywords":response["keywords"],"result": response["data"],"llm_whole_ref":response["llm_whole_ref"],"error_scrape_rate":response["error_scrape_rate"]}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

## uvicorn samples.rewrite_end2end_test:app --reload --host 0.0.0.0 --port 3456