import pandas as pd
import json


def json_to_excel(filename, excel_name):
    with open(filename, "r", encoding="utf-8") as file:
        data = json.load(file)

        # 将JSON数据转换为DataFrame
        df = pd.json_normalize(data)

        # 选择并重命名所需的列
        df_selected = df.loc[:, ["query", "keywords", "tts"]]
        df_selected.columns = ["Query", "Keywords", "TTS"]

        # 将 'Keywords' 列中的列表转换为字符串
        df_selected["Keywords"] = df_selected["Keywords"].apply(lambda x: ", ".join(x))

        # 将数据保存到Excel文件
        df_selected.to_excel(excel_name, index=False)
        print("JSON数据转换为Excel文件成功！")
    return True


def load_results_from_file(filename):
    with open(filename, "r", encoding="utf-8") as f:
        return json.load(f)


def calculate_average_times(results):
    total_times = {
        "intent": 0,
        "web_search": 0,
        "ranking": 0,
        "scarping": 0,
        "rank&scarp": 0,
        "first_char": 0,
        "sum_first_char": 0,
    }
    count = {
        "intent": 0,
        "web_search": 0,
        "ranking": 0,
        "scarping": 0,
        "rank&scarp": 0,
        "first_char": 0,
        "sum_first_char": 0,
    }

    for result in results:
        timings = result.get("timing", {})
        for key in total_times.keys():
            if key in timings and timings[key] is not None:
                total_times[key] += timings[key]
                count[key] += 1

    average_times = {
        key: (total_times[key] / count[key]) if count[key] > 0 else 0
        for key in total_times.keys()
    }
    return average_times

def save_results_to_file(results, filename):
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=4, ensure_ascii=False)

def main():
    filename = "hybrid_reranker_xm1014_1015a_v1.json"
    excel_name = "hybrid_reranker_xm1014_1015a_v1.xlsx"
    results = load_results_from_file(filename)

    with open("test_set/xm1014.txt", "r") as f:
        lines = f.readlines()
    
    new_results = []
    # print(len(results))
    for line in lines:
        query = line.strip()
        flag = False
        for res in results:
            if res['query'] == query:
                new_results.append(res)
                flag = True
        if not flag:
            print(f"{query} not found")
            break
    # print(len(lines))
    # print(len(results))
    print(len(new_results))

    save_results_to_file(new_results, "hybrid_reranker_xm1014_1015a_v1_new.json")
        
    # average_times = calculate_average_times(new_results)

    # print("Average Times:")
    # for key, value in average_times.items():
    #     print(f"{key}: {value:.4f} s")

    excel = json_to_excel("hybrid_reranker_xm1014_1015a_v1_new.json", excel_name)


if __name__ == "__main__":
    main()