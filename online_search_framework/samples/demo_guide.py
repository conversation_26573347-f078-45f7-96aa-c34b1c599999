import os

import toml
from starlette import status

if True:
    config = toml.load('config.toml')
    os.environ['POSTGRES_HOST'] = config['database']['host']
    os.environ['POSTGRES_PORT'] = config['database']['port']
    os.environ['POSTGRES_USER'] = config['database']['user']
    os.environ['POSTGRES_PASSWORD'] = config['database']['password']
    os.environ['POSTGRES_DATABASE'] = config['database']['db_name']
from typing import Union
from src.utils.llms.llm_base import LLMBase
from src.utils.actions.other_actions.topk_base import TopkBase
from src.utils.actions.tool_actions.action_excutor import ActionExecutor
from src.prompts.router_prompts import router_system_zh, router_user_zh
from src.utils.utils import (
    stage0_parse_intent,
    stage1_convert2query_with_link,
    stage3_format_web_fetch,
    stage3_format_rest_query_with_link,
    extract_tsl_query_with_link,
    adjust_ranking_sum_to_list_minus_one,
    cal_time,
)
from src.utils.web_content_scrape import WebScrape

from src.prompts.guide_prompts import (
    guide_system_prompt_zh,
    guide_user_prompt_zh,
)

import asyncio
from concurrent.futures import ThreadPoolExecutor
from src.prompts.follow_up_prompts import (
    follow_up_prompts,
)
from src.prompts.summary_guide_prompts import (
    search_summary_guide_system_prompt_zh,
    search_summary_guide_user_prompt_zh,
)

class WebSearchAgent:
    def __init__(
        self,
        intent_llm: LLMBase,
        topk_method: Union[LLMBase, TopkBase, None],
        summary_llm: LLMBase,
        follow_up_llm: Union[LLMBase, None],
        guide_llm: Union[LLMBase, None],
        action_executor: ActionExecutor,
        limit_scarping_time=0.5,
    ) -> None:
        self.action_executor = action_executor
        self.intent_llm = intent_llm
        self.topk_method = topk_method
        self.summary_llm = summary_llm
        self.follow_up_llm = follow_up_llm
        # self.intent_llm.memory.add_message(router_system_zh, add_type="system")
        # self.summary_llm.memory.add_message(search_summary_system_prompt_zh, add_type="system")
        self.crawler = WebScrape("windows", limit_scarping_time)
        self.guide_llm = guide_llm

    @cal_time("stage0_intent_rewrite")
    def stage0_intent_rewrite(self, query):
        input_query = router_user_zh.format(user_input=query)
        intent_return = self.intent_llm.chat(input_query, stream=True)
        intent_json_output = stage0_parse_intent(query, intent_return)
        return intent_json_output
    
    @cal_time("stage0_guide")
    def stage0_guide(self, query):
        guide_user_prompt = guide_user_prompt_zh.format(
            user_input=query
        )
        res = self.guide_llm.chat(guide_user_prompt, stream=False)
        return res

    @cal_time("stage1_web_search")
    async def stage1_web_search(self, search_queries):
        """
        Args:
            search_queries: ["今日 深圳 天气"] or ["今日 深圳 天气", "今日 北京 天气"] if user's initial query is a complex query.

        Returns:

        """
        temp_results = {}
        tasks = []
        for single_query in search_queries:
            tasks.append(self.action_executor("web_search", single_query))
        results = await asyncio.gather(*tasks)
        for i, single_query in enumerate(search_queries):
            temp_results[single_query] = results[i]
        return stage1_convert2query_with_link(temp_results)

    @cal_time("stage2_3")
    async def stage2_3(self, query_with_link):
        titles, snippets, links,dates = extract_tsl_query_with_link(query_with_link)

        links_lens = [
            len(sub_list) for sub_list in links
        ]  #  [[1, 2], [3, 4], [5]] -> [2, 2, 1]
        links = [item for sublist in links for item in sublist]
        loop = asyncio.get_running_loop()
        scarping = asyncio.create_task(self.crawler.scrape(links))
        with ThreadPoolExecutor() as executor:
            ranking_task = loop.run_in_executor(
                executor, self.topk_method.ranking, query_with_link
            )
            ranking_res = await ranking_task
        scarping_res = await scarping

        topk_idxs = adjust_ranking_sum_to_list_minus_one(ranking_res, links_lens)
        topk_scarping_res = [scarping_res[i] for i in topk_idxs]
        topk_snippets = []
        topk_links = []
        topk_titles = []
        topk_date = []
        for query, topk_idx_list in ranking_res.items():
            for i, idx in enumerate(topk_idx_list):
                idx = int(idx)
                link = query_with_link[query][idx]["link"]
                title = query_with_link[query][idx]["title"]
                snippet = query_with_link[query][idx]["snippet"]
                date = query_with_link[query][idx]["date"]
                topk_snippets.append(snippet)
                topk_titles.append(title)
                topk_links.append(link)
                topk_date.append(date)
                del query_with_link[query][idx]
        llm_ref, frontend_ref,false_total = stage3_format_web_fetch(
            topk_titles,
            topk_snippets,
            topk_links,
            topk_date,
            topk_scarping_res,
            truncate_length=3000,
        )
        rest_ref = stage3_format_rest_query_with_link(query_with_link)
        return (
            llm_ref,
            frontend_ref,
            false_total,
            rest_ref,
        )  # rest_ref剩下没打开的snippet

    def stage4_llm_summary(self, summary_agent, query, reference_content_, rest_ref, pre_text=""):
        summary_user_prompt = search_summary_guide_user_prompt_zh.format(
            web_content=reference_content_, snippets=rest_ref, user_input=query, pre_text=pre_text
        )

        #print(summary_user_prompt)
        res = summary_agent.chat(summary_user_prompt, stream=True)
        return res

    async def chat(self, query: str):
        intent_json_output = self.stage0_intent_rewrite(query)
        if search_list := intent_json_output[query]:
            # search_engine_error,query_with_link = await self.stage1_web_search(search_list)
            loop = asyncio.get_running_loop()
            searching = asyncio.create_task(self.stage1_web_search(search_list))
            with ThreadPoolExecutor() as executor:
                guiding_task = loop.run_in_executor(
                    executor, self.stage0_guide, query
                )
                guiding_res = await guiding_task
            search_engine_error, query_with_link = await searching
            print(f"guiding_res: {guiding_res}")

            if search_engine_error:
                print(search_engine_error) ####搜索引擎结果有问题
                llm_responds = self.stage4_llm_summary(
                    self.summary_llm, query, "搜索引擎错误", "搜索引擎错误"
                )
                return llm_responds
            if self.topk_method:
                llm_ref, frontend_ref, false_total, rest_ref = (
                    await self.stage2_3(query_with_link)
                )
                llm_responds = self.stage4_llm_summary(
                    self.summary_llm, query, llm_ref, rest_ref, guiding_res
                )
                print(f"爬取错误 {false_total} out of {len(search_list)*k}")
            else:
                llm_whole_ref = stage3_format_rest_query_with_link(query_with_link)
                llm_responds = self.stage4_llm_summary(
                    self.summary_llm, query, llm_whole_ref, "None", guiding_res
                )

        else:
            llm_responds = self.stage4_llm_summary(
                self.summary_llm, query, "None", "None"
            )


if __name__ == "__main__":
    from src.utils.llms.openai_SDK import OpenAI_LLM
    from src.utils.actions.tool_actions.bocha import BoCha
    from src.utils.actions.tool_actions.serper import Serper
    from src.utils.actions.tool_actions.searxng import SearXng
    from src.utils.actions.tool_actions.web_search import WebSearch
    from src.utils.memory.memory_base import (
        AllMessageMemory,
        ZeroMessageMemory,
    )
    from src.utils.actions.tool_actions.action_excutor import ActionExecutor
    from src.utils.actions.other_actions.embedding_topk import Piccolo
    from src.utils.actions.other_actions.agent_topk import AgentTopK
    from src.prompts.follow_up_prompts import (
        follow_up_prompts,
    )
    from src.prompts.summary_prompts import (
        search_summary_system_prompt_zh,
    )
    guide_mem, intent_mem, topk_mem, summary_mem = (
        ZeroMessageMemory(),
        AllMessageMemory(),
        ZeroMessageMemory(),
        AllMessageMemory(),
    )
    follow_up_llm = OpenAI_LLM("qwen2-72b-instruct-int8", 1, 0.7)
    tools = [WebSearch(search_engines=[Serper("google")])]
    # tools = [WebSearch(search_engines=[BoCha()])]
    #### qwen2-72b-instruct-fp16,qwen2-72b-instruct-int8, qwen2-72b-instruct-int4
    version = "turbo"
    limit_scarping_time = 0.5
    guide_llm = OpenAI_LLM("qwen2-72b-instruct-int8", 0, 0.7, guide_mem)
    guide_llm.memory.add_message(guide_system_prompt_zh, message_type="system")

    k = 10
    topk_embd = Piccolo(k)
    model_name = "qwen2-72b-instruct-int8"
    intent_llm = OpenAI_LLM(model_name, 1, 0.7, intent_mem)
    agent_topk_llm = AgentTopK(OpenAI_LLM(model_name, 0, 0.7, AllMessageMemory()), k)
    summary_llm = OpenAI_LLM(model_name, 1, 0.7, summary_mem)
    intent_llm.memory.add_message(router_system_zh, message_type="system")
    summary_llm.memory.add_message(search_summary_system_prompt_zh, message_type="system")

    tool_executor = ActionExecutor(tools)
    if version == "light":
        web = WebSearchAgent(
            intent_llm, None, summary_llm, follow_up_llm,guide_llm, tool_executor, limit_scarping_time
        )
    elif version == "turbo":
        web = WebSearchAgent(
            intent_llm, topk_embd, summary_llm, follow_up_llm,guide_llm, tool_executor, limit_scarping_time
        )
    elif version == "prime":
        web = WebSearchAgent(
            intent_llm, topk_embd, summary_llm, follow_up_llm,guide_llm, tool_executor, limit_scarping_time
        )
    else:
        print("version not supported")

    asyncio.run(web.chat("今年太空授课，太空老师都有谁"))
