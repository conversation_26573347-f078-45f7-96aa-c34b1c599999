import json
from argparse import ArgumentParser

import requests


def web_search(url, input_data):
    payload = json.dumps(input_data)

    response = requests.request("POST", url, data=payload)

    response = json.loads(response.text)
    response = json.dumps(response, indent=4, separators=(',', ':'), ensure_ascii=False)
    print(response)


def parse_args():
    parser = ArgumentParser()
    parser.add_argument("hostname", nargs="?", default="localhost")
    parser.add_argument("--port", type=int, default=8080)
    return parser.parse_args()


if __name__ == "__main__":
    # event_type = sys.argv[1]
    args = parse_args()
    hostname = args.hostname
    port = args.port
    url = f"http://{hostname}:{port}/v1.0/web_search"
    print(f"url: {url}")

    input_data = {
        "query": "我国国产大飞机什么时候正式投入飞行",
        "model_version": "turbo",
        "save": False
    }
    print(input_data.keys())
    web_search(url, input_data)
