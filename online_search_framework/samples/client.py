from datetime import datetime, timedelta, timezone

import jwt
import requests

ak = "eda66dbd-6aca-48bd-86d5-66a9709feaff"
sk = "e7f3e6b7-fe3d-4e2b-acd8-11a0672edc4f"


def create_access_token(ak: str, sk: str) -> str:
    expire = datetime.now(timezone.utc) + timedelta(days=7)  # 这里可以设置过期时间
    nbf = datetime.now(timezone.utc) - timedelta(minutes=1)
    to_encode = {"iss": ak, "exp": expire, "nbf": nbf}
    encoded_jwt = jwt.encode(to_encode, sk, algorithm="HS256")
    return encoded_jwt


token = create_access_token(ak, sk)


backend = "https://chatbot.sensetime.com:8007"


headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer %s' % token
}

url = f"{backend}/simple/web_search/v2"

payload = {
    "engine": "hybrid",  # sougou / bing / google
    "query": "京东和杨笠发生了什么",
    "stream": False
}

response = requests.post(url, headers=headers, json=payload)

print(response.json())
