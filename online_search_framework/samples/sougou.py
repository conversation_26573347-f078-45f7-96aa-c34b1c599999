import requests
import json

server = "http://10.112.97.25:32015/custom"

def Query():
    url = "{}/search".format(server)
    data = {
        "query": "阿里巴巴董事长是谁",
        "topk": 10,
        "onlineMod": "OM_Special",
        "timeoutMs": 8000,
        "siteMod": 3,
        "slots": {"search_without_cache": True},
    }
    headers = {
        "Content-type": "application/json",
        "X-Sensenova-Signature": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiIyUzZsekp5Y0xFc054WUpZZWFZU0hqdEFvbWwiLCJleHAiOjE3MzE3NDc1ODcsIm5iZiI6MTcwMDIxMTU4Mn0.GW6SRzmOFTUYpa2uxdRr78a5-LUiWmCN3LZrWoIwGrw"
    }
    resp = requests.post(url, data=json.dumps(data), headers=headers)
    print(resp.text)

Query()