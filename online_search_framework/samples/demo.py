from typing import Union
from src.utils.llms.llm_base import LLMBase
from src.utils.memory.memory_base import MemoryBase
from src.utils.actions.other_actions.topk_base import TopkBase
from src.utils.actions.tool_actions.action_excutor import ActionExecutor
from src.prompts.router_prompts import router_system_zh, router_user_zh
from src.utils.llms.nova_SDK import Nova_LLM
from src.utils.utils import (
    stage0_parse_intent,
    stage1_convert2query_with_link,
    stage3_format_web_fetch,
    stage3_format_rest_query_with_link,
    extract_tsl_query_with_link,
    adjust_ranking_sum_to_list_minus_one,
    cal_time,
)
from src.utils.web_content_scrape import WebScrape
from src.prompts.summary_prompts import (
    search_summary_system_prompt_zh,
    search_summary_user_prompt_zh,
)
from src.prompts.follow_up_prompts import (
    follow_up_prompts,
)
import async<PERSON>
from concurrent.futures import ThreadPoolExecutor
import json
from datetime import datetime
import os
from tqdm import tqdm
from loguru import logger

class WebSearchAgent:
    def __init__(
            self,
            intent_llm: LLMBase,
            topk_method: Union[LLMBase, TopkBase, None],
            summary_llm: LLMBase,
            follow_up_llm: Union[LLMBase, None],
            memory: MemoryBase,
            action_executor: ActionExecutor,
            limit_scarping_time=0.5,
    ) -> None:
        self.action_executor = action_executor
        self.intent_llm = intent_llm
        self.topk_method = topk_method
        self.summary_llm = summary_llm
        self.follow_up_llm = follow_up_llm
        self.memory = memory
        self.memory.add_message(router_system_zh, message_type="system")
        self.crawler = WebScrape("windows", limit_scarping_time)

    def truncate_string(self, s):
        start_index_current_issue = s.find("当前问题:")
        if start_index_current_issue == -1:
            return s
        current_issue_text = s[start_index_current_issue:]
        return current_issue_text

    @cal_time("stage0_intent_rewrite")
    async def stage0_intent_rewrite(self, query):
        input_query = router_user_zh.format(user_input=query)
        print(f"input_query: {input_query}")
        # self.intent_messages.append({"role": "system", "content": router_system_zh.format(formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M"))})
        messages = self.memory.get_past_messages()
        messages[0] = {"role": "system", "content": router_system_zh.format(formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M"))}
        messages.append({"role": "user", "content": input_query})
        # print(f"messages: {messages}")
        intent_return = await self.intent_llm.chat(messages, stream=True)
        self.memory.add_message(query, message_type="user")
        # self.intent_messages.append({"role": "assistant", "content": intent_return})

        print(f"intent_return: {intent_return}")
        intent_json_output = stage0_parse_intent(query, intent_return)
        return intent_json_output

    @cal_time("stage1_web_search")
    async def stage1_web_search(self, search_queries):
        """
        Args:
            search_queries: ["今日 深圳 天气"] or ["今日 深圳 天气", "今日 北京 天气"] if user's initial query is a complex query.

        Returns:

        """
        temp_results = {}
        tasks = []
        for single_query in search_queries:
            tasks.append(self.action_executor("web_search", single_query))
        results = await asyncio.gather(*tasks)
        for i, single_query in enumerate(search_queries):
            temp_results[single_query] = results[i]
        return stage1_convert2query_with_link(temp_results)

    @cal_time("stage2_3")
    async def stage2_3(self, query_with_link):
        # print(query_with_link)
        titles, snippets, links, dates = extract_tsl_query_with_link(query_with_link)

        links_lens = [
            len(sub_list) for sub_list in links
        ]  # [[1, 2], [3, 4], [5]] -> [2, 2, 1]
        links = [item for sublist in links for item in sublist]
        loop = asyncio.get_running_loop()
        # logger.info(links)
        scarping = asyncio.create_task(self.crawler.scrape(links))
        with ThreadPoolExecutor() as executor:
            ranking_task = loop.run_in_executor(
                executor, self.topk_method.ranking, query_with_link
            )
            ranking_res = await ranking_task
        scarping_res = await scarping
        print(f"scarping_res: {scarping_res[0]}")

        topk_idxs = adjust_ranking_sum_to_list_minus_one(ranking_res, links_lens)
        topk_scarping_res = [scarping_res[i][0] for i in topk_idxs]
        image_url = [scarping_res[i][1] for i in range(len(scarping_res))]

        topk_snippets = []
        topk_links = []
        topk_titles = []
        topk_date = []
        for query, topk_idx_list in ranking_res.items():
            for i, idx in enumerate(topk_idx_list):
                idx = int(idx)
                link = query_with_link[query][idx]["link"]
                title = query_with_link[query][idx]["title"]
                snippet = query_with_link[query][idx]["snippet"]
                date = query_with_link[query][idx]["date"]
                topk_snippets.append(snippet)
                topk_titles.append(title)
                topk_links.append(link)
                topk_date.append(date)
                del query_with_link[query][idx]
        llm_ref, frontend_ref, false_total = stage3_format_web_fetch(
            topk_titles,
            topk_snippets,
            topk_links,
            topk_date,
            topk_scarping_res,
            truncate_length=2000,
        )
        rest_ref = stage3_format_rest_query_with_link(query_with_link)
        return (
            llm_ref,
            frontend_ref,
            false_total,
            rest_ref,
            image_url,
        )  # rest_ref剩下没打开的snippet

    async def stage4_llm_summary(self, query, reference_content_, rest_ref):
        summary_user_prompt = search_summary_user_prompt_zh.format(
            web_content=reference_content_, snippets=rest_ref, user_input=query
        )
        messages = self.memory.get_past_messages()
        messages = messages[:-1]
        messages[0] = {"role": "system", "content": search_summary_system_prompt_zh.format(formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M"))}
        messages.append({"role": "user", "content": summary_user_prompt})
        summary_res = await self.summary_llm.chat(messages, stop_words=["注释:", "注释：", "参考文献：", "参考文献:", "\n```"], stream=True)
        self.memory.add_message(summary_res, message_type="assistant")
        return summary_res

    async def stage5_follow_up_question(self):
        memory = self.summary_llm.memory.get_all_messages()
        query = memory[-2]["content"] + "\n" + memory[-1]["content"] + "\n"
        query = follow_up_prompts.format(chat_his=query)
        return await self.follow_up_llm.chat(query, stream=True)

    async def chat(self, query: str):
        intent_json_output = await self.stage0_intent_rewrite(query)
        if search_list := intent_json_output[query]:
            print("sl", search_list)
            search_engine_error, query_with_link = await self.stage1_web_search(search_list)
            if search_engine_error:
                print(search_engine_error)  ####搜索引擎结果有问题
                llm_responds = await self.stage4_llm_summary(
                    query, "搜索引擎错误", "搜索引擎错误"
                )
                # return llm_responds
            if self.topk_method:
                llm_ref, frontend_ref, false_total, rest_ref, image_url = (
                    await self.stage2_3(query_with_link)
                )
                llm_responds = await self.stage4_llm_summary(
                    query, llm_ref, rest_ref
                )
                # print(f"爬取错误 {false_total} out of {len(search_list) * k}")
            else:
                llm_whole_ref = stage3_format_rest_query_with_link(query_with_link)
                llm_responds = await  self.stage4_llm_summary(
                    query, llm_whole_ref, "None"
                )
            # await self.stage5_follow_up_question()

        else:
            llm_responds = await self.stage4_llm_summary(
                query, "None", "None"
            )
        
        result_json = {
            "query": query,
            "keywords": intent_json_output[query],
            "tts": llm_responds
        }

        return result_json


from src.utils.llms.openai_SDK import OpenAI_LLM
from src.utils.actions.tool_actions.bocha import BoCha
from src.utils.actions.tool_actions.serper import Serper
from src.utils.actions.tool_actions.searxng import SearXng
from src.utils.actions.tool_actions.sougou import SouGou, SouGouBare
from src.utils.actions.tool_actions.web_search import WebSearch
from src.utils.memory.memory_base import (
    AllMessageMemory,
    ZeroMessageMemory,
    KMessageMemory
)
from src.utils.actions.tool_actions.action_excutor import ActionExecutor
from src.utils.actions.other_actions.embedding_topk import Piccolo
from src.utils.actions.other_actions.agent_topk import AgentTopK
from src.utils.actions.other_actions.rerank_topk import Rerank

def initialize():
    intent_mem, topk_mem, summary_mem = (
    ZeroMessageMemory(),
    ZeroMessageMemory(),
    ZeroMessageMemory(),
    )

    # tools = [WebSearch(search_engines=[SouGou("sougou_full")])]
    tools = [WebSearch(search_engines=[SouGouBare("sougou_full")])]
    # tools = [WebSearch(search_engines=[BoCha()])]
    # tools = [WebSearch(search_engines=[Serper("google")])]
    #### qwen2-72b-instruct-fp16,qwen2-72b-instruct-int8, qwen2-72b-instruct-int4
    version = "turbo"
    limit_scarping_time = 2
    k = 15
    momery_k = 10
    topk_embd = Piccolo(k)
    topk_rerank = Rerank(k)
    # model_name = "qwen2-72b-instruct-int8"
    model_name = "senseauto-v0.1.0-int8"

    memory = KMessageMemory(momery_k)

    intent_llm = OpenAI_LLM(model_name, 0, 0.7, summary_mem)
    # agent_topk_llm = AgentTopK(OpenAI_LLM(model_name, 0, 0.7, AllMessageMemory()), k)
    summary_llm = OpenAI_LLM(model_name, 0.7, 0.8, summary_mem)
    follow_up_llm = OpenAI_LLM("qwen2-72b-instruct-int8", 1, 0.7)

    tool_executor = ActionExecutor(tools)
    if version == "light":
        web = WebSearchAgent(
            intent_llm, None, summary_llm, follow_up_llm, memory, tool_executor, limit_scarping_time
        )
    elif version == "turbo":
        web = WebSearchAgent(
            intent_llm, topk_rerank, summary_llm, follow_up_llm, memory, tool_executor, limit_scarping_time
        )
    elif version == "prime":
        web = WebSearchAgent(
            intent_llm, topk_embd, summary_llm, follow_up_llm, memory, tool_executor, limit_scarping_time
        )
    else:
        print("version not supported")
        return None
    
    return web

async def main():
    web = initialize()
    if web is None: return

    while True:
        query = input("query: ")
        if query == "q": break

        await web.chat(query)

def load_results_from_file(filename):
    if os.path.exists(filename):
        with open(filename, "r", encoding="utf-8") as f:
            return json.load(f)
    return []

def save_results_to_file(results, filename):
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=4, ensure_ascii=False)

import time
async def test(save_file_name):
    with open("test_set/timeliness_184.txt", "r") as f:
        lines = f.readlines()
    
    results = load_results_from_file(save_file_name)

    for line in tqdm(lines):
        query = line.strip()
        web = initialize()

        if any(result["query"] == query for result in results):
            print(f"Query '{query}' already processed, skipping.")
            continue
        # time.sleep(5)
        try:
            result_json = await web.chat(query)
            results.append(result_json)
            save_results_to_file(results, save_file_name)
        except Exception as e:
            print(f"Error processing query '{query}': {e}")
            # save_partial_result(query, str(e), "error", "partial_results.json")
    save_results_to_file(results, save_file_name)

asyncio.run(main())
# asyncio.run(test("sougou_full_bare_senseautoint8_reranker_break=2s_184.json"))
# asyncio.run(web.chat("推荐十首周杰伦的歌"))

# async def main():
#     # while True:
#     #     query = input("query: ")
#     #     if query == 'q': break

#     #     await web.chat(query)

#     with open("test_set/timeliness.txt", "r") as f:
#         lines = f.readlines()
    
#     results = []
#     for line in lines:
#         query = line.strip()
#         memory = KMessageMemory(momery_k)
#         intent_llm = OpenAI_LLM(model_name, 0, 0.7, summary_mem)
#         # agent_topk_llm = AgentTopK(OpenAI_LLM(model_name, 0, 0.7, AllMessageMemory()), k)
#         summary_llm = OpenAI_LLM(model_name, 0.7, 0.8, summary_mem)
#         follow_up_llm = OpenAI_LLM("qwen2-72b-instruct-int8", 1, 0.7)

#         tool_executor = ActionExecutor(tools)
#         if version == "light":
#             web = WebSearchAgent(
#                 intent_llm, None, summary_llm, follow_up_llm, memory, tool_executor, limit_scarping_time
#             )
#         elif version == "turbo":
#             web = WebSearchAgent(
#                 intent_llm, topk_embd, summary_llm, follow_up_llm, memory, tool_executor, limit_scarping_time
#             )
#         elif version == "prime":
#             web = WebSearchAgent(
#                 intent_llm, topk_embd, summary_llm, follow_up_llm, memory, tool_executor, limit_scarping_time
#             )
#         else:
#             print("version not supported")

#         intent_json_output = await web.chat(query)

#         # print(intent_json_output)
#         results.append(intent_json_output)
    
#     with open("timeliness_keywords_res_new_prompt_1.json", "w", encoding="utf-8") as f:
#         json.dump(results, f, indent=4, ensure_ascii=False)

# asyncio.run(main())

