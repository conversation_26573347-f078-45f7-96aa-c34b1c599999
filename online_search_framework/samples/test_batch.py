import asyncio

from fastapi.responses import JSONResponse
from main import web_search_v2, web_search, pure_web_search, WebSearchReqV2, WebSearchReq, PureWebSearchReq
from schemas import UserInDBBase
from datetime import datetime
import json
from pprint import pprint
import sys

def test_web_search():
    payload: WebSearchReq = WebSearchReq(
        query="左航现在读高级了",
        mode="turbo",
        engine="bing"
    )
    user = UserInDBBase(
        email='<EMAIL>',
        created_at=datetime.now()
    )
    resp: JSONResponse = asyncio.run(web_search(payload, user))
    pprint(json.loads(resp.body))

async def test_web_search_v2(query, engine):
    payload: WebSearchReqV2 = WebSearchReqV2(
        model_info = {
            "rewriter": {
                "model_name": "senseauto-chat-v0.2.0",
                "temperature": 0,
                "top_p": 0.7,
                "max_tokens": 4096
            },
            "summary": {
                "model_name": "senseauto-chat-v0.2.0",
                "temperature": 0.1,
                "top_p": 0.5,
                "max_tokens": 4096
            }
        },
        query=query,
        engine=engine
    )
    user = UserInDBBase(
        email='<EMAIL>',
        created_at=datetime.now()
    )
    resp: JSONResponse = await web_search_v2(payload, user)
    # pprint(json.loads(resp.body))

    return json.loads(resp.body)


def test_pure_web_search():
    payload: PureWebSearchReq = PureWebSearchReq(
        query="左航现在读高级了",
        engine="bing"
    )
    user = UserInDBBase(
        email='<EMAIL>',
        created_at=datetime.now()
    )
    resp: JSONResponse = asyncio.run(pure_web_search(payload, user))
    pprint(json.loads(resp.body))

async def main():
    file_in = "badcase.xlsx"
    engine = "byte"
    df = pd.read_excel(file_in, sheet_name=0)
    row_count = df.shape[0]
    # row_count = 5

    for i in range(row_count):
        query = df.loc[i,"Query"]
        print(f"第{i}个问题，{query}")
        response = await test_web_search_v2(query, engine)
        print(response)

        df.loc[i,"answer"] = response["answer"]
        if "TTFT" in response["time_cost"].keys():
            df.loc[i,"TTFT"] = response["time_cost"]["TTFT"]
        else:
            df.loc[i,"TTFT"] = ""
        df.loc[i,"cost"] = str(response["time_cost"])
        json_path = os.path.join(os.path.dirname(file_in), "json_xiaomi_1030_new")
        os.makedirs(json_path, exist_ok=True)
        with open(os.path.join(json_path, '{}.json'.format(i)), 'w', encoding='utf-8') as f:
            f.write(json.dumps(response, ensure_ascii=False, indent=4))
    out_path = os.path.join(os.path.dirname(file_in), "result")
    os.makedirs(out_path, exist_ok=True)
    t = time.strftime("%Y%m%d%H%M%S")
    file_out = str(t) + "_api_xiaomi_" + str(engine) + "_" + os.path.basename(file_in)
    # test.to_excel(os.path.join(out_path,file_out))  # 有默认行索引
    df.to_excel(os.path.join(out_path, file_out), index=False)  # 无行索引

if __name__ == '__main__':
    from loguru import logger
    import pandas as pd
    import os
    import time
    logger.remove()
    logger.add(sys.stderr, level="INFO")
    asyncio.run(main())