from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime
import secrets
import uuid


class UserBase(BaseModel):
    email: EmailStr
    is_active: bool = True
    is_superuser: bool = False
    full_name: str = secrets.token_hex(10)


class UserCreate(UserBase):
    password: str


# Properties to receive via API on update
class UserUpdate(UserBase):
    password: Optional[str] = None


class UserInDBBase(UserBase):
    id: Optional[uuid.UUID] = None
    created_at: datetime


class UserOut(UserBase):
    id: uuid.UUID
    sk: str


class UserInDB(UserInDBBase):
    hashed_password: str


class Token(BaseModel):
    access_token: str
    token_type: str


class TokenPayload(BaseModel):
    iss: str
    exp: datetime
    nbf: datetime
