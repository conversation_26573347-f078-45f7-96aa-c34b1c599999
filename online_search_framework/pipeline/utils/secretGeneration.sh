kubectl delete secret gac-online-search-service-secret

# Those variables are stored in k8s secret called access-management-secret
kubectl create secret generic gac-online-search-service-secret \
    --from-file=SENSEAUTO_KEY=./auto_key.txt \
    --from-file=TENCENT_AK=./tencent_ak.txt \
    --from-file=TENCENT_SK=./tencent_sk.txt\
    --from-file=BING_KEY=./bing_key.txt \
    --from-file=REDIS_PASSWORD=./redis_pass.txt \
    --from-file=GAODE_API_KEY=./gaode_api_key.txt
# Verify
kubectl get secrets