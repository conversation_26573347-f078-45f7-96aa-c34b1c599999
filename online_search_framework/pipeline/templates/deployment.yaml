apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "license.fullname" . }}
  labels:
    {{- include "license.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "license.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "license.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "license.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          {{- range .Values.application.variables }}
            - name: {{ .name }}
              value: {{ .value | quote }}
          {{- end }}
          {{- range .Values.application.sensitiveVariable }}
            - name: {{ .name }}
              valueFrom:
                secretKeyRef:
                  name: {{ .valueFrom.secretKeyRef.name }}
                  key: {{ .valueFrom.secretKeyRef.key }}
                  optional: {{ .valueFrom.secretKeyRef.optional }}
          {{- end }}

          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          {{- if not .Values.podProbe.liveness.disabled }}
          livenessProbe:
            httpGet:
              path: {{ .Values.podProbe.liveness.path }}
              port: {{ .Values.podProbe.liveness.port }}
            failureThreshold: {{ .Values.podProbe.liveness.failureThreshold }}
            initialDelaySeconds: {{ .Values.podProbe.liveness.initialDelaySeconds }}
            periodSeconds: {{ .Values.podProbe.liveness.periodSeconds }}
            timeoutSeconds: {{ .Values.podProbe.liveness.timeoutSeconds }}
          {{- end }}
          {{- if not .Values.podProbe.readiness.disabled }}
          readinessProbe:
            httpGet:
              path: {{ .Values.podProbe.readiness.path }}
              port: {{ .Values.podProbe.readiness.port }}
            failureThreshold: {{ .Values.podProbe.readiness.failureThreshold }}
            initialDelaySeconds: {{ .Values.podProbe.readiness.initialDelaySeconds }}
            periodSeconds: {{ .Values.podProbe.readiness.periodSeconds }}
            timeoutSeconds: {{ .Values.podProbe.readiness.timeoutSeconds }}
          {{- end }}
          {{- if not .Values.podProbe.startUp.disabled }}
          startupProbe:
            httpGet:
              path: {{ .Values.podProbe.startUp.path }}
              port: {{ .Values.podProbe.startUp.port }}
            failureThreshold: {{ .Values.podProbe.startUp.failureThreshold }}
            periodSeconds: {{ .Values.podProbe.startUp.periodSeconds }}
            timeoutSeconds: {{ .Values.podProbe.startUp.timeoutSeconds }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
