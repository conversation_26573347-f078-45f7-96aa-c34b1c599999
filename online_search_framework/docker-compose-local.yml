services:
  osf:
    pull_policy: build  # build every time, will use cache
    build:
      context: ./
      dockerfile: ./Dockerfile

    ports:
      - "8080:8080"
    restart: always
    env_file:
      - .env
    environment:
      - SERVICE_NAME=${SERVICE_NAME}
      - SELECTED_MODEL=senseauto_dev  ## the llm model server in `config.yaml`
    # healthcheck:
    #   test: ["CMD", "python", "-c", "import requests; requests.get('http://127.0.0.1:8080/health')"]
    #   interval: 10s
    #   timeout: 2s
    #   retries: 3

  # pyright:
  #   image: registry.sensetime.com/cloud-platform-ocr/online_search_framework:${OSF_TAG}
  #   environment:
  #     - NPM_CONFIG_LOGLEVEL=silent
  #     - npm_config_registry=https://registry.npmmirror.com
