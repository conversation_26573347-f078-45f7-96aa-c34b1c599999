from typing import Annotated
import uuid
from fastapi import Depends, HTTPException, WebSocketException, status
from fastapi.security import OAuth2PasswordBearer
import jwt
from fastapi.security.utils import get_authorization_scheme_param
from sqlmodel import Session, select
from cachetools import cached, TTLCache

from service import security
from service.security import OAuth<PERSON><PERSON>eb<PERSON>cket<PERSON>earer, get_password_hash, verify_password
from db.engine import get_session
from db.entity import User, UserCreate
import schemas

reusable_oauth2 = OAuth2PasswordBearer(tokenUrl="/api/v1/user/access-token")
ws_oauth2 = OAuth2WebsocketBearer("/api/v1/user/access-token")

TokenDep = Annotated[str, Depends(reusable_oauth2)]
WsTokenDep = Annotated[str, Depends(ws_oauth2)]


@cached(
    cache=TTLCache(maxsize=4096, ttl=86400)
)  # 12h，缓存失效并不意味token失效，只是需要重新走一遍密码学验证流程
def get_current_user(token: TokenDep) -> User:
    try:
        payload = jwt.decode(token, options={"verify_signature": False})
    except Exception as _:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
        )
    token_data = schemas.TokenPayload(**payload)
    user = None
    with get_session() as session:
        try:
            user_id = uuid.UUID(token_data.iss, version=4)
            user = session.get(User, user_id)
        except Exception as _:
            pass
        if user is None:
            user = session.exec(
                select(User).where(User.ak == token_data.iss)
            ).one_or_none()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    try:
        jwt.decode(token, user.sk, algorithms=[security.ALGORITHM])
        return user
    except Exception as _:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
        )


def get_current_active_user(
    current_user: Annotated[User, Depends(get_current_user)],
) -> User:
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


CurrentUser = Annotated[User, Depends(get_current_active_user)]


# WsCurrentUser = Annotated[User, Depends(get_ws_current_active_user)]


def get_current_active_superuser(current_user: CurrentUser) -> User:
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=400, detail="The user doesn't have enough privileges"
        )
    return current_user


def create_user(*, user_create: UserCreate) -> User:
    with get_session() as session:
        db_obj = User.model_validate(
            user_create,
            update={"hashed_password": get_password_hash(user_create.password)},
        )
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
    return db_obj


def get_user_by_email(*, email: str) -> User | None:
    with get_session() as session:
        statement = select(User).where(User.email == email)
        session_user = session.exec(statement).first()
    return session_user


def authenticate(*, email: str, password: str) -> User | None:
    db_user = get_user_by_email(email=email)
    if not db_user:
        return None
    if not verify_password(password, db_user.hashed_password):
        return None
    return db_user
