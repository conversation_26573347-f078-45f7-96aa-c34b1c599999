#!/usr/bin/env python3

import jwt
from datetime import datetime, timedelta, timezone
import pandas as pd
from tqdm.asyncio import tqdm
import requests
import numpy as np
import aiohttp
from typing import Any, AsyncGenerator, Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, field
from argparse import ArgumentParser
import warnings
import traceback
import time
import sys
import resource
import random
import os
import json
import asyncio
import argparse


"""
python ./bench_serving_kami.py --dataset-path ./dataset.txt --num-prompts 1 --host *********** --port 8080 --request-rate 5

"""

AIOHTTP_TIMEOUT = aiohttp.ClientTimeout(total=6 * 60 * 60)

global args

ak = "eda66dbd-6aca-48bd-86d5-66a9709feaff"
sk = "e7f3e6b7-fe3d-4e2b-acd8-11a0672edc4f"

# 设置pandas DataFrame输出样式
pd.options.display.float_format = '{:.2f}'.format  # 保留两位小数
pd.options.display.max_columns = None  # 显示所有列


def create_access_token(ak: str, sk: str) -> str:
    expire = datetime.now(timezone.utc) + timedelta(days=7)  # 这里可以设置过期时间
    nbf = datetime.now(timezone.utc) - timedelta(minutes=1)
    to_encode = {"iss": ak, "exp": expire, "nbf": nbf}
    encoded_jwt = jwt.encode(to_encode, sk, algorithm="HS256")
    return encoded_jwt


TOKEN = create_access_token(ak, sk)


@dataclass
class RequestFuncInput:
    prompt: str
    extra_request_body: Dict[str, Any]
    request_id: int


@dataclass
class RequestFuncOutput:
    generated_text: str = ""
    success: bool = False
    latency: float = 0.0
    srv_latency: float = 0.0
    ttft: float = 0.0  # Time to first token
    keywords_overhead: float = 0.0
    sources_overhead: float = 0.0
    fetch_overhead: float = 0.0
    total_time: float = 0.0
    # List of inter-token latencies
    error: str = ""
    output_len: int = 0
    request_id: int = 0
    raw_response: dict = field(default_factory=dict)


async def async_request_kami(
    api_url: str,
    request_func_input: RequestFuncInput,
    pbar: Optional[tqdm] = None,
) -> RequestFuncOutput:
    # api_url = f"{backend}/v1/llm/chat-completions"    # 只有内网可以访问

    prompt = request_func_input.prompt

    async with aiohttp.ClientSession(timeout=AIOHTTP_TIMEOUT) as session:
        payload = {
            "model": args.model,
            "messages": [{
                "role": "user",
                "content": prompt
            }],
            "stream": args.stream,
            "dev": args.dev
        }
        headers = {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + TOKEN
        }

        # print(f"prompt: {prompt}")

        output = RequestFuncOutput()

        generated_text = ""
        st = time.perf_counter()
        try:
            async with session.post(
                url=api_url, json=payload, headers=headers
            ) as response:
                if response.status == 200:
                    js = await response.json()

                    # print(f"response: {js}")
                    # generated_text = js["answer"]
                    generated_text = js["data"]["choices"][0]["message"]
                    latency = time.perf_counter() - st
                    # xiaomi接口当中没有time_cost字段
                    # output.srv_latency = js["time_cost"]["total_time"]
                    # if "TTFT" in js["time_cost"]:
                    #     output.ttft = js["time_cost"]["TTFT"]
                    # output.keywords_overhead = js["time_cost"].get(
                    #     "keywords", 0.0)
                    # output.sources_overhead = js["time_cost"].get(
                    #     "sources", 0.0)
                    # output.fetch_overhead = js["time_cost"].get("fetch", 0.0)
                    # output.total_time = js["time_cost"].get("total_time", 0.0)

                    output.generated_text = generated_text
                    output.success = True
                    output.latency = latency
                    output.raw_response = js
                else:
                    print(
                        f"status is {response.status}, response: {response.content}")
                    output.error = response.reason or ""
                    output.success = False
        except Exception:
            output.success = False
            exc_info = sys.exc_info()
            output.error = "".join(traceback.format_exception(*exc_info))
            print(f"exception: {output.error}")

    if pbar:
        pbar.update(1)
    output.request_id = request_func_input.request_id
    return output


@dataclass
class BenchmarkMetrics:
    completed: int
    request_throughput: float
    mean_ttft_ms: float
    median_ttft_ms: float
    std_ttft_ms: float
    p99_ttft_ms: float
    mean_e2e_latency_ms: float
    median_e2e_latency_ms: float
    overheads: pd.DataFrame


def sample_sharegpt_requests(
    dataset_path: str,
    num_requests: int,
) -> List[str]:
    # Load the dataset.
    dataset = []
    with open(dataset_path) as f:
        dataset = [line.strip() for line in f]

    # Shuffle the dataset.
    random.shuffle(dataset)

    # Filter out sequences that are too long or too short
    filtered_dataset: List[str] = []
    for i in range(len(dataset)):
        if len(filtered_dataset) == num_requests:
            break

        # Tokenize the prompts and completions.
        prompt = dataset[i]
        filtered_dataset.append(prompt)

    return filtered_dataset


async def get_request(
    input_requests: List[str],
    request_rate: float,
) -> AsyncGenerator[str, None]:
    input_requests = iter(input_requests)
    for request in input_requests:
        yield request

        if request_rate == float("inf"):
            # If the request rate is infinity, then we don't need to wait.
            continue

        # Sample the request interval from the exponential distribution.
        interval = np.random.exponential(1.0 / request_rate)
        # The next request will be sent after the interval.
        await asyncio.sleep(interval)

# stats_ignore_zeros 函数适合处理数据中含有大量零值的情况（例如零代表缺失值或不感兴趣的项），在统计时自动忽略零值可以更准确地反映非零数据的分布
def stats_ignore_zeros(series):
    # 过滤出 series 中所有非零的元素，并将它们存储在 series_non_zero 中
    series_non_zero = series[series != 0] 
    return pd.Series({
        'count': series_non_zero.count(),
        'std': series_non_zero.std(),
        'mean': series_non_zero.mean(),
        'median': series_non_zero.median(),
        'p99': series_non_zero.quantile(0.99),
        'max': series_non_zero.max()
    })


def calculate_metrics(
    input_requests: List[str],
    outputs: List[RequestFuncOutput],
    dur_s: float,
) -> Tuple[BenchmarkMetrics, List[int]]:
    completed = 0
    ttfts: List[float] = []
    e2e_latencies: List[float] = []
    for i in range(len(outputs)):
        if outputs[i].success:
            ttfts.append(outputs[i].ttft)

            e2e_latencies.append(outputs[i].latency)

            completed += 1

    if completed == 0:
        warnings.warn(
            "All requests failed. This is likely due to a misconfiguration "
            "on the benchmark arguments.",
            stacklevel=2,
        )

    OVERHEADS = ['ttft', 'keywords_overhead',
                 'sources_overhead', 'fetch_overhead', 'total_time']
    overheads_df = dict()
    for overhead in OVERHEADS:
        overheads_df[overhead.replace("_overhead", "") + "(ms)"] = [
            output.__getattribute__(overhead) * 1000 for output in outputs]
    overheads_df = pd.DataFrame(overheads_df).apply(stats_ignore_zeros)

    metrics = BenchmarkMetrics(
        completed=completed,
        request_throughput=completed / dur_s,
        # ttfts is empty if streaming is not supported by backend
        mean_ttft_ms=np.mean(ttfts or 0) * 1000,
        median_ttft_ms=np.median(ttfts or 0) * 1000,
        std_ttft_ms=np.std(ttfts or 0) * 1000,
        p99_ttft_ms=np.percentile(ttfts or 0, 99) * 1000,
        mean_e2e_latency_ms=np.mean(e2e_latencies) * 1000,
        median_e2e_latency_ms=np.median(e2e_latencies) * 1000,
        overheads=overheads_df
    )

    return metrics, []


async def benchmark(
    api_url: str,
    input_requests: List[str],
    request_rate: float,
    disable_tqdm: bool,
    extra_request_body: Dict[str, Any],
    serial: bool=False
):

    request_func = async_request_kami
    print("Starting initial single prompt test run...")
    test_prompt = input_requests[0]
    test_input = RequestFuncInput(
        prompt=test_prompt,
        extra_request_body=extra_request_body,
        request_id=-1
    )
    test_output = await request_func(api_url, request_func_input=test_input)
    if not test_output.success:
        raise ValueError(
            "Initial test run failed - Please make sure benchmark arguments "
            f"are correctly specified. Error: {test_output.error}"
        )
    else:
        print("Initial test run completed. Starting main benchmark run...")

    pbar = None if disable_tqdm else tqdm(total=len(input_requests))

    benchmark_start_time = time.perf_counter()
    tasks: List[asyncio.Task] = []
    request_id = 0
    if serial:
        request_rate = float("inf")
    async for request in get_request(input_requests, request_rate):
        prompt = request
        request_func_input = RequestFuncInput(
            prompt=prompt,
            extra_request_body=extra_request_body,
            request_id=request_id
        )
        request_id += 1
        tasks.append(
            asyncio.create_task(
                request_func(
                    api_url, request_func_input=request_func_input, pbar=pbar)
            )
        )
        if serial:
            await tasks[-1]
    outputs: List[RequestFuncOutput] = await asyncio.gather(*tasks)

    if pbar is not None:
        pbar.close()

    benchmark_duration = time.perf_counter() - benchmark_start_time

    metrics, output_lens = calculate_metrics(
        input_requests=input_requests,
        outputs=outputs,
        dur_s=benchmark_duration,
    )

    print("\n{s:{c}^{n}}".format(s=" Serving Benchmark Result ", n=50, c="="))
    print("{:<40} {:<10}".format("Traffic request rate:", request_rate))
    print("{:<40} {:<10}".format("Successful requests:", metrics.completed))
    print("{:<40} {:<10.2f}".format(
        "Benchmark duration (s):", benchmark_duration))
    print(
        "{:<40} {:<10.2f}".format(
            "Request throughput (req/s):", metrics.request_throughput
        )
    )
    print("{s:{c}^{n}}".format(s="End-to-End Latency", n=50, c="-"))
    print(
        "{:<40} {:<10.2f}".format(
            "Mean E2E Latency (ms):", metrics.mean_e2e_latency_ms)
    )
    print(
        "{:<40} {:<10.2f}".format(
            "Median E2E Latency (ms):", metrics.median_e2e_latency_ms
        )
    )
    print("{s:{c}^{n}}".format(s="Time to First Token", n=50, c="-"))
    print("{:<40} {:<10.2f}".format("Mean TTFT (ms):", metrics.mean_ttft_ms))
    print("{:<40} {:<10.2f}".format(
        "Median TTFT (ms):", metrics.median_ttft_ms))
    print("{:<40} {:<10.2f}".format("P99 TTFT (ms):", metrics.p99_ttft_ms))
    print("{s:{c}^{n}}".format(s="Overheads(exclusive of 0 values)", n=50, c="-"))
    print(metrics.overheads)
    print("=" * 50)

    if (
        metrics.median_ttft_ms is not None
    ):
        result = {
            "request_rate": request_rate,
            "mean_e2e_latency_ms": metrics.mean_e2e_latency_ms,
            "median_e2e_latency_ms": metrics.median_e2e_latency_ms,
            "median_ttft_ms": metrics.median_ttft_ms,
            "duration": benchmark_duration,
            "completed": metrics.completed,
        }
    else:
        print(f"Error running benchmark for request rate: {request_rate}")
        print("-" * 30)

    # Determine output file name
    if args.output_file:
        output_file_name = args.output_file
    else:
        now = datetime.now().strftime("%Y%m%d%H%M")
        output_file_name = f"{now}_{args.num_prompts}.jsonl"

    result = {
        "duration": benchmark_duration,
        "completed": metrics.completed,
        "request_throughput": metrics.request_throughput,
        "mean_ttft_ms": metrics.mean_ttft_ms,
        "median_ttft_ms": metrics.median_ttft_ms,
        "std_ttft_ms": metrics.std_ttft_ms,
        "p99_ttft_ms": metrics.p99_ttft_ms,
        "output_lens": output_lens,
        "ttfts": [output.ttft for output in outputs],
        "errors": [output.error for output in outputs],
        "mean_e2e_latency_ms": metrics.mean_e2e_latency_ms,
        "median_e2e_latency_ms": metrics.median_e2e_latency_ms,
    }

    # Append results to a JSONL file
    with open(output_file_name, "a") as file:
        file.write(json.dumps(result, ensure_ascii=False) + "\n")

    # Output detailed requests and responses
    detailed_answers = []
    for o in outputs:
        tmp = {"request": input_requests[o.request_id],
               "result": o.raw_response}
        detailed_answers.append(tmp)
    now = datetime.now().strftime("%Y%m%d%H%M")
    detailed_answers_fn = f"{now}_{args.num_prompts}_detailed.json"
    with open(detailed_answers_fn, "a") as file:
        file.write(json.dumps(detailed_answers, ensure_ascii=False, indent = 4) + "\n")
    return result


def parse_request_rate_range(request_rate_range):
    if len(request_rate_range.split(",")) == 3:
        start, stop, step = map(int, request_rate_range.split(","))
        return list(range(start, stop, step))
    else:
        return list(map(int, request_rate_range.split(",")))


def run_benchmark(args_: argparse.Namespace):
    global args
    args = args_

    # Set global environments
    #调用 set_ulimit() 函数，提升系统软限制（文件描述符数量）以支持高并发，确保不会因为文件描述符限制导致错误
    set_ulimit()
    random.seed(args.seed)
    np.random.seed(args.seed)

    extra_request_body = {}
    if args.extra_request_body:
        extra_request_body = json.loads(args.extra_request_body)

    # Set url
    api_url = (
        f"{args.base_url}/v1/llm/chat-completions"
        if args.base_url
        # 只有内网可以访问
        else f"http://{args.host}:{args.port}/v1/llm/chat-completions"
    )

    input_requests = sample_sharegpt_requests(
        dataset_path=args.dataset_path,
        num_requests=args.num_prompts,
    )

    if not args.multi:
        return asyncio.run(
            benchmark(
                api_url=api_url,
                input_requests=input_requests,
                request_rate=args.request_rate,
                disable_tqdm=args.disable_tqdm,
                extra_request_body=extra_request_body,
                serial=args.serial
            )
        )
    else:
        # Benchmark multiple rps. TODO: use a fixed duration to compute num_prompts
        request_rates = parse_request_rate_range(args.request_rate_range)

        for rate in request_rates:
            asyncio.run(
                benchmark(
                    api_url=api_url,
                    input_requests=input_requests,
                    request_rate=rate,
                    disable_tqdm=args.disable_tqdm,
                    extra_request_body=extra_request_body,
                    serial=args.serial
                )
            )


def set_ulimit(target_soft_limit=65535):
    resource_type = resource.RLIMIT_NOFILE
    current_soft, current_hard = resource.getrlimit(resource_type)

    if current_soft < target_soft_limit:
        try:
            resource.setrlimit(
                resource_type, (target_soft_limit, current_hard))
        except ValueError as e:
            print(f"Fail to set RLIMIT_NOFILE: {e}")


if __name__ == "__main__":
    parser = ArgumentParser(
        description="Benchmark the online serving throughput.")
    parser.add_argument(
        "--base-url",
        type=str,
        default=None,
        help="Server or API base url if not using http host and port.",
    )
    parser.add_argument(
        "--host", type=str, default="0.0.0.0", help="Default host is 0.0.0.0."
    )
    parser.add_argument(
        "--port",
        type=int,
        help="If not set, the default port is configured according to its default value for different LLM Inference Engines.",
    )
    parser.add_argument(
        "--dataset-path", type=str, default="", help="Path to the dataset."
    )
    parser.add_argument(
        "--num-prompts",
        type=int,
        default=1000,
        help="Number of prompts to process. Default is 1000.",
    )
    parser.add_argument(
        "--request-rate",
        type=float,
        default=float("inf"),
        help="Number of requests per second. If this is inf, then all the requests are sent at time 0. "
        "Otherwise, we use Poisson process to synthesize the request arrival times. Default is inf.",
    )
    parser.add_argument("--seed", type=int, default=1, help="The random seed.")
    parser.add_argument(
        "--multi",
        action="store_true",
        help="Use request rate range rather than single value.",
    )
    parser.add_argument(
        "--request-rate-range",
        type=str,
        default="2,34,2",
        help="Range of request rates in the format start,stop,step. Default is 2,34,2. It also supports a list of request rates, requiring the parameters to not equal three.",
    )
    parser.add_argument("--output-file", type=str,
                        help="Output JSONL file name.")
    parser.add_argument(
        "--disable-tqdm",
        action="store_true",
        help="Specify to disable tqdm progress bar.",
    )
    parser.add_argument(
        "--disable-stream",
        action="store_true",
        help="Disable streaming mode.",
    )
    parser.add_argument(
        "--extra-request-body",
        metavar='{"key1": "value1", "key2": "value2"}',
        type=str,
        help="Append given JSON object to the request payload. You can use this to specify"
        "additional generate params like sampling params.",
    )
    parser.add_argument(
        "--serial",
        action="store_true",
        help="Test the prompts serially when this flag is set. It will overwrite the request-rate to inf."
    )
    parser.add_argument(
        "--model",
        type=str,
        default="senseauto",
        help="Specify the model to use for processing (e.g., 'senseauto'...)."
    )
    parser.add_argument(
        "--stream",
        action="store_true",
        help="Enable streaming mode for real-time processing of data."
    )
    parser.add_argument(
        "--dev",
        action="store_true",
        help="Enable developer mode for debugging and testing purposes."
    )
    args = parser.parse_args()
    run_benchmark(args)
