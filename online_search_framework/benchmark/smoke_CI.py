import jwt
import json
import sys
import time
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass, field
from argparse import ArgumentParser
import traceback
import requests

AK = "146049b1-0a95-49a2-8856-5e5e35f0f9a6"
SK = "b8bb9b60-9b3d-4e68-be09-f1e6816794d4"


@dataclass
class RequestFuncOutput:
    generated_text: str = ""
    success: bool = False
    latency: float = 0.0
    srv_latency: float = 0.0
    ttft: float = 0.0  # Time to first token
    sources: str = ""
    keywords: float = None
    error: str = ""
    output_len: int = 0


def create_access_token(ak: str, sk: str) -> str:
    expire = datetime.now(timezone.utc) + timedelta(days=7)
    nbf = datetime.now(timezone.utc) - timedelta(minutes=1)
    to_encode = {"iss": ak, "exp": expire, "nbf": nbf}
    encoded_jwt = jwt.encode(to_encode, sk, algorithm="HS256")
    return encoded_jwt


TOKEN = create_access_token(AK, SK)


def main(url, query):
    payload = {
        "engine": "bing",  # sougou / bing / google
        "query": query,
        "stream": False,
        "detect": True
    }
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + TOKEN
    }

    output = RequestFuncOutput()

    generated_text = ""
    st = time.perf_counter()
    try:
        response = requests.post(url=url, json=payload, headers=headers, timeout=6 * 60 * 60)
        if response.status_code == 200:
            print("response code: 200")
            js = response.json()

            generated_text = js.get("answer", "")
            latency = time.perf_counter() - st
            output.generated_text = generated_text
            output.success = True
            output.latency = latency
            if "time_cost" in js:
                output.srv_latency = js["time_cost"]["total_time"]
                output.ttft = js["time_cost"]["TTFT"]
                output.sources = js["time_cost"]["sources"]
                output.keywords = js["time_cost"].get("keywords")
        else:
            print(f"status is {response.status_code}, response: {response.content}")
            output.error = response.reason or ""
            output.success = False
    except Exception as e:
        print("exception", e)
        output.success = False
        exc_info = sys.exc_info()
        output.error = "".join(traceback.format_exception(*exc_info))
    finally:
        print('success:', output.success, sep='\t')
        print('latency:', output.latency, sep='\t')
        print('srv_latency:', output.srv_latency, sep='\t')
        print('ttft:\t', output.ttft, sep='\t')
        print('sources:', output.sources, sep='\t')
        print('keywords:', output.keywords, sep='\t')
        print('error:\t', output.error, sep='\t')
        print('answer:\t', output.generated_text, sep='\t')
        ret = 0 if output.generated_text != "" else 1
        print(f"ret is {ret}")
        sys.exit(ret)


if __name__ == '__main__':
    parser = ArgumentParser(
        description="Smoke testing used to check if an API is functioning correctly.")
    parser.add_argument(
        "-q",
        "--query",
        type=str,
        default="北京今天的天气",
        help="A query sent to the LLM."
    )
    parser.add_argument(
        "--url",
        type=str,
        default="http://gm-cloud.sensetime.com:8080/simple/web_search/v2",
        help="The default is http://gm-cloud.sensetime.com:8080/simple/web_search/v2"
    )
    args = parser.parse_args()
    main(args.url, args.query)
