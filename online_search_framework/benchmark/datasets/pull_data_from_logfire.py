import requests
import json
from datetime import datetime


def pull_logs(start_time="2024-10-20 00:00:00", end_time="2024-10-25 10:38:00", limit=100,
              save=True, service_name="kami_search_testing",
              user_id=""):
    # user_id = "146049b1-0a95-49a2-8856-5e5e35f0f9a6"
    # start_time = "2024-10-25 00:00:00"
    # end_time = "2024-10-25 10:38:00"
    # limit = 1

    date_format = '%Y-%m-%d %H:%M:%S'
    params = {
        "start": round(datetime.strptime(start_time, date_format).timestamp() * 1_000_000),
        "end": round(datetime.strptime(end_time, date_format).timestamp() * 1_000_000),
        "limit": limit,
        "service": service_name
    }
    if user_id:
        params["tags"] = json.dumps({"user.id": user_id})
    else:
        params["tags"] = json.dumps({})

    r = requests.get(
        "http://gm-cloud.sensetime.com:9686/api/traces",
        auth=("jaeger", "GeneralModel2024*"),
        params=params
    )

    js = r.json()
    # print(json.dumps(js, ensure_ascii=False, indent=4))
    conversations = []
    for d in js["data"]:
        trace_id = d["traceID"]
        for s in d["spans"]:
            span_id = s["spanID"]
            # print(f"spanID: {spanID}")
            # if s["operationName"].startswith("Chat Completion"):
            request, response = "", ""
            usage = {}
            for tag in s["tags"]:  # 一个`tags`数组，包含一轮问和答
                if tag["key"] == "request_data":
                    value = tag["value"]
                    value_js = json.loads(value)
                    if len(value_js) == 0:
                        print("value_js empty!")
                        continue
                    # print(json.dumps(value_js, ensure_ascii=False, indent=4))
                    if not request:
                        request = value_js["messages"][0]["content"] + '\n' + \
                            value_js["messages"][1]["content"]
                elif tag["key"] == "response_data":
                    value = tag["value"]
                    value_js = json.loads(value)
                    if len(value_js) == 0:
                        print("value_js empty!")
                        continue
                    # print("response_data:")
                    print(json.dumps(value_js, ensure_ascii=False, indent=4))
                    if not response:
                        if "message" in value_js:
                            response = value_js["message"]["content"]
                            usage = value_js["usage"]
                        elif "combined_chunk_content" in value_js:
                            response = value_js["combined_chunk_content"]
            if request:
                id = trace_id + "_" + span_id
                conv = get_sharegpt_conversation(
                    request, response, usage, id)
                conversations.append(conv)
    if save:
        filename = start_time.replace(
            " ", "_").replace(":", "_") + "_"+end_time.replace(" ", "_").replace(":", "_") + "_" + str(limit) + ".json"
        with open(filename, 'w') as out_file:
            json.dump(conversations, out_file, indent=4, ensure_ascii=False)
    return conversations


def get_sharegpt_conversation(request, response, usage, ID):
    conversation = {
        "id": ID,
        "conversations": [
            {
                "from": "human",
                "value": request
            },
            {
                "from": "gpt",
                "value": response,
                "usage": usage
            }
        ]
    }
    return conversation


if __name__ == "__main__":
    user_id = "146049b1-0a95-49a2-8856-5e5e35f0f9a6"
    conversations = pull_logs(start_time="2024-10-20 00:00:00",
                              end_time="2024-10-28 17:38:00", limit=1000, save=True,
                              service_name="kami_search", user_id="")
