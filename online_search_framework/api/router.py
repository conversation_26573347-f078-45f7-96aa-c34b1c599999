import json
import secrets
import time
import uuid

from fastapi import APIRouter
from fastapi.responses import Response
from loguru import logger
from sse_starlette.sse import EventSourceResponse
from starlette.responses import JSONResponse
from src.utils.llms.openai_SDK import OpenAI_LLM

from main import global_config, selected_llm_config, rewrite_llm_config, serper_se, bing_se, tencent_se
from schemas import WebSearchReqV2
from service.logger_manager import fire_logger
from src.prompts.router_prompts import router_system_zh, router_user_zh, router7b_system_prompt, router7b_user_prompt
from src.utils.actions.other_actions.rerank_topk import Rerank
from src.utils.actions.tool_actions.action_excutor import ActionExecutor
from src.utils.actions.tool_actions.web_search import WebSearch
from src.utils.memory.memory_base import AllMessageMemory
from src.utils.web_content_scrape import WebScrape, WebScrapePy
from yield_demo import WebSearchAgent as YieldSearchAgent, WebChatQueryRequest

api_version = "1.9.0"
api_v1 = APIRouter(prefix='/cabin')


@api_v1.get("/web_search/health")
async def health() -> Response:
    """Health check."""
    response_body = {"status": "ok", "version": api_version}
    response_content = json.dumps(response_body).encode("utf-8")
    response_header = {"Content-Type": "application/json"}
    return Response(status_code=200, content=response_content, headers=response_header)


@api_v1.post("/web_search/v2")
async def web_search_v2(item: WebSearchReqV2):
    request_id = uuid.uuid4()
    response_header = {"request_id": str(request_id)}
    fire_logger.info("%s START", str(request_id))
    start_time = time.perf_counter()

    try:
        k = item.k
        detect = item.detect
        engine = item.engine
        use_search_cache = item.use_search_cache
        message_id = secrets.token_hex(7)

        if engine == 'google':
            tools = [WebSearch(search_engines=[serper_se], use_search_cache=use_search_cache)]
        elif engine == 'bing':
            tools = [WebSearch(search_engines=[bing_se], use_search_cache=use_search_cache)]
        else:
            tools = [WebSearch(search_engines=[tencent_se], use_search_cache=use_search_cache)]
        # logger.info(f"selected search engine: {engine}")
        rerank_module = Rerank(k, global_config["rerank"]["base_url"])
        tool_executor = ActionExecutor(tools)

        public_memory = AllMessageMemory()
        # 处理历史
        if item.history is not None:
            for v in item.history:
                logger.debug(f"{v}  type: {type(v)}")
                public_memory.add_message(v.content, message_type=v.role)
        elif item.messages is not None:
            # 兼容OpenAI API
            for v in item.messages[:-1]:
                public_memory.add_message(v.content, message_type=v.role)
        else:
            # 没有历史对话
            pass

        # 提取当前query
        if item.query is not None:
            current_query = item.query
        else:
            current_query = item.messages[-1].content

        # 构造时生成api_key，对于nova的key，有过期时间，所以需要每条request都构造一次，生成key有cache进行加速
        rewrite_client = OpenAI_LLM(rewrite_llm_config)
        summary_client = OpenAI_LLM(selected_llm_config)
        limit_scraping_time = "800ms"
        # 构造爬虫
        if global_config["scrape"]["engine-in-use"] == "go-readability":
            crawler = WebScrape(
                global_config["scrape"], limit_scraping_time=limit_scraping_time
            )
        elif global_config["scrape"]["engine-in-use"] == "python-document":
            crawler = WebScrapePy()
        else:
            raise ValueError(
                f"unknown scrape engine: {global_config['scrape']['engine-in-use']}"
            )

        # default is 72b
        router_system = router_system_zh
        router_user = router_user_zh
        if "type" in rewrite_llm_config and rewrite_llm_config["type"] == "7b":
            router_system = router7b_system_prompt
            router_user = router7b_user_prompt

        web = YieldSearchAgent(
            rewrite_client,
            summary_client,
            rerank_module,
            tool_executor,
            public_memory,
            crawler,
            None,
            detect,
            global_config["sensitive"],
            router_system_prompt=router_system,
            router_user_prompt=router_user,
        )

        web_chat_query_request = WebChatQueryRequest(
            query=current_query,
            message_id=message_id,
            request_id=str(request_id),
            model_name="kami-search",
            resource="/simple/web_search/v2",
        )  # TODO remove the model_name hardcode

        if not item.stream:
            # if True:
            response = {}
            answer = ""
            first_word = True
            time_cost = {}
            async for result in web.chat(web_chat_query_request):
                if isinstance(result, dict) and result.get("type") == "keywords":
                    # response['keywords'] = result.get("data")
                    time_cost["keywords"] = round(time.perf_counter() - start_time, 4)
                if isinstance(result, dict) and result.get("type") == "sources":
                    response["sources"] = result.get("data")
                    time_cost["sources"] = round(time.perf_counter() - start_time, 4)
                if isinstance(result, dict) and result.get("type") == "fetch":
                    # response['sources'] = result.get("data")
                    time_cost["fetch"] = round(time.perf_counter() - start_time, 4)
                if isinstance(result, dict) and result.get("type") == "message":
                    answer += result["data"]
                    if first_word is True:
                        first_word = False
                        time_cost["TTFT"] = round(time.perf_counter() - start_time, 4)
                    if "usage" in result:  # may only appear in the last result
                        response["usage"] = result["usage"]
                if isinstance(result, dict) and result.get("type") == "error":
                    answer = result.get("data")
                    response['code'] = result.get("code")
                if isinstance(result, dict) and result.get("type") == "messageEnd":
                    if response.get('code', None) is None:
                        response['code'] = result.get("code", 0)
                    # if response['code'] != 0:
                    #     answer += result["data"]

            time_cost["total_time"] = round(time.perf_counter() - start_time, 4)
            response["answer"] = answer
            response["time_cost"] = time_cost
            # fire_logger.info("%s FINISH", str(request_id), extra={"content": response})
            logger.info(f"{str(request_id)} FINISH")

            # # create async tasks to write messages to the DB
            # if "usage" in response:
            #     input_tokens = response["usage"]["prompt_tokens"]
            #     output_tokens = response["usage"]["completion_tokens"]
            # else:
            #     input_tokens, output_tokens = 0, 0

            return JSONResponse(response, headers=response_header)
        else:
            async def event_generator():
                result = {
                    # "data": chat_id.hex,
                    "type": "chat_id",
                    "messageId": str(message_id),
                }
                ## disable chat_id yield for TTFT testing
                # yield dict(data=json.dumps(result, ensure_ascii=False))
                answer = ""
                last_message = None
                first_word = True
                time_cost = {}
                async for result in web.chat(web_chat_query_request):
                    # and result.get("type") == "message":
                    if isinstance(result, dict) and result.get("type") != "messageEnd":
                        yield dict(data=json.dumps(result, ensure_ascii=False))
                        if isinstance(result, dict) and result.get("type") == "keywords":
                            time_cost["keywords"] = round(time.perf_counter() - start_time, 4)
                        if isinstance(result, dict) and result.get("type") == "sources":
                            time_cost["sources"] = round(time.perf_counter() - start_time, 4)
                        if isinstance(result, dict) and result.get("type") == "fetch":
                            time_cost["fetch"] = round(time.perf_counter() - start_time, 4)
                        if result["type"] == "message":
                            answer += result["data"]
                            last_message = result
                            if first_word is True:
                                first_word = False
                                time_cost["TTFT"] = round(time.perf_counter() - start_time, 4)
                    else:
                        # yield messageEnd with time_cost summary
                        time_cost["total_time"] = round(time.perf_counter() - start_time, 4)
                        result["time_cost"] = time_cost
                        yield dict(data=json.dumps(result, ensure_ascii=False))
                # create async tasks to write messages to the DB
                if last_message and "usage" in last_message:
                    input_tokens = last_message["usage"]["prompt_tokens"]  # type: ignore
                    output_tokens = last_message["usage"]["completion_tokens"]  # type: ignore

            return EventSourceResponse(event_generator(), headers=response_header)
    except Exception as e:
        logger.error(e)
        import traceback

        traceback.print_exc()
        response = {"error": str(e)}
        return JSONResponse(response, headers=response_header)
