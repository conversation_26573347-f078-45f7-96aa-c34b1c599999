from typing import Any, Coroutine

import requests
import aiohttp, asyncio
from typing_extensions import Optional
from configuration import config
from aiohttp import ClientTimeout
from loguru import logger
import src.utils.news_error_code as error_code

url=config["user_memory"]["url"]

async def _request_habit_from_user_memory_raw(body: dict, time_limit: float = 0.8) -> tuple[None, dict[str, str]] | tuple[None, dict[str, str | None]] | Any:
    header={'Content-Type': 'application/json'}
    timeout = ClientTimeout(total=time_limit)
    error = None
    try:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(url=url, headers=header, json=body) as response:
                if response.status == 200:
                    result = await response.json()
                    return result, None
                else:
                    error = {"code":error_code.error_code_mem_http_error, "msg": response.reason}
                    return None,error
    except (aiohttp.ClientError, asyncio.TimeoutError) as e:
        logger.error(f"_request_habit_from_user_memory_raw error  e: {e}")
        error = {"code": error_code.error_code_mem_http_error, "msg": f"request error:{e}"}
        return None,error
    
# realtion_filter 表示需要筛选的关系类型，如 LIKES 、 INTRERSTED_IN 等
# async def request_habit_from_user_memory_raw(body: dict, relation_filter: Optional[str] = None) -> Optional[list]:
#     all_info = _request_habit_from_user_memory_raw(body=body)
#     if not all_info:
#         return None
#     status = all_info.get("status", {})
#     if "200" != status.get("code", ""):
#         return None
#     data = all_info.get("data", {})
#     data_list = data.get("dataList", [])
#     if not relation_filter:
#         return data_list
#     else:
#         filtered_data_list = [temp for temp in data_list if isinstance(temp, str) and relation_filter in temp]
#         return filtered_data_list



# realtion_filter 表示需要筛选的关系类型，如 LIKES 、 INTRERSTED_IN 等
async def request_habit_from_user_memory_text(body: dict, relation_filter: str, time_limit: float = 0.8) -> tuple[None,
dict[str, str]] | tuple[None, Any] | tuple[None, dict[str, str | Any]] | tuple[None, None] | tuple[str, None]:
    error = None
    try:
        all_info, request_error= await asyncio.wait_for(
            _request_habit_from_user_memory_raw(body=body, time_limit=time_limit),
            timeout=time_limit
        )
        if request_error:
            return None,request_error
        status = all_info.get("status", {})
        if "200" != status.get("code", ""):
            error = {"code": error_code.error_code_mem_return_error, "msg": f"Return status: {status}"}
            return None,error,
        data = all_info.get("data", {})
        data_list = data.get("dataList", [])
        # relation_filter 不再使用，返回完整的用户画像
        filtered_data_list = data_list
        # filtered_data_list = [temp for temp in data_list if isinstance(temp, str) and relation_filter in temp]
        if 0 >= len(filtered_data_list):
            return None,error
        else:
            try:
                print(filtered_data_list)
                temp_care = "User "
                cares = ", ".join(filtered_data_list)
                print(cares)
                return temp_care + cares + ".",error
            except BaseException as e:
                error = {"code": error_code.error_code_mem_return_error, "msg": f"cant format result:{str(e)}"}
                print(e)
                return None,error
    except asyncio.TimeoutError:
        error = {"code":error_code.error_code_mem_http_error,"msg":"request_habit_from_user_memory_text timeout"}
        logger.error("request_habit_from_user_memory_text timeout")
        return None,error

        