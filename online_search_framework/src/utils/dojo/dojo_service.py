from src.utils.actions.tool_actions.action_base import BaseAction
import requests
import os
import json
import time
import jwt
from src.utils.dojo.RequestHandler import RequestHandler

# from src.utils.dojo.doc_llm import DocLLM
from src.utils.actions.tool_actions.action_base import tool_api


class Dojo(BaseAction):
    def __init__(self, dojo_config):
        super().__init__()
        self.backend = dojo_config["url"]
        # self.search_config = self.load_config("src/config/dojo.yaml")
        self.ak = dojo_config["dojo_ak"]
        self.sk = dojo_config["dojo_sk"]
        self.know_id = dojo_config["kb_id"]
        self.payload = {
            "iss": self.ak,
            "exp": (
                int(time.time()) + 180000
            ),  # 填写您期望的有效时间，此处示例代表当前时间+30分钟
            "nbf": int(time.time())
            - 5,  # 填写您期望的生效时间，此处示例代表当前时间-5秒
        }
        self.request_handler = RequestHandler(
            self.backend, self.gen_header(self.ak, self.sk)
        )

    def gen_header(self, ak, sk):
        self.payload["iss"] = ak
        self.payload["exp"] = int(time.time()) + 180000
        self.payload["nbf"] = int(time.time()) - 5
        return {"Authorization": f"Bearer {jwt.encode(payload=self.payload, key=sk)}"}

    # 上传文档到知识库
    def upload_docs(self, kbname: str, filename: str):
        files = {"file": open(filename, "rb")}
        data = {"knowledge_base_name": kbname, "override": False}
        response = self.request_handler.post_data(
            "/v1/knowledge_base/upload_doc", data=data, files=files
        )
        if response.get("code") == 200 and response.get("msg") == "ok":
            print("Document uploaded successfully")
        else:
            raise RuntimeError(f"Failed to upload document: {response.get('msg')}")

    # 获取知识库中的文件列表
    def list_docs(self, kbname: str):
        params = {"knowledge_base_name": kbname}
        response = self.request_handler.get(
            "v1/knowledge_base/list_docs", params=params
        )
        # print("list doc", response.json().json())
        return response.get("data")

    def list_knowlege_bases(self):
        response = self.request_handler.get("/v1/knowledge_base/list_knowledge_bases")
        return response.get("data")

    # 删除知识库
    def delete_kb(self, kbname: str, is_default=False):
        # json = kbname
        response = self.request_handler.post_json(
            "/v1/knowledge_base/delete_knowledge_base", json=kbname
        )
        print("delete", response)

    def Tseg(
        self, file_path: str, max_len: int, chunk_overlap: int, split_mode: int = 0
    ):
        with open(file_path, "r", encoding="utf-8") as f:
            text = f.read()
        data = {
            "text": text,
            "max_len": max_len,  # 最大支持到512
            "split_mode": split_mode,  # 0表示语义，1表示规则，500表示不切割
            "chunk_overlap": chunk_overlap,
            "threshold": 0.5,
        }
        payload = {"inputs": [data]}
        response = self.request_handler.post_json("/v1/tseg", json=payload)
        # result = response.json()
        outfile = os.path.splitext(file_path)[0] + ".json"
        text_lst = []
        [text_lst.extend(_["segments"]) for _ in response["outputs"]]
        knowledge_json = {"qa_lst": [], "text_lst": text_lst}
        with open(outfile, "w", encoding="utf-8") as f:
            json.dump(knowledge_json, f, ensure_ascii=False, indent=4)
        print(f"The cost time of tseg: {response['cost']}s")
        return outfile
        # response = requests.post(
        #     url=f"{self.backend}/v1/tseg",
        #     json=payload,
        #     headers=self.gen_header(self.ak, self.sk),
        # )
        # response.raise_for_status()
        # if response.status_code == 200:
        #     result = response.json()
        #     outfile = os.path.splitext(file_path)[0] + ".json"
        #     text_lst = []
        #     [text_lst.extend(_["segments"]) for _ in result["outputs"]]
        #     knowledge_json = {"qa_lst": [], "text_lst": text_lst}
        #     with open(outfile, "w", encoding="utf-8") as f:
        #         json.dump(knowledge_json, f, ensure_ascii=False, indent=4)
        #     print(f"The cost time of tseg: {result['cost']}s")
        # else:
        #     print("Error:", response.status_code, response.json())

    # 创建知识库,但是里面没有文件。
    def create_kb(
        self,
        kbname: str = "",
        vs_type: str = "pg",
        emb="piccolo-multilingual-base-v1.0.7-2k",
        config={
            "enable_es": False,
            "text_split": {
                "mode": 0,  # 0表示语义，1表示规则，500表示不切割
                "config": {"max_length": 512},
            },
        },
    ):
        response = self.request_handler.post_json(
            "/v1/knowledge_base/create_knowledge_base",
            data={
                "knowledge_base_name": kbname,
                "vector_store_type": vs_type,
                "embed_model": emb,
                "config": config,
            },
        )
        print(response)
        # r = requests.post(
        #     url=f"{self.backend}/v1/knowledge_base/create_knowledge_base",
        #     json={
        #         "knowledge_base_name": kbname,
        #         "vector_store_type": vs_type,
        #         "embed_model": emb,
        #         "config": config,
        #     },
        #     headers=self.gen_header(self.ak, self.sk),
        # )

    # @tool_api
    # async def offline_search(self, query):
    #     self.search_config["messages"][0]["content"] = query
    #     resopnse = self.request_handler.post_json("/v1/search", json=self.search_config)
    #     content = resopnse["result"]  # list 中含着dict
    #     content = [doc["page_content"] for doc in content]
    #     resopnse = {query: content}

    #     return resopnse

    def downlowd(self):
        r = requests.get(
            f"{self.backend}/v1/knowledge_base/list_knowledge_bases?detail=True",
            headers=self.gen_header(self.ak, self.sk),
        ).json()
        assert r["code"] == 200 and r["msg"] == "success"
        return r

    async def offline_search(self, query) -> dict:
        response = self.request_handler.post_json(
            f"{self.backend}/v1/search",
            json={
                "messages": [{"role": "user", "content": query}],
                "know_ids": [self.know_id],
                "knowledge_config": {
                    "intent_configs": {"enable": False},
                    "rewrite_configs": {"enable": False},
                    "online_search_configs": {"enable": False},
                    "rerank_configs": {"enable": True, "topK": 5},
                    "knowledge_base_configs": [
                        {
                            "know_id": self.know_id,
                            "offline_search_configs": {
                                "enable": True,
                                "q_QA_vector_search_configs": {
                                    "enable": True,
                                    "q_question_thresh_high": 1,
                                    "q_question_thresh_low": 0.9,
                                    "q_question_topK": 5,
                                    "q_answer_thresh_high": 1,
                                    "q_answer_thresh_low": 0.9,
                                    "q_answer_topK": 5,
                                },
                                "q_text_vector_search_configs": {"enable": False},
                                "q_ES_search_configs": {"enable": False},
                            },
                        }
                    ],
                },
            },
        )
        # content = response["result"]  # list 中含着dict
        # content = [doc["page_content"] for doc in content]
        # resopnse = {query: content}
        return response


if __name__ == "__main__":
    file_path = "/home/<USER>/miaomuxing1/Documents/ruying_api/ruying.txt"
    tmp_path = "./tmp/"
    kb_id = "demo_kb_id"

    # 1 step 切分文件
    max_len = 256
    chunk_overlap = 32
    split_mode = 0
    file_path = Dojo.Tseg(file_path, max_len, chunk_overlap, split_mode)

    # 2 step 创建知识库(已使用Tseg切分，所以建立知识库时将text_split--mode设为500)
    config = {
        "enable_es": True,  #
        "text_split": {
            "mode": 500,  # 0表示语义，1表示规则，500表示不切割
            "config": {"max_length": 512},
        },
    }
    Dojo.create_kb(kb_id, config=config)

    # 3 step 上传文件(upload_docs上传的文件必须符合格式要求，具体参照Tseg输出)
    Dojo.upload_docs(kb_id, file_path)
