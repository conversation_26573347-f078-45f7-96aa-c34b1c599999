import requests
import os
from urllib.parse import urljoin


def request_handler(func):
    def wrapper(*args, **kwargs):
        try:
            response = func(*args, **kwargs)
            response.raise_for_status()  # 检查HTTP错误状态
            return response.json()  # 假设所有请求都返回JSON格式的响应
        except requests.exceptions.HTTPError as http_err:
            raise RuntimeError(f"HTTP error occurred: {http_err}")
        except requests.exceptions.RequestException as req_err:
            raise RuntimeError(f"Request error occurred: {req_err}")
        except Exception as e:
            raise RuntimeError(f"An unexpected error occurred: {str(e)}")

    return wrapper


class RequestHandler:
    def __init__(self, base_url, headers=None):
        self.base_url = base_url
        self.headers = headers

    @request_handler
    def post_json(self, endpoint, json=None, files=None):
        url = urljoin(self.base_url, endpoint)
        return requests.post(url, json=json, files=files, headers=self.headers)

    @request_handler
    def post_data(self, endpoint, data=None, files=None):
        url = urljoin(self.base_url, endpoint)
        return requests.post(url, data=data, files=files, headers=self.headers)

    @request_handler
    def get(self, endpoint, params=None):
        url = urljoin(self.base_url, endpoint)
        return requests.get(url, params=params, headers=self.headers)
