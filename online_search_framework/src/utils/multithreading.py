import concurrent.futures


class MultiThreadExecutor:
    def __init__(self, max_workers=10):
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)

    def run_in_threads(self, args_list, ordered_return=False):
        if not isinstance(args_list, list):
            raise ValueError("args_list must be a list of tuples or lists.")

        futures = []
        for i, args in enumerate(args_list):
            if isinstance(args, (list, tuple)) and len(args) >= 1:
                func = args[0]
                func_args = args[1:]
                futures.append((i, self.executor.submit(func, *func_args)))
            else:
                raise ValueError(
                    "Each element of args_list must be a list or tuple with at least one function and its arguments"
                )

        results = [None] * len(futures)
        for i, future in futures:
            try:
                results[i] = future.result()
            except Exception as e:
                results[i] = e

        if ordered_return:
            results = [
                result for _, result in sorted(zip([i for i, _ in futures], results))
            ]

        return results

    def shutdown(self):
        self.executor.shutdown(wait=True)
