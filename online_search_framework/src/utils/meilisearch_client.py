import aiohttp
import time
import logging


class MeiliIndex:
    def __init__(self, url: str, api_key: str, index: str):
        self.url = url
        self.api_key = api_key
        self.index = index

    async def get_document(self, doc_id: str):
        # start_time = time.perf_counter()
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.url}/indexes/{self.index}/documents/{doc_id}",
                    headers={"Authorization": f"Bearer {self.api_key}"},
                    timeout=aiohttp.ClientTimeout(0.1),
                ) as resp:
                    # logging.info(f"meili {doc_id}: {round(time.perf_counter()-start_time, 3)}")
                    if resp.status == 200:
                        return await resp.json()
                    return None
        except Exception as _:
            return None

    async def add_documents(self, docs: list[dict]):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.url}/indexes/{self.index}/documents",
                    json=docs,
                    headers={"Authorization": f"Bearer {self.api_key}"},
                    timeout=aiohttp.ClientTimeout(0.2),
                ) as _:
                    pass
        except Exception as _:
            pass
