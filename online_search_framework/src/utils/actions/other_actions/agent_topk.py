from src.utils.actions.other_actions.topk_base import Topk<PERSON><PERSON>
from typing import Dict
from src.prompts.topk_prompts import topk_system_zh, topk_user_zh
from src.utils.multithreading import MultiThreadExecutor
import re


class AgentTopK(TopkBase):
    def __init__(self, agent, k, base_url):
        super().__init__()
        self.embedding_url = base_url
        self.agent = agent
        self.k = str(k)
        self.agent.memory.add_message(topk_system_zh, "system")

    def ranking(self, query_with_link: dict):
        def process_query(query, snippets):
            topk_user_prompt = topk_user_zh.format(
                snippets=snippets, user_input=query, Top_k=self.k
            )
            topk_res = self.agent.chat(topk_user_prompt, stream=True)
            return self.rerank_output_parse(query, topk_res)

        formated_ = self.pre_format(query_with_link)

        args_list = [
            (process_query, query, snippets) for query, snippets in formated_.items()
        ]

        executor = MultiThreadExecutor(max_workers=10)
        results = executor.run_in_threads(args_list, ordered_return=True)

        temp = {query: result for query, result in zip(formated_.keys(), results)}
        return temp

    def rerank_output_parse(self, query: str, llm_json_output: str):
        """
        Parses the JSON output from an LLM to extract the "Action_input" list.

        Args:
            query (str): The query string associated with the LLM output.
            llm_json_output (str): The JSON output string from the LLM.
                Example:
                '{"Action_input": ["item1", "item2", "item3"]}'

        Returns:
            dict: A dictionary with the query as the key and the extracted list as the value.
                Example:
                {'some query': ['item1', 'item2', 'item3']}
        """
        cleaned_string = re.sub(r"\\n|\\", "", llm_json_output).strip("'\"")

        if match := re.search(
            r'"Action_input":\s*\[(.*?)\]', cleaned_string, re.DOTALL
        ):
            if action_input_str := match.group(1):
                action_input_list = [
                    item.strip().strip('"') for item in action_input_str.split(",")
                ]
            else:
                action_input_list = []
        else:
            action_input_list = []
        return action_input_list

    def pre_format(self, query_with_link: Dict):
        """
        Format search results with optional titles.

        Args:
            query_with_link (dict): Dictionary of queries with their search results.
                Example:
                {
                    '今日 奥运会 消息': {
                        1: {
                            'title': '巴黎奥运会羽毛球比赛今日开赛！ - Paris 2024 Olympic Games',
                            'snippet': '巴黎奥运会羽毛球比赛在拉夏贝尔门体育馆正式拉开帷幕。顶尖选手们齐聚一堂，精彩对决一触即发，让我们共同见证羽毛球场上的激情与荣耀！',
                            'link': 'https://olympics.com/zh/paris-2024/live-updates/f66209be-4d16-4b6f-87ff-1ab46f90aac4'
                        },
                        2: {
                            'title': '冲击首金！巴黎奥运会今日比赛看点→',
                            'snippet': '今天是巴黎奥运会开幕后的首个比赛日，将决出14枚金牌。首金花落谁家，大家也十分期待。 本届奥运会的首金，有可能在射击混合团体10米气步枪和跳水女子 ...',
                            'link': 'https://content-static.cctvnews.cctv.com/snow-book/index.html?item_id=9712519362118668996'
                        }
                    },
                    '今日 xxxx 消息': {
                        1: {
                            'title': 'xxxxxx',
                            'snippet': 'xxxxxxxxx',
                            'link': 'xxxx'
                        }
                    }
                }

            keep_title (bool): Whether to include titles in the formatted output. Default is True.

        Returns:
            dict: Dictionary with formatted search results for each query as a single string.
                Example:
                {
                    '今日 奥运会 消息': "[1]: title: 巴黎奥运会羽毛球比赛今日开赛！ - Paris 2024 Olympic Games, snippet: 巴黎奥运会羽毛球比赛在拉夏贝尔门体育馆正式拉开帷幕。顶尖选手们齐聚一堂，精彩对决一触即发，让我们共同见证羽毛球场上的激情与荣耀！\n[2]: title: 冲击首金！巴黎奥运会今日比赛看点→, snippet: 今天是巴黎奥运会开幕后的首个比赛日，将决出14枚金牌。首金花落谁家，大家也十分期待。 本届奥运会的首金，有可能在射击混合团体10米气步枪和跳水女子 ...",
                    '今日 xxxx 消息': "[1]: title: xxxxxx, snippet: xxxxxxxxx"
                }
        """
        result = {}

        for query in query_with_link:
            formatted_results = []
            index = 1
            for sub_key in query_with_link[query]:
                title_snippet = f"title: {query_with_link[query][sub_key]['title']} -"
                snippet = query_with_link[query][sub_key]["snippet"]
                formatted_results.append(
                    f"[{index}]: {title_snippet}snippet: {snippet}"
                )
                index += 1
            result[query] = "\n".join(formatted_results)
        return result

    def post_format(self, query_keys, indices_list):
        pass
