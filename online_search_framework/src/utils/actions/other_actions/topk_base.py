from abc import ABC, abstractmethod
from src.utils.actions.tool_actions.action_base import BaseAction


class TopkBase(ABC, BaseAction):
    def __init__(self):
        super().__init__()

    @abstractmethod
    def ranking(self, query_with_link: dict) -> dict:
        raise NotImplementedError

    def get_idx_link(self, query_with_link, query_topk_indx):
        """
        Extracts and returns the links based on the provided query Topk indices.

        Args:
            query_with_link (dict): Dictionary containing queries with their respective link details.
                Example:
                {
                    '今日 奥运会 消息': {
                        1: {
                            'title': '巴黎奥运会羽毛球比赛今日开赛！ - Paris 2024 Olympic Games',
                            'snippet': '巴黎奥运会羽毛球比赛在拉夏贝尔门体育馆正式拉开帷幕。顶尖选手们齐聚一堂，精彩对决一触即发，让我们共同见证羽毛球场上的激情与荣耀！',
                            'link': 'https://olympics.com/zh/paris-2024/live-updates/f66209be-4d16-4b6f-87ff-1ab46f90aac4'
                        },
                        2: {
                            'title': '冲击首金！巴黎奥运会今日比赛看点→',
                            'snippet': '今天是巴黎奥运会开幕后的首个比赛日，将决出14枚金牌。首金花落谁家，大家也十分期待。 本届奥运会的首金，有可能在射击混合团体10米气步枪和跳水女子 ...',
                            'link': 'https://content-static.cctvnews.cctv.com/snow-book/index.html?item_id=9712519362118668996'
                        },
                        3: {
                            'title': '巴黎2024实时博客- 奥运会实时动态和劲爆新闻',
                            'snippet': '在今日晚些将举办的开幕式环节中，也会有大家都很熟悉的运动员入场式。你对所有参赛的国家和地区奥委会知多少？我们为你整理了一份列表。',
                            'link': 'https://olympics.com/zh/paris-2024/live-updates/9611097c-0b15-4b18-b5c4-749442c56006'
                        }
                    },
                    '今日 xxxx 消息': {
                        1: {
                            'title': 'xxxxxx',
                            'snippet': 'xxxxxxxxx',
                            'link': 'xxxx'
                        }
                    }
                }

            query_topk_indx (dict): Dictionary containing the indices of top-k links to extract.
                Example:
                {
                    '今日 奥运会 消息': ['1', '2'],
                    '今日 xxxx 消息': ['1']
                }

        Returns:
            dict: Dictionary with queries as keys and lists of extracted links as values.
                Example:
                {
                    '今日 奥运会 消息': [
                        'https://olympics.com/zh/paris-2024/live-updates/f66209be-4d16-4b6f-87ff-1ab46f90aac4',
                        'https://content-static.cctvnews.cctv.com/snow-book/index.html?item_id=9712519362118668996'
                    ],
                    '今日 xxxx 消息': ['xxxx']
                }
        """
        query_links_to_open = {}
        for q, values in query_topk_indx.items():
            temp_links = []
            for v in values:
                temp_links.append(query_with_link[q][int(v)]["link"])
            query_links_to_open[q] = temp_links
        return query_links_to_open
