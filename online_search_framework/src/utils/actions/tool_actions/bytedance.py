from src.utils.actions.tool_actions.web_search_base import WebSearchBase
import time
from loguru import logger

from volcenginesdkarkruntime import Ark

client = Ark(api_key="93057fc4-a6b3-43c9-bf58-224361406a10")


class Bytedance(WebSearchBase):
    def __init__(self, engine_name="byte"):
        super().__init__()
        if engine_name == "byte":
            self.engine_name = "byte"

    async def search(self, query: str, num: int):
        completion = client.bot_chat.completions.create(
            model="bot-20241015104116-97zdt",
            messages=[
                {"role": "system", "content": "调用联网插件查询，回复不超过十个字"},
                {"role": "user", "content": query},
            ],
        )

        print(completion)
        return self.format_search_res(completion)

    def format_search_res(self, search_res):
        result = {}

        search_results = search_res.bot_usage.action_details[0]["tool_details"][0][
            "output"
        ]["data"]["data"]["results"]
        for idx, search_result in enumerate(search_results, start=1):
            date = search_result.get("publish_time", "")
            if date != "":
                date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(date))
            result[idx] = {
                "title": search_result.get("title", ""),
                "snippet": search_result.get("summary", ""),
                "link": search_result.get("url", ""),
                "date": date.split(" ")[0],
            }
        return result
