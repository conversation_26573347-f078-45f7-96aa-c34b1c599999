from src.utils.actions.tool_actions.web_search_base import WebSearchBase
from src.utils.actions.tool_actions.action_base import tool_api
from src.utils.actions.tool_actions.action_base import BaseAction
from typing import List

from src.utils.cache import rd
from src.utils.multithreading import MultiThreadExecutor
import asyncio
import traceback
import json
from service.logger_manager import fire_logger
import time
from loguru import logger

class WebSearch(BaseAction):
    def __init__(self, search_engines: List[WebSearchBase], use_search_cache: bool = False):
        super().__init__()
        self.use_search_cache = use_search_cache
        self.search_engines = search_engines
        self.executor = MultiThreadExecutor(max_workers=len(search_engines))

    async def search_with_timeout(self, engine, query, num):
        if self.use_search_cache:
            try:
                data = await rd.get(f"wsrch:{engine.engine_name}:{query}")
                if data:
                    search_res = json.loads(data)
                else:
                    start_time = time.perf_counter()
                    search_res = await engine.search(query, num)
                    logger.info(f'search engine {engine.engine_name} took {round(time.perf_counter() - start_time, 4)}s')
                    await rd.setex(
                        f"wsrch:{engine.engine_name}:{query}",
                        3600 * 3,
                        json.dumps(search_res, ensure_ascii=False),
                    )
            except Exception as e:
                stack_trace = traceback.format_exc()
                fire_logger.error(
                    "Search Engine %s Error: %s", engine.engine_name, stack_trace
                )
                search_res = f"Error: {str(e)}"
            return engine.engine_name, search_res
        else:
            try:
                start_time = time.perf_counter()
                search_res = await engine.search(query, num)
                logger.info(f'search engine {engine.engine_name} took {round(time.perf_counter() - start_time, 4)}s')
            except Exception as e:
                stack_trace = traceback.format_exc()
                fire_logger.error(
                    "Search Engine %s Error: %s", engine.engine_name, stack_trace
                )
                search_res = f"Error: {str(e)}"
            return engine.engine_name, search_res

    @tool_api
    async def web_search(self, query: str, num) -> dict:
        """
        Performs a web search using multiple search engines concurrently.

        Args:
            query (str): The search query string.

        Returns:
            dict: A dictionary where the keys are the class names of the search engines
                  and the values are dictionaries containing search results or error messages.
                  The inner dictionary keys are result index numbers, and the values are
                  dictionaries with 'title', 'snippet', and 'link' for each result.
        """

        tasks = [
            self.search_with_timeout(engine, query, num)
            for engine in self.search_engines
        ]

        results = await asyncio.gather(*tasks)


        return {class_name: search_res for class_name, search_res in results}
