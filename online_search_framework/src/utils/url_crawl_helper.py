import asyncio
import json

import aiohttp
import mmh3
import redis.asyncio.lock

from .cache import rd


# kami_index = MeiliIndex(
#     config["meilisearch.url"], config["meilisearch.api_key"], "kami_search"
# )


class CrawlHelper:
    def __init__(self, api_url: str, timeout: str):
        self.api_url = api_url
        self.parse_timeout = timeout
        self.total_timeout = timeout

    async def batch_process(self, urls: list[str]):
        async with aiohttp.ClientSession() as session:
            tasks = [self._process(url, session) for url in urls]
            results = await asyncio.gather(*tasks)
        return results

    async def _api_call(self, target: str, session: aiohttp.ClientSession):
        result = dict(url=target, title="", raw="", summary="")
        try:
            async with session.post(
                    self.api_url,
                    json={
                        "urls": [target],
                        "parse_timeout": self.parse_timeout,
                        "total_timeout": self.total_timeout,
                    },
                    timeout=aiohttp.ClientTimeout(1.5),
            ) as resp:
                resp = await resp.json()
                if resp:
                    resp = resp[0]
                    result["title"] = resp["title"]
                    result["raw"] = resp["content"]
                    result["summary"] = resp["text_content"]
        except Exception as _:
            pass
        return result

    async def _process(self, url: str, session: aiohttp.ClientSession):
        doc_id = mmh3.hash_bytes(url).hex()
        # redis 分布式锁，考虑并发以及多实例部署时不重复解析相同的url
        async with redis.asyncio.lock.Lock(
                rd, f"lock:{doc_id}", timeout=60
        ):  # timeout: 考虑程序手动中断等情况，防止死锁
            data = await rd.get(f"wsrch:cache:{doc_id}")
            if data:
                return json.loads(data)  # type: ignore

            # doc = await kami_index.get_document(doc_id)
            # if doc:
            #     await rd.setex(
            #         f"wsrch:cache:{doc_id}", 3600 * 3, json.dumps(doc, ensure_ascii=False)
            #     )  # hot data
            #     return doc

            result = await self._api_call(url, session)
            if not result["summary"]:  # 网页解析失败
                await rd.set(f"wsrch:failed:{doc_id}", url)  # 留待后续人工处理
                return result

            # doc = dict(id=doc_id)
            # doc.update(result)
            # await kami_index.add_documents([doc])

            await rd.setex(
                f"wsrch:cache:{doc_id}", 3600 * 3, json.dumps(result, ensure_ascii=False)
            )
        return result
