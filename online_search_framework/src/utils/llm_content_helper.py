import re, json, ast
from typing_extensions import Optional
from loguru import logger

class ContentHelper:

    def __init__(self):
        self.llm_total_responds = ""
        self.title = ""
        self.tts_start = False
        self.pre_tts_start = False



    def append_content(self, part: str) -> Optional[str]:
        to_return = None
        count = self.llm_total_responds.count("\u001F")
        if count == 1:
            if not self.tts_start:
                pre_title = self.llm_total_responds.split("\u001F", 1)[0]
                self.title = re.sub(r"[*#]+", "", pre_title)
                self.tts_start = True

        self.llm_total_responds = self.llm_total_responds + part
        return to_return

