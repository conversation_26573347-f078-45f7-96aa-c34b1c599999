from typing import Any, Coroutine

import requests
from typing_extensions import Optional
from configuration import config
import src.utils.news_error_code as error_code

api_key = config["gaode"]["api_key"]
url_regeo = config["gaode"]["regeo"]["url"]


def _request_re_geo(lat: str, lon: str) -> tuple[None, dict[str, str]] | None | Any:
    error = None
    try:
        location = format(float(lon), ".6f") + "," + format(float(lat), ".6f")
    except ValueError as e:
        error = {"code": error_code.error_code_location_return_error, "msg": f"Invalid latitude or longitude: {e}"}
        print(f"Invalid latitude or longitude: {e}")
        return None,error

    param = {"key": api_key, "location": location, "radius": "100", "output": "JSON"}
    session = requests.Session()
    try:
        with session.post(url=url_regeo, params=param) as response:
            print(f"!!!!!!!!!!!!!!!!!!!!    {response.status_code}")
            if response.status_code == 200:
                return response.json(),error
            else:
                error = {"code":error_code.error_code_location_http_error,"msg":response.reason}
                return None, error
    except requests.RequestException as e:
        error = {"code":error_code.error_code_location_http_error,"msg":f"Request location failed: {str(e)}"}
        print(f"Request failed: {e}")
    finally:
        session.close()
    return None,error
    

async def request_re_geo_rough(lat: str, lon: str)-> tuple[None, Any] | tuple[str, dict[str, str] | None] | tuple[
    None, dict[str, str | Any]] | None:
    position_json,request_error = _request_re_geo(lat=lat, lon=lon)
    error = None
    if request_error:
        return None,request_error
    if isinstance(position_json, dict):
        if "1" == position_json["status"] and position_json["regeocode"]:
            position = ""
            try:
                regeocode = position_json["regeocode"]

                addressComponent = regeocode["addressComponent"]
                position += addressComponent.get("province", "")
                city_list = addressComponent.get("city")
                if isinstance(city_list, str):
                    position += city_list
                elif isinstance(city_list, list) and 0 < len(city_list) and isinstance(city_list[0], str):
                    position += city_list[0]
            except BaseException as e:
                import traceback

                traceback.print_exc()
                error = {"code":error_code.error_code_location_format_error,"msg":str(e)}
            return position,error
        else:
            error_msg = position_json["info"]
            error = {"code":error_code.error_code_location_return_error,"msg":error_msg}
            return None,error
    else:
        error = {"code": error_code.error_code_location_format_error, "msg": "position is not json"}
        return None,error

async def request_re_geo_detail(lat: str, lon: str)-> Optional[str]:
    position_json = _request_re_geo(lat=lat, lon=lon)
    if not position_json:
        return None
    if isinstance(position_json, dict):
        if "1" == position_json["status"] and position_json["regeocode"]:
            regeocode = position_json["regeocode"]
            position = ""
            addressComponent = regeocode["addressComponent"]
            position += addressComponent.get("province", "")
            city_list = addressComponent.get("city")
            if isinstance(city_list, str):
                position += city_list
            elif isinstance(city_list, list) and 0 < len(city_list) and isinstance(city_list[0], str):
                position += city_list[0]
            position += addressComponent.get("district", "")
            position += addressComponent.get("township", "")
            neighborhood = addressComponent["neighborhood"]
            building = addressComponent["building"]
            streetNumber = addressComponent["streetNumber"]
            if streetNumber: 
                position += streetNumber.get("street", "")
                position += streetNumber.get("number", "")
            elif neighborhood:
                position += neighborhood.get("name", "")
            elif building:
                position += building.get("name", "")
            return position
        else:
            return None
    else: 
        return None