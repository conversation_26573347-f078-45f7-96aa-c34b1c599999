import asyncio
import uuid

from typing import Optional
from typing import Union, Dict, List

from src.utils.llms.openai_SDK import OpenAI_LLM
from src.utils.web_content_scrape import WebScrape, WebScrapePy
from src.utils.memory.memory_base import MemoryBase



# class TravelRecommendAgent:
#      def __init__(
#             self,
#             intent_llm: OpenAI_LLM,
#             summary_llm: OpenAI_LLM,
#             topk_method: Union[TopkBase, None],
#             action_executor: ActionExecutor,
#             public_memory: MemoryBase,
#             crawler: Union[WebScrape, WebScrapePy],
#             dojo: None = None,
#             detect: bool = False,
#             sensitive_config: Optional[Dict[str, str]] = None,
#             search_nums: int = 10,
#             rest_k: int = 5,
#             using_cached_link_first: bool = False,
#             reranker_using_title: bool = False,
#             truncate_length: int = 1500,
#             router_system_prompt: str = router_system_zh,
#             router_user_prompt: str = router_user_zh,
#     ) -> None:
#         self.action_executor = action_executor
#         self.intent_llm = intent_llm
#         self.summary_llm = summary_llm
#         self.topk_method = topk_method
#         self.crawler = crawler
#         self.public_memory = public_memory
#         self.detect = detect
#         self.dojo = dojo
#         if sensitive_config is None:
#             self.sensitive_config = {}
#         else:
#             self.sensitive_config = sensitive_config
#         self.rest_k = rest_k
#         self.search_nums = search_nums
#         self.using_cached_link_first = using_cached_link_first
#         self.reranker_using_title = reranker_using_title
#         self.truncate_length = truncate_length
#         self.router_system_prompt = router_system_prompt
#         self.router_user_prompt = router_user_prompt