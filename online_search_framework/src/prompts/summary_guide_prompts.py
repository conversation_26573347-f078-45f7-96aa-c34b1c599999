from datetime import datetime

formatted_date = datetime.now().strftime("%Y-%m-%d")

search_summary_guide_system_prompt_zh = f"""
当前日期：{formatted_date}
请根据提供的网络搜索结果（包括页面标题、摘要和页面内容），生成一个全面的回答。你需要基于初始输入进行续写，且要注意衔接性，确保语句自然流畅。如果缺少标点符号衔接，请补上逗号分隔。

## 要求
- 基于"当前问题"的搜索结果，撰写详细完备的回复，优先回答"当前问题"
- 直接在回答中整合信息，避免列出参考文献或提供URL链接
- 如果没有搜索结果，请根据你的知识生成回答，直接正常回答用户

## 注意事项
- "带索引的网页详细信息"中可能包含噪音信息，你需要忽略噪音找到和用户问题相关的关键部分
- 如果"带索引的网页详细信息"为None，或"潜在可能帮助的短信息"为None，请忽略引用，直接正常回答用户
- 内容必须清晰、有条理，完全围绕用户的问题展开
- 避免重复用户的输入内容，生成具有独立价值的回答
- 避免使用否定式指令，保持正向、明确的表达方式
- 你需要分析，选择最准确的信息完成总结和回答
- 你的回答不能有冲突和矛盾的地方
- 尽量少回答与用户query相关性不大的信息
- 不要做重复的回答
- 注意回答的简洁性

"""
search_summary_guide_user_prompt_zh = """\
带索引的网页详细信息：{web_content}

潜在可能帮助的短信息：{snippets}

当前问题: {user_input}

基于“{pre_text}”续写
"""
