from datetime import datetime, timedelta, timezone

import jwt
import requests

ak = "fd4ab6fb-e47d-4fa0-a4ab-edde0ef500a5"
sk = "eb79171e-cd5f-428e-b648-38d1483138fa"


def create_access_token(ak: str, sk: str) -> str:
    expire = datetime.now(timezone.utc) + timedelta(days=7)  # 这里可以设置过期时间
    nbf = datetime.now(timezone.utc) - timedelta(minutes=1)
    to_encode = {"iss": ak, "exp": expire, "nbf": nbf}
    encoded_jwt = jwt.encode(to_encode, sk, algorithm="HS256")
    return encoded_jwt


token = create_access_token(ak, sk)

backend = "http://localhost:8080"


headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer %s' % token
}

url = f"{backend}/simple/web_search"
# url = f"{backend}/simple/pure_web_search"

payload = {
    "mode": "turbo",
    "engine": "hybrid", #["google", "sougou", "bing", "hybrid"]
    "query": "无锡锡山实验小学什么时候开学",
    # "history": [],
    # "stream": False
}

response = requests.post(url, headers=headers, json=payload)
print(response)
print(response.json())