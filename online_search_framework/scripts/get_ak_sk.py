import requests

backend = "http://cubic.sensetime.com:8080"
r = requests.post(
    f"{backend}/api/v1/user/access-token",
    data={
        "username": "<EMAIL>",  # remeber to modify
        "password": "password",  # remeber to modify
    },
)

auth_token = f"Bearer {r.json()['access_token']}"

data = requests.get(
    f"{backend}/api/v1/user/me", headers={"Authorization": auth_token}
).json()

print(f"ak: {data['id']}")
print(f"sk: {data['sk']}")
