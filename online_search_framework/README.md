# online_search_framework

新建一个虚拟环境然后
`pip install -r requirements.txt`
open src folder and run `python demo.py`

1. Input query for testing: 特朗普最近怎么了
2. 等待结果...

可以看code_framework.png了解class继承关系
.ipynb只是我用来测试的，没有什么用

AllMessageMemory会记住之前所有的历史对话
ZeroMessageMemory只会记住system prompt
KMessageMemory会记住最近的K对user和assistant的对话

# 开发测试流程
由于测试都是基于docker镜像，所以需要首先安装docker engine。可以参考这里：https://docs.docker.com/engine/install/
注意本机不需要有docker gpu的支持。

## 流程：
* 首先修改代码
* 然后本机启动一个镜像：`sudo docker compose -f docker-compose-local.yml up`
* 然后可以发请求测试，有个冒烟脚本可以参考一下 `./benchmark/smoke.py`，测试命令：
    ```
    python smoke.py --url http://localhost:8080/simple/web_search/v2
    ```
* 如果出现问题，就修改代码，修改完后，要重新起服务，要先停掉服务：`sudo docker compose -f docker-compose-local.yml down`
* 然后再用上面的`up`命令重启服务，注意这时候应该会重新build镜像，会复用之前的cache，build速度应该很快
* 重复上面的过程，直到测试通过。

## 提交mr以及merge代码
* 在本地开发某个新feature时，首先基于最新的dev分支建立本地分支，分支名以自己的名字开头，像这样：
    ```
    git checkout dev
    git pull
    git branch trump/xxfeature
    ```
* 本地开发、测试都完成后，将分支push到远程：
    ```
    git push origin trump/xxfeature
    ```
* 然后向dev分支提一个mr，在web上操作
* 如果mr提示有冲突，状态为 `merge blocked`，这时候需要本地解决冲突，解决冲突前可以先备份自己的代码到一个临时分支：
    ```
    git checkout trump/xxfeature
    git branch trump/xxfeature_back
    git checkout dev   ## 切到dev分支pull最新代码
    git pull
    git checkout trump/xxfeature   ## 切换到自己的feature 分支准备做rebase
    git rebase dev    ## 一个个冲突解决
    git rebase --contie  ## 解决完一个冲突后执行，屏幕有提示
    ......  ## 解决所有的冲突
    git push origin -f trump/xxfeature   ## 重新向远程push代码，会覆盖原来的代码，所以之前有备份的操作
    ```
* 经过上面的操作后，mr应该就没有冲突了。review通过后，reviewer会merge，并删除原分支。

# testing环境和正式环境的部署
之前已经配置好了gitlab-ci 将dev分支deploy到26机器上作为测试环境，将master分支deploy到27机器上作为正式环境。

`selected_llm_server` 用环境变量传递，在./.gitlab-ci.yml 中已经设置好了。

# 代码的自动lint和format
* 在开发环境装好`pre-commit`，依赖已经放在了`./requirements.txt`中，然后运行`pre-commit install`，
  则以后每次`git commit`的时候，都会自动做lint和format。相关命令：
  ```
  pip install pre-commit
  pre-commit install  
  ```
* gitlab-ci 也会强制做，不通过的话，会block后面的pipeline，比如deploy

# 日志的收集和查看
日志查看网址：[日志网址](http://gm-cloud.sensetime.com:9686/search)，服务名有两个，一个是`kami_search`，对应正式环境；另一个是`kami_search_testing`，对应测试环境。

日志拉取脚本： `./benchmark/datasets/pull_data_from_logfire.py`

日志里面的耗时的解释：keywords是第一阶段，改写模型的输出；sources是改写到搜索api返回的时间；fetch是爬+清洗的时间；TTFT是query到模型首字时间
