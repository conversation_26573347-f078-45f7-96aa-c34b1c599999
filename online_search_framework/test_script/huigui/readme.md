## 回归测试执行步骤

先启动本地服务，然后执行以下步骤：

1. 从ones导出缺陷列表,内容包括: id, 标题, 描述
2. 执行转换脚本，将ones描述进行结构化提取： `python3 trans_ones_excel.py GACCLM-缺陷.csv GACCLM-缺陷.xlsx`
3. 人工校验转换生成的 GACCLM-缺陷.xlsx 文件，确保 request_body 字段是合法的 JSON 格式，以及 query的多轮问答是否符合逻辑顺序
4. 执行回归测试  `python3 bad_case_Ai_News.py`
5. 步骤3将生成 Bad_Case_Result_Ai_news_<时间>.xlsx的结果文件，以及跳过的用例列表 skipped_cases.txt
6. 人工分析结果文件，确认是否有误判的情况

## 需要安装下面这些库
pip install colorlog openpyxl requests pandas