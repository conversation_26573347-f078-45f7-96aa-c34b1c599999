#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import pandas as pd
import re
import json
import os
import urllib.request
import urllib.error
import ssl
import time
import unicodedata

from category_mapping import NEWS_SCHEMA, CATEGORY_PROMPT_MAP


def extract_description_info(
    title,
    description,
    api_base,
    api_key,
    model,
    schema=None,
    timeout=60,
    retries=1,
    user_prompt=None,
):
    """
    使用 OpenAI 兼容接口，对 description 文本进行结构化信息抽取。

    参数:
        description (str): 待抽取的原始描述文本。
        api_base (str): OpenAI 兼容服务的 Base URL，例如 https://api.openai.com 或本地/私有部署地址。
        api_key (str): API Key。
        model (str): 模型名称，例如 gpt-4o-mini、gpt-3.5-turbo、qwen-turbo、glm-4 等（视服务而定）。
        schema (dict|None): 期望的输出字段示例（用于提示 LLM）。为空则使用内置的通用 schema。
        timeout (int): 请求超时时间（秒）。
        retries (int): 失败后的重试次数。

    返回:
        dict: 解析后的结构化结果。若解析失败，返回包含 error 字段的字典。

    说明:
        - 兼容 /v1/chat/completions 与 /chat/completions 两种常见路径。
        - 要求模型严格输出 JSON；若返回为纯文本，尝试从中提取 JSON。
        - 若某些字段无法识别，统一置为 null 或空数组。
    """
    if not isinstance(description, str) or not description.strip():
        return {"error": {"type": "invalid_input", "message": "empty description"}}

    if schema is None:
        # 一个通用、可扩展的默认 schema，可按需调整
        schema = {
            "real_output": "string",
            "expected_output": ["string"],
            "request_body": "<request_body>",
        }

    # 构建消息
    system_prompt = (
        "你是一名信息抽取助手。请仅输出一个 JSON 对象，不要包含任何额外文字、标点或解释。"
        "字段必须完整给出；若无法识别某字段，请给 null（标量）或 []（列表）。"
    )

    if user_prompt and isinstance(user_prompt, str):
        user_instructions = user_prompt
    else:
        print("error: user_prompt is required")
        sys.exit(1)

    payload = {
        "model": model,
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_instructions},
        ],
        "temperature": 0,
        # 一些兼容实现支持该字段；若不支持会忽略
        "response_format": {"type": "json_object"},
    }

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}",
    }

    # 兼容常见路径
    candidate_paths = [
        "/v1/chat/completions",
        "/chat/completions",
    ]

    def _request(url):
        data = json.dumps(payload, ensure_ascii=False).encode("utf-8")
        req = urllib.request.Request(url, data=data, headers=headers, method="POST")
        # 忽略证书验证（如果是本地自签名服务），根据需要可移除
        ctx = ssl.create_default_context()
        try:
            with urllib.request.urlopen(req, timeout=timeout, context=ctx) as resp:
                return json.loads(resp.read().decode("utf-8"))
        except urllib.error.HTTPError as e:
            try:
                err_body = e.read().decode("utf-8")
            except Exception:
                err_body = str(e)
            raise RuntimeError(f"HTTP {e.code}: {err_body}")
        except urllib.error.URLError as e:
            raise RuntimeError(f"URL error: {e}")

    last_err = None
    for attempt in range(retries + 1):
        for path in candidate_paths:
            url = api_base.rstrip("/") + path
            try:
                resp_json = _request(url)
                # 期望 OpenAI 兼容返回结构
                content = (
                    resp_json.get("choices", [{}])[0]
                    .get("message", {})
                    .get("content", "")
                )
                if not content:
                    return {
                        "error": {
                            "type": "empty_content",
                            "message": f"empty content from provider: {resp_json}",
                        }
                    }

                # 解析 JSON（严格/宽松）
                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    # 宽松提取第一段 {...} JSON
                    import re as _re

                    match = _re.search(r"\{[\s\S]*\}", content)
                    if match:
                        try:
                            return json.loads(match.group(0))
                        except Exception:
                            pass
                    return {"raw": content}
            except Exception as e:
                last_err = e
                # 尝试下一个路径或下一次重试
                continue
        # 重试间隔
        if attempt < retries:
            try:
                import time as _time

                _time.sleep(0.8 * (attempt + 1))
            except Exception:
                pass

    return {
        "error": {
            "type": "request_failed",
            "message": str(last_err) if last_err else "unknown",
        }
    }


def extract_description_info_from_env(title, description, model=None, **kwargs):
    """
    从环境变量读取 OpenAI 兼容配置并调用 `extract_description_info`。

    识别的环境变量（依次回退）：
        - Base URL: OPENAI_BASE_URL / OPENAI_API_BASE / OPENAI_API_HOST / API_BASE
        - API Key : OPENAI_API_KEY / API_KEY / OPENAI_KEY
        - Model   : 传入的 model 参数优先，否则 OPENAI_MODEL / MODEL / 默认 'gpt-4o-mini'

    额外的可选参数通过 **kwargs 传入（如 schema/timeout/retries）。
    """
    base_url = "http://************:8099/v1"
    api_key = "1s3963nw8802M4O55yMuU6x37tOYQ682"
    model = "SenseAuto-Chat"

    return extract_description_info(
        title, description, base_url, api_key, model, **kwargs
    )


def parse_news_title(title_str):
    """
    解析新闻标题，提取类别、版本号和实际内容。

    标题格式示例：
    - 【AI新闻V1.0.1】设置偏好 马斯克美国党话题+军事新闻类型（有版本）
    - 【AI新闻】设置偏好 马斯克美国党话题+军事新闻类型（无版本，version 返回 None）
    - 【AI交互桌面-API】设置偏好 …（无版本，连字符不代表版本，version 返回 None）

    Args:
        title_str (str): 原始的标题字符串。

    Returns:
        dict: 包含解析结果的字典，如 {'category': 'AI新闻', 'version': 'V1.0.1', 'content': '设置偏好 马斯克美国党话题+军事新闻类型'}。
              如果格式不匹配，返回 None。
    """
    # 先尝试“有版本”的模式：
    # 仅当版本以 V/v 开头，后接数字与可选的 .数字 段，避免将 "API" 等误识为版本。
    # 示例：V1、v2、V1.0、V1.0.1、v3.2.0
    pattern_with_ver = r"【(.+?)([Vv]\d+(?:\.\d+){0,3})】(.*)"

    if isinstance(title_str, str):
        title_str = title_str.strip()
    else:
        return None

    match = re.fullmatch(pattern_with_ver, title_str)
    if match:
        category = match.group(1).strip()
        version = match.group(2).strip()
        return {"category": category, "version": version}

    # 再尝试“无版本”的模式：
    # 【开始】(任意非贪婪字符作为类别)】(剩余内容)
    pattern_without_ver = r"【(.+?)】(.*)"
    match2 = re.fullmatch(pattern_without_ver, title_str)
    if match2:
        category = match2.group(1).strip()
        return {"category": category, "version": None}

    # 如果都不匹配，返回 None
    return None


def main():
    """
    主函数：读取CSV，解析标题，并输出结构化结果至 Excel 文件。
    """
    # 1. 检查命令行参数
    if len(sys.argv) < 3:
        print("----------------------------------------------------")
        print("用法示例:")
        print("  python trans_ones_excel.py input.csv output.xlsx")
        print("----------------------------------------------------")
        print("请提供输入的 CSV 文件和输出的 Excel 文件名。")
        sys.exit(1)

    input_path = sys.argv[1]
    output_path = sys.argv[2]

    # 解析 Excel 输出路径：第二个参数即输出路径；若后缀非 .xlsx，则自动替换为 .xlsx
    excel_out_path = output_path
    base, ext = os.path.splitext(excel_out_path)
    if ext.lower() != ".xlsx":
        excel_out_path = base + ".xlsx"
        print(f"提示: 输出文件扩展名已自动更改为 .xlsx -> {excel_out_path}")

    # 2. 读取 CSV 文件
    try:
        print(f"正在读取文件: {input_path}...")
        df = pd.read_csv(input_path, encoding="utf-8-sig")
    except FileNotFoundError:
        print(f"错误: 文件 '{input_path}' 未找到！")
        sys.exit(1)
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        sys.exit(1)

    # 3. 智能识别标题列
    title_col = None
    common_title_names = ["title", "标题", "名称", "subject"]

    for col in df.columns:
        if str(col).strip().lower() in common_title_names:
            title_col = col
            break

    if title_col is None:
        print("\n错误: 无法识别标题列！")
        print(f"检测到的列名是: {df.columns.tolist()}")
        sys.exit(1)

    print(f"识别到标题列: '{title_col}'")

    # 4. 逐行处理并解析数据（不再筛选标题）
    print("\n开始处理数据并解析标题...")
    result_list = []

    news_df = df
    if news_df.empty:
        print("提示: 输入数据为空。")
        sys.exit(0)

    # 日志与统计
    print("已启用描述信息的 LLM 结构化抽取。")
    llm_success = 0
    llm_failed = 0
    llm_skipped = 0
    llm_t0_total = time.time()

    for index, row in news_df.iterrows():
        original_title = row[title_col]
        parsed_info = parse_news_title(original_title)

        # 创建一个字典来存储当前行的所有信息
        result_item = {
            "id": row.get("ID", None),  # 仅使用原始ID列的值，缺失则为 None
            "original_title": original_title,
            "parsed_data": None,  # 默认为 None
            "description": row.get("描述", ""),  # 获取描述信息
        }

        # 调用大模型对描述进行结构化抽取（带日志与耗时）
        desc_text = result_item.get("description", "")
        if not isinstance(desc_text, str) or not desc_text.strip():
            print(f"[LLM] 第 {index} 行（id={result_item['id']}）描述为空，跳过抽取。")
            # 将错误对象写入 parsed_data.description
            if not isinstance(parsed_info, dict):
                parsed_info = {}
            parsed_info["description"] = {
                "error": {"type": "invalid_input", "message": "empty description"}
            }
            result_item["parsed_data"] = parsed_info
            llm_skipped += 1
        else:
            print(
                f"[LLM] 开始抽取第 {index} 行（id={result_item['id']}），描述长度：{len(desc_text)}"
            )
            llm_t0 = time.time()
            try:
                # 为该行按标题与 CATEGORY_PROMPT_MAP 的 key 做子串匹配，得到行级别的 category_arg
                category_arg = None
                title_lower = str(original_title or "").lower()
                for k in CATEGORY_PROMPT_MAP.keys():
                    kk = str(k).strip()
                    if kk and kk.lower() in title_lower:
                        category_arg = k
                        break
                # 决定使用的 category：优先命令行，其次标题解析
                chosen_category = category_arg
                if not chosen_category and isinstance(parsed_info, dict):
                    chosen_category = parsed_info.get("category")

                # 基于 category 选择 prompt 与 schema
                prompt_to_use = None
                schema_to_use = None
                cfg = (
                    CATEGORY_PROMPT_MAP.get(chosen_category)
                    if chosen_category
                    else None
                )
                if cfg:
                    schema_to_use = cfg.get("schema")
                    p = cfg.get("prompt")
                    if callable(p):
                        prompt_to_use = p(
                            original_title, desc_text, schema_to_use or NEWS_SCHEMA
                        )
                    elif isinstance(p, str):
                        # 允许在模板字符串中使用 {schema} / {title} / {description}
                        try:
                            prompt_to_use = p.format(
                                schema=json.dumps(
                                    (schema_to_use or NEWS_SCHEMA),
                                    ensure_ascii=False,
                                    indent=2,
                                ),
                                title=original_title,
                                description=desc_text,
                            )
                        except Exception:
                            prompt_to_use = p

                extracted = extract_description_info_from_env(
                    original_title,
                    desc_text,
                    schema=schema_to_use,
                    user_prompt=prompt_to_use,
                )
            except Exception as e:
                extracted = {"error": {"type": "exception", "message": str(e)}}
            dt = time.time() - llm_t0
            if isinstance(extracted, dict) and "error" in extracted:
                err = extracted.get("error", {})
                print(
                    f"[LLM] 抽取失败 第 {index} 行：{err.get('type')} - {err.get('message')}，用时 {dt:.2f}s"
                )
                llm_failed += 1
            else:
                preview_keys = (
                    list(extracted.keys())[:5] if isinstance(extracted, dict) else []
                )
                print(
                    f"[LLM] 抽取完成 第 {index} 行，用时 {dt:.2f}s，字段：{preview_keys}"
                )
                llm_success += 1
            # 合并到 parsed_data 中
            if not isinstance(parsed_info, dict):
                parsed_info = {}
            parsed_info["description"] = extracted
            result_item["parsed_data"] = parsed_info

        # 如果标题解析成功但上面未覆盖 parsed_data（例如描述为空时），也要设置 parsed_data
        if not result_item.get("parsed_data"):
            if not isinstance(parsed_info, dict):
                parsed_info = {}
            result_item["parsed_data"] = parsed_info

        result_list.append(result_item)

    print(f"处理完成，共处理 {len(result_list)} 条记录。")
    # 汇总 LLM 抽取统计
    dt_total = time.time() - llm_t0_total
    print(
        f"[LLM] 抽取统计：成功 {llm_success}，失败 {llm_failed}，跳过 {llm_skipped}，总耗时 {dt_total:.2f}s"
    )

    # 5. 将结果保存为 Excel：包含 id、original_title、original_description（顶层原始描述）以及 parsed_data 的子字段
    try:
        print(f"正在将结果保存到 Excel 文件: {excel_out_path}")

        # 扁平化 dict 的工具：a.b.c 形式的列名
        def _flatten(d, parent_key=""):
            out = {}
            if not isinstance(d, dict):
                return out
            for k, v in d.items():
                key = f"{parent_key}.{k}" if parent_key else str(k)
                if isinstance(v, dict):
                    # 不展开 request_body，直接作为一个字段保留
                    if str(k) == "request_body":
                        out[key] = v
                    else:
                        out.update(_flatten(v, key))
                else:
                    # list 等复杂类型不再深入，保留为 JSON 字符串在写入时处理
                    out[key] = v
            return out

        # 汇总所有 parsed_data 的顶层键与扁平化后的子键，作为列
        # 需求：不导出 parsed_data 顶层的 description 列，但保留其子列，且列名不带 "description." 前缀；
        # 另新增列 original_description，用于保存顶层原始描述文本（即每条记录的 result_item['description']）。
        pd_keys = set()
        col_source_map = {}  # 列名 -> (source_type, source_key)
        for item in result_list:
            pdict = item.get("parsed_data")
            if isinstance(pdict, dict):
                # 顶层键保留（例如 content 等），但排除 description/category/version 本身（它们将单独处理或放置固定位置）
                for k in pdict.keys():
                    if k in {"description", "category", "version"}:
                        continue
                    pd_keys.add(k)
                    # 顶层字段从 pdict 取
                    col_source_map[k] = ("top", k)

                # 子键扁平化（例如 description.real_output 等）
                flat = _flatten(pdict)
                for fk in flat.keys():
                    if fk == "description":
                        # 防御性：不会出现于 _flatten 结果，但保持一致
                        continue
                    if fk.startswith("description."):
                        # 去掉前缀后的子列名
                        child = fk.split(".", 1)[1]
                        pd_keys.add(child)
                        # 子列从 flat 的原始键取值
                        col_source_map[child] = ("flat", fk)
                    else:
                        # 其他扁平化键保持原样；同时排除 category/version 以避免重复
                        if fk in {"category", "version"}:
                            continue
                        pd_keys.add(fk)
                        col_source_map[fk] = ("flat", fk)

        # 固定列顺序：前置 id, category, version, original_title；最后 original_description
        columns = (
            ["id", "category", "version", "original_title"]
            + sorted(pd_keys)
            + ["original_description"]
        )

        rows = []
        for item in result_list:
            row = {
                "id": item.get("id"),
                "category": None,
                "version": None,
                "original_title": item.get("original_title"),
                "original_description": item.get("description"),
            }
            pdict = (
                item.get("parsed_data")
                if isinstance(item.get("parsed_data"), dict)
                else {}
            )
            flat = _flatten(pdict)
            # 固定列中的 category/version 从顶层 parsed_data 读取
            if isinstance(pdict, dict):
                row["category"] = pdict.get("category")
                row["version"] = pdict.get("version")
            for col in pd_keys:
                src = col_source_map.get(col)
                v = None
                if src:
                    src_type, src_key = src
                    if src_type == "flat":
                        v = flat.get(src_key)
                    else:
                        v = pdict.get(src_key)
                # 如果是嵌套对象或数组，序列化为 JSON 字符串；否则原样保留
                if isinstance(v, (dict, list)):
                    try:
                        row[col] = json.dumps(v, ensure_ascii=False)
                    except Exception:
                        row[col] = str(v)
                else:
                    row[col] = v
            rows.append(row)

        df_out = pd.DataFrame(rows, columns=columns)

        # 写入 Excel 并设置样式：居中、自动换行与合适列宽
        # 说明：使用 openpyxl 引擎，随后对 Worksheet 应用样式
        from openpyxl.styles import Alignment
        from openpyxl.utils import get_column_letter

        # 计算列宽：按内容的“显示宽度”（东亚全角字符按 2 计算）估算
        def display_len(s: str) -> int:
            if s is None:
                return 0
            if not isinstance(s, str):
                s = str(s)
            total = 0
            for ch in s:
                # 全角/宽字符按 2 计算，其余按 1
                total += 2 if unicodedata.east_asian_width(ch) in ("F", "W") else 1
            return total

        col_widths = {}
        for col in df_out.columns:
            maxw = display_len(col)
            # 转字符串前先把 NaN 变为空串
            series = df_out[col]
            # 避免巨大文本把列撑太宽，设定上限
            for v in series:
                if pd.isna(v):
                    s = ""
                elif isinstance(v, (dict, list)):
                    try:
                        s = json.dumps(v, ensure_ascii=False)
                    except Exception:
                        s = str(v)
                else:
                    s = str(v)
                maxw = max(maxw, display_len(s))
            # 为可读性设置宽度范围（字符数）：最小 12，最大 60
            col_widths[col] = max(12, min(maxw + 2, 60))

        # 使用 ExcelWriter 写入并应用样式
        with pd.ExcelWriter(excel_out_path, engine="openpyxl") as writer:
            df_out.to_excel(writer, index=False, sheet_name="Sheet1")
            ws = writer.sheets["Sheet1"]

            # 设置列宽
            for idx, col_name in enumerate(df_out.columns, start=1):
                letter = get_column_letter(idx)
                ws.column_dimensions[letter].width = col_widths.get(col_name, 20)

            # 单元格样式：水平/垂直居中 + 自动换行
            align = Alignment(horizontal="center", vertical="center", wrap_text=True)
            for row in ws.iter_rows(
                min_row=1, max_row=ws.max_row, min_col=1, max_col=ws.max_column
            ):
                for cell in row:
                    cell.alignment = align

        print("Excel 文件保存成功！")
    except Exception as e:
        print(f"保存 Excel 文件时发生错误: {e}")


if __name__ == "__main__":
    main()
