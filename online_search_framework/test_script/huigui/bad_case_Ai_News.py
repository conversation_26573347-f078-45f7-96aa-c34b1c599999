from bad_case_base_function import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    code_middle_fail,
    code_complete,
    ExcelResultWriter,
)
from typing_extensions import Optional
from dataclasses import dataclass
import os
import aiohttp
import time
import asyncio
import json
import logging
import colorlog

# 本脚本专用日志配置（不依赖项目其他部分）


def get_logger(level=logging.INFO):
    # 创建logger对象
    logger = logging.getLogger()
    logger.setLevel(level)
    # 创建控制台日志处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    # 定义颜色输出格式
    color_formatter = colorlog.ColoredFormatter(
        "%(log_color)s%(levelname)s: %(message)s",
        log_colors={
            "DEBUG": "cyan",
            "INFO": "green",
            "WARNING": "yellow",
            "ERROR": "red",
            "CRITICAL": "red,bg_white",
        },
    )
    # 将颜色输出格式添加到控制台日志处理器
    console_handler.setF<PERSON>atter(color_formatter)
    # 移除默认的handler
    for handler in logger.handlers:
        logger.removeHandler(handler)
    # 将控制台日志处理器添加到logger对象
    logger.addHandler(console_handler)
    return logger


logger = get_logger(logging.DEBUG)

file_path_in = os.path.dirname(os.path.abspath(__file__)) + "/" + "GACCLM-缺陷.xlsx"

# 使用可读的时间格式作为文件名后缀，例如 2025-09-04_16-30-00
human_read_time = time.strftime("%Y-%m-%d_%H-%M-%S", time.localtime())
file_path_out = (
    os.path.dirname(os.path.abspath(__file__))
    + "/"
    + f"Bad_Case_Result_Ai_news_{human_read_time}.xlsx"
)

excelHelper = ExcelHelper(file_path=file_path_in)
judgeHelper = JudgeHelper()


@dataclass
class Result:
    code: int = code_middle_fail
    query: str = ""
    result_text: str = ""


class BadCaseRunner:
    @staticmethod
    def _extract_judge_fields(judge_text: str) -> tuple[str, str]:
        """从 LLM 判定文本中提取 result 与 reason。

        期望格式：
            {
                "result": <判断结果>,
                "reason": <判断依据>
            }

        兼容以下情况：
        - 前后有说明性文本或 Markdown 代码块
        - JSON 内部换行、空格
        - 返回为空或非 JSON 时，返回空字符串
        """
        if not judge_text:
            return "", ""
        s = str(judge_text).strip()
        # 去除常见的 Markdown 代码块包裹
        if s.startswith("```"):
            # ```json\n{...}\n```
            try:
                fence_end = s.rfind("```")
                if fence_end > 0:
                    inner = s.split("\n", 1)[1][
                        : fence_end - (len(s.split("\n", 1)[0]) + 1)
                    ]
                    s = inner.strip()
            except Exception:
                pass

        # 直接尝试整体解析
        def try_parse_obj(text: str):
            try:
                obj = json.loads(text)
                if isinstance(obj, dict):
                    return obj
            except Exception:
                return None

        obj = try_parse_obj(s)
        if obj is None:
            # 尝试在文本里寻找第一个完整的大括号 JSON
            start = s.find("{")
            end = s.rfind("}")
            if start != -1 and end != -1 and end > start:
                candidate = s[start : end + 1]
                obj = try_parse_obj(candidate)

        if not isinstance(obj, dict):
            return "", ""

        result = obj.get("result")
        reason = obj.get("reason")
        return ("" if result is None else str(result)), (
            "" if reason is None else str(reason)
        )

    @staticmethod
    def check_query_json(query_json_str: str) -> Optional[dict]:
        try:
            return json.loads(query_json_str)
        except BaseException as e:
            logger.error(
                f"check_query_json: query_json_str: {query_json_str}, e: {str(e)}"
            )
            return None

    @staticmethod
    def build_header() -> dict:
        return {
            "Authorization": "Bearer app-0lG1fLjvC3qgQTZGEg3sl9gi",
            "Content-Type": "application/json",
        }

    @staticmethod
    def build_payload(
        request_body: dict,
        query_text: str,
        round_index: int,
        history: Optional[list] = None,
    ) -> dict:
        """封装单轮请求的 payload 构造逻辑。

        入参:
        - request_body: 原始请求体（用于提取 lat/lon）
        - query_text: 本轮 query 文本
        - round_index: 轮次索引，从 0 开始
        - conversation_id: 会话 ID（从第二轮起携带）
        """
        payload = dict()
        payload["query"] = query_text
        payload["engine"] = "tencent"
        payload["user_info"] = {
            "car_id": "fake_car_id",
            "user_id": "fake_user_id",
            "category": ["news_topic", "news_type"],
        }
        payload["location"] = {
            "lat": str(request_body.get("lat", "39.904989") or "39.904989"),
            "lon": str(request_body.get("lon", "116.4855") or "116.4855"),
        }
        if round_index > 0 and history:
            payload["history"] = history
        return payload

    @staticmethod
    async def run_single_case(request_body: dict) -> Result:
        """
        request_body的格式如下，lat与lon为可选参数，query为列表，如果是多轮对话，将存在多个元素
        request_body = {
            "query": ["xxx", "ccc"],
            "lat": 123.456,
            "lon": 78.901
        }
        """

        result = Result()
        # 收集多轮答案（仅在此函数内部使用）
        all_answers: list[str] = []

        try:
            queries = request_body.get("query")

            # 兼容字符串或列表输入；最终转换为非空的字符串列表
            if isinstance(queries, list):
                rounds = [q for q in queries if isinstance(q, str) and q.strip()]
            elif isinstance(queries, str):
                rounds = [queries] if queries.strip() else []
            else:
                rounds = []

            if not rounds:
                logger.error(
                    "new_run_single_case: request_body.query is empty or invalid"
                )
                return result

            history = []
            async with aiohttp.ClientSession() as session:
                for i, q in enumerate(rounds):
                    logger.info(f"new_run_single_case: round {i}, query: {q}")
                    payload = BadCaseRunner.build_payload(
                        request_body=request_body,
                        query_text=q,
                        round_index=i,
                        history=history,
                    )
                    logger.debug(f"new_run_single_case: payload: {payload}")
                    streamHelper = AgentStreamHelper()
                    try:
                        async with session.post(
                            "http://127.0.0.1:8080/gac/web_search/v1/news_query",
                            read_bufsize=4194304,
                            headers=BadCaseRunner.build_header(),
                            json=payload,
                        ) as response:
                            response.raise_for_status()

                            # 使用 AgentStreamHelper 的统一流式解析
                            async for raw_chunk in response.content.iter_any():
                                streamHelper.feed(raw_chunk)
                            streamHelper.flush()

                        result.code = code_complete
                        result.query = q
                        answer = streamHelper.get_answer()
                        logger.debug(f"[ANSWER]: {answer}")
                        history.append({"content": f"{q}", "role": "user"})
                        history.append(
                            {"content": streamHelper.get_answer(), "role": "assistant"}
                        )
                        # 累积多轮答案
                        if answer:
                            all_answers.append(answer)

                    except BaseException as e:
                        logger.error(
                            f"new_run_single_case: payload: {payload}, e: {str(e)}"
                        )
                        # 继续尝试后续轮次
                        continue

            # 循环结束后，合并所有轮次的问答
            if all_answers:
                merged_blocks = []
                for idx, ans in enumerate(all_answers, start=1):
                    q_text = rounds[idx - 1] if idx - 1 < len(rounds) else ""
                    merged_blocks.append(f"--- Round {idx} ---\nQ: {q_text}\nA:\n{ans}")
                result.result_text = "\n\n".join(merged_blocks)

        except BaseException as e:
            logger.error(
                f"new_run_single_case: request_body: {request_body}, e: {str(e)}"
            )

        return result

    async def run_bad_cases(self):
        writer = ExcelResultWriter(excelHelper)
        skipped_case_list = []
        async for row in excelHelper.read_rows("AI新闻"):
            id = row.get("id")
            original_title = row.get("original_title")
            requirement = row.get("expected_output")
            request_body = row.get("request_body")
            logger.info(f"Processing case. original_title: {original_title}")
            if isinstance(request_body, str) and request_body.strip():
                try:
                    request_body_dict = json.loads(request_body)
                except Exception:
                    logger.error("request_body 解析失败")
                    continue
            else:
                request_body_dict = request_body

            if not isinstance(request_body_dict, dict):
                logger.warning("request_body 不是字典，跳过该用例")
                skipped_case_list.append(id)
                continue

            logger.debug(f"request_body_dict: {request_body_dict}")
            single_case_result = await BadCaseRunner.run_single_case(request_body_dict)
            if single_case_result.code != code_complete:
                logger.error(f"run_single_case failed: {single_case_result}")

            answer = str(single_case_result.result_text or "")
            logger.debug(f"[OUTPUT]: {answer}")
            judge_result = await judgeHelper.judge_result(
                query=json.dumps(request_body_dict, ensure_ascii=False)
                if isinstance(request_body_dict, (dict, list))
                else str(request_body_dict),
                answer=answer,
                requirement=str(requirement or ""),
            )
            logger.debug(f"judge_result: {judge_result}")
            # 解析判定 JSON，拆分为 judge_result 与 judge_reason
            judge_result, judge_reason = BadCaseRunner._extract_judge_fields(
                judge_result or ""
            )
            # 收集并缓存在 writer 中
            await writer.append_row(
                row,
                regression_output=single_case_result.result_text,
                judge_result=judge_result or "",
                judge_reason=judge_reason,
            )

        # 所有用例处理结束后，统一写入新 Excel
        writer.write_to(file_path_out)

        # 记录跳过的用例，写入到txt文件
        if len(skipped_case_list) > 0:
            with open("skipped_cases.txt", "w") as f:
                f.writelines([f"{case_id}\n" for case_id in skipped_case_list])


if __name__ == "__main__":
    loop = asyncio.new_event_loop()
    badCaseRunner = BadCaseRunner()
    try:
        asyncio.set_event_loop(loop=loop)
        loop.run_until_complete(badCaseRunner.run_bad_cases())
    finally:
        loop.close()
