import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import Alignment
from openpyxl.utils import get_column_letter
from typing_extensions import Optional
from openai import OpenAI
import asyncio
import argparse
from pathlib import Path
import json


code_middle_fail = 100
code_complete = 0


class ExcelHelper:
    EXPECTED_HEADERS = [
        "id",
        "category",
        "version",
        "original_title",
        "expected_output",
        "real_output",
        "request_body",
        "original_description",
    ]

    def __init__(self, file_path, sheet_index: int = -1):
        self.file_path = file_path
        self.workbook = load_workbook(file_path)
        self.sheet = self._resolve_sheet(sheet_index)
        # 标题到列索引的映射，如 {"id": 1, ...}（openpyxl 列索引从1开始）
        self._header_to_col = {}
        self._init_headers()

    def _resolve_sheet(self, sheet_index: int):
        # 优先使用索引指定的工作表
        if 0 <= sheet_index < len(self.workbook.worksheets):
            return self.workbook.worksheets[sheet_index]
        # 其次使用 active，不行则回退到第一个工作表
        ws = self.workbook.active
        if ws is None and self.workbook.worksheets:
            ws = self.workbook.worksheets[0]
        if ws is None:
            raise RuntimeError("Workbook has no worksheets")
        return ws

    def _normalize_header(self, text) -> str:
        # 归一化标题：去两端空白、转小写、将空白和横线替换为下划线、去除 BOM
        if text is None:
            return ""
        s = str(text)
        s = s.replace("\ufeff", "").strip().lower()
        # 将连续空白替换为单个下划线
        import re

        s = re.sub(r"\s+", "_", s)
        s = s.replace("-", "_")
        return s

    def _init_headers(self):
        # 读取首行作为表头
        header_row = next(
            self.sheet.iter_rows(min_row=1, max_row=1, values_only=True), None
        )
        mapping = {}
        if header_row:
            for idx, name in enumerate(header_row, start=1):
                norm = self._normalize_header(name)
                if norm:
                    mapping[norm] = idx
        # 仅保留我们关心的字段
        self._header_to_col = {
            h: mapping[h] for h in self.EXPECTED_HEADERS if h in mapping
        }

    def get_original_headers(self):
        """返回源表第一行的原始表头（未归一化）。若为空则返回空列表。"""
        header_row = next(
            self.sheet.iter_rows(min_row=1, max_row=1, values_only=True), None
        )
        return list(header_row) if header_row else []

    def _row_to_dict(self, row_values: list) -> dict:
        # row_values 是按列的值列表，从第二行开始
        result = {}
        for key, col in self._header_to_col.items():
            if not col:
                continue
            idx = col - 1
            result[key] = row_values[idx] if 0 <= idx < len(row_values) else None
        return result

    async def read_rows(self, category: Optional[str] = None):
        """
        按行读取数据，返回字典：
        {
          "id", "category", "version", "original_title",
          "expected_output", "real_output", "request_body", "original_description"
        }
        可通过 category 过滤。
        """
        # 从第二行起读取（第一行是表头）
        for row in self.sheet.iter_rows(min_row=2, values_only=True):
            # 跳过空行
            if row is None or all(
                v is None or (isinstance(v, str) and v.strip() == "") for v in row
            ):
                continue
            item = self._row_to_dict(list(row))
            if category is not None:
                cat_val = item.get("category")
                if (cat_val is None) or (str(cat_val).strip() != str(category).strip()):
                    continue
            yield item

    async def read_each_line(self):
        """
        兼容旧接口：返回每一行的数据字典。
        如需按类目筛选，请使用 read_rows(category=...).
        """
        async for item in self.read_rows():
            yield item


class AgentStreamHelper:
    def __init__(self):
        self.total_message = ""
        self.json_data = []
        self.log_data = []
        self.conversation_id = None
        # streaming helpers
        self._line_buffer = ""
        self._raw_collector = []
        self._event_count = 0

    def _parse_message(self, data: dict):
        """Parse a message event. Accepts flexible answer formats:
        - answer as JSON string: {"type": "text", "data": "..."}
        - answer as dict with the same fields
        - answer as plain text string (append directly)
        """
        answer = data.get("answer", "")

        # If already dict
        if isinstance(answer, dict):
            answer_type = answer.get("type")
            if answer_type == "text":
                self.total_message += str(answer.get("data", ""))
            elif answer_type == "json":
                self.json_data.append(answer)
            else:
                # Unknown typed payload; try common fields
                content = answer.get("content") or answer.get("data")
                if isinstance(content, str):
                    self.total_message += content
            return

        # If string: try to parse as JSON first, fallback to plain text
        if isinstance(answer, str):
            s = answer.strip()
            if s:
                try:
                    answer_json = json.loads(s)
                    if isinstance(answer_json, dict):
                        answer_type = answer_json.get("type")
                        if answer_type == "text":
                            self.total_message += str(answer_json.get("data", ""))
                        elif answer_type == "json":
                            self.json_data.append(answer_json)
                        else:
                            content = answer_json.get("content") or answer_json.get(
                                "data"
                            )
                            if isinstance(content, str):
                                self.total_message += content
                        return
                except Exception:
                    # Not a JSON string, treat as plain text
                    self.total_message += s
                    return

        # Any other types are ignored safely

    def _parse_agent_log(self, data: dict):
        label = data.get("data", {}).get("label", "")
        content = data.get("data", {}).get("data", {})
        if isinstance(content, dict):
            content["original_label"] = label
            self.log_data.append(content)

    def _parse_workflow_started(self, data: dict):
        self.conversation_id = data.get("conversation_id")

    def parse_stream_line(self, line: str):
        try:
            json_item = json.loads(line)
            event = json_item.get("event")
            if "agent_log" == event:
                self._parse_agent_log(json_item)
            if "message" == event:
                self._parse_message(json_item)
            if "workflow_started" == event:
                self._parse_workflow_started(json_item)
            self._event_count += 1

        except BaseException as e:
            print(f"\n[error] parse_stream_line:  e: {str(e)}. \nline: {line}")

    # --------- Robust streaming helpers (SSE/JSON lines) ---------
    def feed(self, part):
        """Feed a raw chunk from the HTTP stream. Handles buffering and per-line parsing."""
        if isinstance(part, bytes):
            text = part.decode("utf-8", errors="ignore")
        else:
            text = str(part)
        if not text:
            return
        self._raw_collector.append(text)
        self._line_buffer += text
        self._drain_lines()

    def _drain_lines(self):
        while "\n" in self._line_buffer:
            line, self._line_buffer = self._line_buffer.split("\n", 1)
            self._handle_stream_line(line.strip())

    def flush(self):
        tail = self._line_buffer.strip()
        if tail:
            self._handle_stream_line(tail)
        self._line_buffer = ""

    def _handle_stream_line(self, s: str):
        if not s:
            return
        # SSE style: data: {...}
        if s.lower().startswith("data:"):
            payload = s.split("data:", 1)[1].strip()
            if payload and payload != "[DONE]":
                self.parse_stream_line(payload)
            return
        # Plain JSON line
        if s.startswith("{") or s.startswith("["):
            self.parse_stream_line(s)

    # ----------------- Introspection helpers -----------------
    def get_raw_response(self) -> str:
        return "".join(self._raw_collector)

    def get_event_count(self) -> int:
        return self._event_count

    def get_conversation_id(
        self,
    ) -> Optional[str]:
        return self.conversation_id

    def get_total_text_result(
        self,
    ):
        return self.total_message

    def get_answer(self):
        """Return JSON['answer'] when available; otherwise return the JSON or text.

        Priority:
        1) If raw response is a top-level JSON: return json['answer'] if present;
           else return the whole JSON string.
        2) Else return aggregated plain text (total_message) if present.
        3) Else parse SSE raw lines and concatenate any {"type":"message","data":"..."}.
        4) Else return the raw response text.
        """
        raw_all = "".join(self._raw_collector)
        raw_str = raw_all.strip()

        # 1) Prefer top-level JSON result
        if raw_str.startswith("{") or raw_str.startswith("["):
            try:
                obj = json.loads(raw_str)
                if isinstance(obj, dict):
                    if "answer" in obj:
                        ans = obj.get("answer")
                        if isinstance(ans, str):
                            return ans
                        try:
                            return json.dumps(ans, ensure_ascii=False)
                        except Exception:
                            return str(ans)
                    # No 'answer' field: return entire JSON
                    try:
                        return json.dumps(obj, ensure_ascii=False)
                    except Exception:
                        return raw_str
                else:
                    # JSONArray or other: return full JSON string
                    try:
                        return json.dumps(obj, ensure_ascii=False)
                    except Exception:
                        return raw_str
            except Exception:
                # Not a valid JSON, continue
                pass

        # 2) Use aggregated text if present
        if isinstance(self.total_message, str) and self.total_message.strip():
            return self.total_message

        # 3) Try reconstructing from raw SSE payloads
        try:
            if not raw_all:
                return ""
            answer_parts: list[str] = []
            for line in raw_all.splitlines():
                s = line.strip()
                if not s:
                    continue
                if s.lower().startswith("data:"):
                    payload = s.split("data:", 1)[1].strip()
                else:
                    payload = s
                if payload in ("", "[DONE]"):
                    continue
                try:
                    obj = json.loads(payload)
                except Exception:
                    # Not JSON, skip
                    continue
                # Accept both Dify-like {event: ...} and our API {type: ...}
                msg_type = obj.get("type") or obj.get("event")
                if msg_type == "message":
                    data = obj.get("data")
                    if isinstance(data, str):
                        answer_parts.append(data)
                    elif isinstance(data, dict):
                        content = (
                            data.get("content") or data.get("text") or data.get("data")
                        )
                        if isinstance(content, str):
                            answer_parts.append(content)
            if answer_parts:
                return "".join(answer_parts)
        except Exception:
            pass

        # 4) Fallback to raw text
        return raw_all

    def get_agent_log(self, filter: Optional[str] = None) -> list:
        if not filter:
            return self.log_data
        else:
            result = []
            for item in self.json_data:
                if filter == item.get("original_label"):
                    result.append(item)
            return result

    def get_json_data(self, filter: Optional[str] = None) -> list:
        if not filter:
            return self.json_data
        else:
            result = []
            for item in self.json_data:
                if filter == item.get("region"):
                    result.append(item)
            return result


class JudgeHelper:
    def __init__(self):
        self.apikey = "1s3963nw8802M4O55yMuU6x37tOYQ682"
        self.url = "http://58.22.103.26:8099/v1"
        self.modelname = "SenseAuto-Chat"
        self.client = OpenAI(api_key=self.apikey, base_url=self.url)

    async def judge_result(
        self, query: str, answer: str, requirement: str
    ) -> Optional[str]:
        prompt_base = """
        你是一个问答结果质量判断专家，根据输入的问题、生成的结果, 以及对问答的要求，综合判断生成的结果是否满足要求。
        要求：
            -你的回答要在开头就表明判断的定论，用：满足要求、基本满足要求、无法判断、不满足要求，来给出一个明确的判断
            -之后要简要的说明你的判断依据
            -按照输出的json格式输出
        输入：
            问题:{query}
            生成的结果:{answer}
            对问答的要求:{requirement}
        输出:
            {{
                "result": <判断结果>,
                "reason": <判断依据>
            }}
        """
        prompt = prompt_base.format(query=query, answer=answer, requirement=requirement)

        massage: list = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": "请作出回应"},
        ]

        response = self.client.chat.completions.create(
            model=self.modelname, messages=massage, stream=False
        )

        return response.choices[0].message.content


class ExcelResultWriter:
    """
    用于在跑完所有用例后，一次性将新增结果写入新的 Excel：
    - 将 single_case_result.result_text 写入列名为 'regression_output' 的列
    - 将 LLM 评判结果写入列名为 'judge_result' 的列

    使用方式（伪代码）：
        helper = ExcelHelper(src_file, sheet_index=0)
        writer = ExcelResultWriter(helper)
        async for item in helper.read_rows(category=None):
            # 业务侧生成
            regression_output = single_case_result.result_text
            judge_text = await JudgeHelper().judge_result(query, regression_output, requirement)
            await writer.append_row(item, regression_output=regression_output, judge_result=judge_text)
        writer.write_to(out_path)
    """

    EXTRA_COLUMNS = ["regression_output", "judge_result", "judge_reason"]

    def __init__(self, excel_helper: "ExcelHelper"):
        self.excel_helper = excel_helper
        self._buffer: list[dict] = []

    async def append_row(
        self,
        row_item: dict,
        *,
        regression_output: str = "",
        judge_result: str = "",
        judge_resutl: str = "",
        judge_reason: str = "",
    ):
        data = dict(row_item or {})
        data["regression_output"] = regression_output
        data["judge_result"] = judge_result
        data["judge_reason"] = judge_reason
        self._buffer.append(data)

    def write_to(self, out_file: str, *, keep_source_columns: bool = True):
        """
        将缓冲区中的数据写入新 Excel 文件。
        - keep_source_columns=True 时，优先按照源文件中出现过、且被 ExcelHelper 识别的列顺序输出，
          其后追加 regression_output 与 judge_result。
        """
        # 先构造列顺序
        if keep_source_columns:
            # 优先使用 ExcelHelper.EXPECTED_HEADERS 的顺序，仅保留源表实际存在的那些
            present = [
                h
                for h in ExcelHelper.EXPECTED_HEADERS
                if h in self._collect_all_keys_from_source()
            ]
            columns = present + [c for c in self.EXTRA_COLUMNS if c not in present]
        else:
            # 使用所有出现过的键
            keys = sorted(self._collect_all_keys())
            columns = [
                k for k in keys if k not in self.EXTRA_COLUMNS
            ] + self.EXTRA_COLUMNS

        df = pd.DataFrame(self._buffer)
        # 确保所有列存在
        for col in columns:
            if col not in df.columns:
                df[col] = ""
        # 重排列顺序
        df = df[columns]
        # 写入并设置样式（水平居中、自动换行、合适列宽）
        out_path = str(out_file)
        try:
            with pd.ExcelWriter(out_path, engine="openpyxl") as writer:
                sheet_name = "Sheet1"
                df.to_excel(writer, index=False, sheet_name=sheet_name)
                ws = writer.sheets[sheet_name]

                # 1) 设置所有单元格对齐：居中 + 自动换行
                align = Alignment(
                    horizontal="center", vertical="center", wrap_text=True
                )
                for row in ws.iter_rows(
                    min_row=1, max_row=ws.max_row, min_col=1, max_col=ws.max_column
                ):
                    for cell in row:
                        cell.alignment = align

                # 2) 自适应列宽（根据表头与内容长度估算，限制最小/最大宽度）
                min_width, max_width = 10, 80
                for col_idx, col_name in enumerate(df.columns, start=1):
                    col_letter = get_column_letter(col_idx)

                    # 计算字符串显示长度（粗略按字符数估算，非 ASCII 适当放大）
                    def visual_len(s: str) -> int:
                        total = 0
                        for ch in s:
                            total += 2 if ord(ch) > 127 else 1
                        return total

                    max_len = visual_len(str(col_name))
                    for r in range(2, ws.max_row + 1):
                        v = ws.cell(row=r, column=col_idx).value
                        if v is None:
                            continue
                        s = str(v)
                        # 对换行内容，按最长一行计算
                        parts = s.splitlines() or [s]
                        for part in parts:
                            max_len = max(max_len, visual_len(part))

                    # 适当留白
                    width = max_len + 2
                    if width < min_width:
                        width = min_width
                    if width > max_width:
                        width = max_width

                    ws.column_dimensions[col_letter].width = width

                # 写入完成由上下文自动保存
        except Exception as e:
            raise RuntimeError(f"写入 Excel 失败: {e}")

    def _collect_all_keys(self) -> set:
        keys = set()
        for row in self._buffer:
            keys.update(row.keys())
        return keys

    def _collect_all_keys_from_source(self) -> set:
        # 根据 ExcelHelper 的 header 映射来推测源文件包含的列（使用规范化后的键名）
        return set(self.excel_helper._header_to_col.keys())


# -----------------------
# Local quick test runner
# -----------------------
async def _test_excel_helper(
    file_path: str,
    sheet: int,
    category: Optional[str],
    limit: int,
    show_categories: bool,
):
    p = Path(file_path)
    if not p.exists():
        print(f"[error] File not found: {file_path}")
        return

    helper = ExcelHelper(file_path, sheet_index=sheet)

    # 打印已识别的表头映射
    print("[info] detected headers -> columns (1-based):")
    print(helper._header_to_col)

    # 列出全部类别（可选）
    if show_categories:
        cats = set()
        async for item in helper.read_rows():
            val = item.get("category")
            if val is not None and str(val).strip() != "":
                cats.add(str(val).strip())
        print(f"[info] unique categories ({len(cats)}): {sorted(cats)}")

    # 展示前 N 条（可选过滤）
    print("[info] sample rows:")
    count = 0
    async for item in helper.read_rows(category=category):
        print(item)
        count += 1
        if count >= max(1, limit):
            break
    if count == 0:
        if category is None:
            print("[warn] no data rows parsed.")
        else:
            print(f"[warn] no rows matched category='{category}'.")


def _build_arg_parser():
    parser = argparse.ArgumentParser(description="Quick local test for ExcelHelper")
    parser.add_argument("--file", required=True, help="Path to the Excel file (.xlsx)")
    parser.add_argument(
        "--sheet", type=int, default=-1, help="Sheet index (default: -1 for active)"
    )
    parser.add_argument("--category", default=None, help="Optional category filter")
    parser.add_argument(
        "--limit", type=int, default=5, help="Number of rows to preview (default: 5)"
    )
    parser.add_argument(
        "--show-categories",
        action="store_true",
        help="List unique categories before sampling",
    )
    return parser


if __name__ == "__main__":
    args = _build_arg_parser().parse_args()
    asyncio.run(
        _test_excel_helper(
            file_path=args.file,
            sheet=args.sheet,
            category=args.category,
            limit=args.limit,
            show_categories=args.show_categories,
        )
    )
