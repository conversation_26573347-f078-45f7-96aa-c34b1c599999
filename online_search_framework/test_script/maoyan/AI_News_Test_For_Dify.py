import requests
import time
import json
import os
import pandas as pd
# ===== 配置区域 =====

DIFY_TTFB_THRESHOLD = 3  # Dify-TTFB耗时阈值
T1_LOC_THRESHOLD = 0.1     # t1-loc耗时阈值
T2_MEM_THRESHOLD = 0.1     # t2-mem耗时阈值
T3_PRE_INTENT_THRESHOLD = 1  # t3-pre_intent耗时阈值
T3_INTENT_THRESHOLD = 1  # t3-intent耗时阈值
T4_SRC_THRESHOLD = 2     # t4-src耗时阈值
T5_FETCH_THRESHOLD = 1     # t5-fetch耗时阈值
T6_MFT_THRESHOLD = 0.5     # t6-mft耗时阈值
T7_SFT_THRESHOLD = 0.5     # t7-sft耗时阈值
T8_ACFT_THRESHOLD = 3    # t8-acft耗时阈值

url = "https://agent-sit.senseauto.com/v1/chat-messages"
headers = {
    "Authorization": "Bearer app-0lG1fLjvC3qgQTZGEg3sl9gi",
    "Content-Type": "application/json"
}
# url = "https://api.dify.ai/v1/chat-messages"
# headers = {
#     "Authorization": "Bearer app-WwHiSeDsKb8ed1KDs5yv1FUf",
#     "Content-Type": "application/json"
# }

test_time = time.strftime("%Y%m%d_%H%M%S", time.localtime())

queries = [
    "今日国内热点新闻",
    "我这边昨天有什么热点新闻吗",
    "最近一月的社会热点新闻",
    "最近吉林有哪些社会热点新闻",
    "今天抖音热搜前十是哪些",
    "今天娱乐圈最火的八卦是什么？",
    "科技圈最近24小时有什么重磅新闻？",
    "听说德铁的员工又罢工了，罢工的原因是什么？",
    "2024年国际烟花汇演在哪举行",
    "以色列为啥要打巴勒斯坦，最新情报是什么",
    "最近中国男足在世预赛中的表现如何？",
    "最终排名是多少？",
    # "今天有什么股市新闻",
    # "今天有什么数码新闻",
    # "今天的百度热榜新闻前十有哪些",
    # "今日热点新闻简报",
    # "最近一周的社会热点新闻",
    # "今天澎湃新闻的热榜是哪些",
    # "最近江苏有哪些社会热点新闻",
    # "今天的微博热搜前十都是啥",
    # "当前全网讨论度最高的事件有哪些？",
    # "最近中国男足在世预赛中的表现如何？最终排名是多少？",
    # "详细介绍一下第二条新闻"
]

# desktop_path = os.path.expanduser("~/Desktop")
# output_file = os.path.join(desktop_path, "AI新闻_Dify_20250713_tencent.txt")

desktop_path = os.path.join(os.path.expanduser("~"),"桌面")
output_file = os.path.join("C:\\zhongyuanfeng_vendor\\桌面", "AI新闻_Dify_20250713_tencent.txt")
print(desktop_path)

# ===== 开始测试 =====

results = []

excelData = {
    'query': [],
    'Dify-TTFB': [],
    't1-loc': [],
    't2-mem': [],
    't3-pre_intent': [],
    't3-intent': [],
    't4-src': [],
    't5-fetch': [],
    't6-mft': [],
    't7-sft': [],
    't8-acft': [],
    'Answer': [],
    'Remark': []
}



def output_excel():


    df = pd.DataFrame(excelData)
    # 使用ExcelWriter和xlsxwriter引擎
    file_path = 'AI新闻_' + test_time + '.xlsx'
    writer = pd.ExcelWriter(file_path, engine='xlsxwriter')
    df.to_excel(writer, index=False, sheet_name='AI新闻（tencent）')
    # 获取工作表和workbook对象
    workbook = writer.book
    worksheet = writer.sheets['AI新闻（tencent）']
    # 定义格式
    header_format = workbook.add_format({
        'bold': True,
        'text_wrap': True,
        'valign': 'center',
        'fg_color': '#D7E4BC',
        'border': 1})
    data_format = workbook.add_format({'align': 'center', "valign": "vcenter"})
    # 设置列宽
    worksheet.set_column(0, len(excelData) - 2, 20, data_format)
    worksheet.set_column(len(excelData) - 1, len(excelData) - 1, 60, data_format)
    for row in range(1, len(excelData) - 1):  # 假设设置前 100 行
        worksheet.set_row(row, 30)
    # 应用标题格式（第一行是标题行）
    worksheet.write_row('A1', df.columns, header_format)

    yellow_format = workbook.add_format({'bg_color': '#FFFF00'})  # 黄色背景
    
    # 需要检查的列及其对应的Excel列字母
    check_columns = {
        'B': 'Dify-TTFB',  # B列对应Dify-TTFB
        'C': 't1-loc',     # C列对应t1-loc
        'D': 't2-mem',     # D列对应t2-mem
        'E': 't3-pre_intent',  # E列对应t3-pre_intent
        'F': 't3-intent',  # F列对应t3-intent
        'G': 't4-src',     # G列对应t4-src
        'H': 't5-fetch',   # H列对应t5-fetch
        'I': 't6-mft',     # I列对应t6-mft
        'J': 't7-sft',     # J列对应t7-sft
        'K': 't8-acft'     # K列对应t8-acft
    }
    
    # 应用条件格式到每一列
    for col_letter, col_name in check_columns.items():
        # 获取数据行范围（从第2行开始，到最后一行）
        start_row = 1  # Excel行号从0开始，第2行对应索引1
        end_row = len(excelData[col_name]) - 1  # 最后一行
        
        # 为当前列设置条件格式
        if col_letter == 'B':  # Dify-TTFB列特殊处理
            # 规则1: 值为N/A
            worksheet.conditional_format(
                f'{col_letter}{start_row + 1}:{col_letter}{end_row + 1}',
                {
                    'type': 'formula',
                    'criteria': f'={col_letter}2="N/A"',
                    'format': yellow_format
                }
            )
            # 规则2: 数值大于等于阈值
            worksheet.conditional_format(
                f'{col_letter}{start_row + 1}:{col_letter}{end_row + 1}',
                {
                    'type': 'formula',
                    'criteria': f'=AND(ISNUMBER(VALUE({col_letter}2)), VALUE({col_letter}2)>={DIFY_TTFB_THRESHOLD})',
                    'format': yellow_format
                }
            )
        else:
            # 获取对应列的阈值
            threshold_var = globals()[f"{col_name.replace('-', '_').upper()}_THRESHOLD"]
            worksheet.conditional_format(
                f'{col_letter}{start_row + 1}:{col_letter}{end_row + 1}',
                {
                    'type': 'formula',
                    'criteria': f'=AND(ISNUMBER({col_letter}2), {col_letter}2>={threshold_var})',
                    'format': yellow_format
                }
            )

    writer.close()

def fill_excel_data():
    t1_loc = float(sense_time_info.get("t1-loc", "0")) if type(sense_time_info) is dict else 0
    t2_mem = float(sense_time_info.get("t2-mem", "0")) if type(sense_time_info) is dict else 0
    t3_intent = float(sense_time_info.get("t3-intent", "0")) if type(sense_time_info) is dict else 0
    t3_pre_intent = float(sense_time_info.get("t3-pre_intent", "0")) if type(sense_time_info) is dict else 0
    t4_src = float(sense_time_info.get("t4-src", "0")) if type(sense_time_info) is dict else 0
    t5_fetch = float(sense_time_info.get("t5-fetch", "0")) if type(sense_time_info) is dict else 0
    t6_mft = float(sense_time_info.get("t6-mft", "0")) if type(sense_time_info) is dict else 0
    t7_sft = float(sense_time_info.get("t7-sft", "0")) if type(sense_time_info) is dict else 0
    t8_acft = float(sense_time_info.get("t8-acft", "0")) if type(sense_time_info) is dict else 0
    
    # 计算Dify-TTFB的数值
    ttfb_value = float(ttfb_display) if ttfb_display != "N/A" else 0
    
    # 计算各个阶段的耗时
    t2_mem_display = t2_mem - t1_loc
    t3_pre_intent_display = t3_pre_intent - t2_mem
    t3_intent_display = t3_intent - t3_pre_intent
    t4_src_display = t4_src - t3_intent
    t5_fetch_display = t5_fetch - t4_src
    t6_mft_display = t6_mft - t5_fetch
    t7_sft_display = t7_sft - t6_mft
    
    # 检查哪些列的值大于等于阈值或为N/A
    failed_columns = []
    if ttfb_display == "N/A":
        failed_columns.append(f"Dify-TTFB为N/A")
    elif ttfb_value >= DIFY_TTFB_THRESHOLD:
        failed_columns.append(f"Dify-TTFB超过{DIFY_TTFB_THRESHOLD}")
    if t1_loc >= T1_LOC_THRESHOLD:
        failed_columns.append(f"t1-loc超过{T1_LOC_THRESHOLD}")
    if t2_mem_display >= T2_MEM_THRESHOLD:
        failed_columns.append(f"t2-mem超过{T2_MEM_THRESHOLD}")
    if t3_pre_intent_display >= T3_PRE_INTENT_THRESHOLD:
        failed_columns.append(f"t3-pre_intent超过{T3_PRE_INTENT_THRESHOLD}")
    if t3_intent_display >= T3_INTENT_THRESHOLD:
        failed_columns.append(f"t3-intent超过{T3_INTENT_THRESHOLD}")
    if t4_src_display >= T4_SRC_THRESHOLD:
        failed_columns.append(f"t4-src超过{T4_SRC_THRESHOLD}")
    if t5_fetch_display >= T5_FETCH_THRESHOLD:
        failed_columns.append(f"t5-fetch超过{T5_FETCH_THRESHOLD}")
    if t6_mft_display >= T6_MFT_THRESHOLD:
        failed_columns.append(f"t6-mft超过{T6_MFT_THRESHOLD}")
    if t7_sft_display >= T7_SFT_THRESHOLD:
        failed_columns.append(f"t7-sft超过{T7_SFT_THRESHOLD}")
    if t8_acft >= T8_ACFT_THRESHOLD:
        failed_columns.append(f"t8-acft超过{T8_ACFT_THRESHOLD}")

    # 统计新闻数量 - 直接统计"news_url"出现的次数
    news_count = final_answer.count('"news_url"') if final_answer and final_answer != "无有效响应" else 0
    
    # 生成备注信息
    remark_parts = []
    if failed_columns:
        remark_parts.append("; ".join(failed_columns))
    if news_count > 0:
        remark_parts.append(f"新闻数量={news_count}")
    elif news_count == 0 and final_answer and final_answer != "无有效响应":
        remark_parts.append("新闻数量=0")
    
    remark = "; ".join(remark_parts) if remark_parts else ""

    if t5_fetch != 0:
        excelData['query'].append(query_text)
        excelData['Dify-TTFB'].append(ttfb_display)
        excelData['t1-loc'].append(t1_loc)
        excelData['t2-mem'].append(t2_mem - t1_loc)
        excelData['t3-pre_intent'].append(t3_pre_intent - t2_mem)
        excelData['t3-intent'].append(t3_intent - t3_pre_intent)
        excelData['t4-src'].append(t4_src - t3_intent)
        excelData['t5-fetch'].append(t5_fetch - t4_src)
        excelData['t6-mft'].append(t6_mft - t5_fetch)
        excelData['t7-sft'].append(round(t7_sft - t6_mft, 6))
        excelData['t8-acft'].append(t8_acft)
        excelData['Answer'].append(final_answer)
        excelData['Remark'].append(remark)
    else:
        excelData['query'].append(query_text)
        excelData['Dify-TTFB'].append(ttfb_display)
        excelData['t1-loc'].append(t1_loc)
        excelData['t2-mem'].append(t2_mem - t1_loc)
        excelData['t3-pre_intent'].append(t3_pre_intent - t2_mem)
        excelData['t3-intent'].append(0)
        excelData['t4-src'].append(0)
        excelData['t5-fetch'].append(0)
        excelData['t6-mft'].append(t6_mft - t3_pre_intent)
        excelData['t7-sft'].append(0)
        excelData['t8-acft'].append(t8_acft)
        excelData['Answer'].append(final_answer)
        excelData['Remark'].append(remark)

def add_params_des():

        excelData['query'].append(" ")
        excelData['Dify-TTFB'].append("Dify智能体接口首字延迟")
        excelData['t1-loc'].append("高德经纬度转换成城市")
        excelData['t2-mem'].append("获取用户画像")
        excelData['t3-pre_intent'].append("前置落域")
        excelData['t3-intent'].append("意图识别")
        excelData['t4-src'].append("搜索引擎返回结果")
        excelData['t5-fetch'].append("爬虫结束")
        excelData['t6-mft'].append("大模型总结首字延迟")
        excelData['t7-sft'].append("安全检测首字延迟")
        excelData['t8-acft'].append("原子能力接口首字延迟")
        excelData['Answer'].append(" ")
        excelData['Remark'].append("")

conversation_id_to_pass = None

for idx, query_text in enumerate(queries, 1):
    print(f"🚀 正在测试 Query {idx}/{len(queries)}：{query_text}")

    payload = {
        "query": query_text,
        "response_mode": "streaming",
        "inputs": {
            "car_id": "fake_car_id",
            "user_id": "fake_user_id",
            "lat": "31.16813",
            "lon": "121.39987",
            "detect": "false",
            "search_engine": "tencent",
            # "signal_source": "byte"
        },
        "user": "abc-124"
    }

    if idx == len(queries) and conversation_id_to_pass:
        payload["conversation_id"] = conversation_id_to_pass
        print(f"💡 正在为最后一条 query 传入 conversation_id: {conversation_id_to_pass}")

    start_time = time.time()
    print(f"✅ start_time ：{start_time}")
    try:
        response = requests.post(url, headers=headers, json=payload, stream=True, timeout=15)
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        continue

    if response.status_code != 200:
        print(f"❌ 请求失败，状态码: {response.status_code}")
        continue

    # 初始化状态
    first_message_time = None
    final_answer = ""
    sense_time_info = None

    try:
        for raw_chunk in response.iter_lines(decode_unicode=True):
            if not raw_chunk.strip():
                continue

            if raw_chunk.startswith("data: "):
                json_str = raw_chunk[len("data: "):]
                try:
                    data = json.loads(json_str)
                except json.JSONDecodeError:
                    continue

                if idx == len(queries) - 1: # 仅在倒数第二个用例时执行
                    if data.get("event") == "workflow_started" and "conversation_id" in data:
                        conversation_id_to_pass = data.get("conversation_id")
                        print(f"⭐ 成功捕获 conversation_id: {conversation_id_to_pass}")

                event_type = data.get("event")

                if event_type == "message":
                    if first_message_time is None:
                        first_message_time = time.time()
                        print(f"✅ first_message_time ：{first_message_time}")
                        ttfb = round((first_message_time - start_time), 4)

                    if "answer" in data:
                        try:
                            answer_obj = json.loads(data["answer"])
                            text = answer_obj.get("data", "")
                            final_answer += text
                        except json.JSONDecodeError:
                            continue

                elif event_type == "agent_log":
                    log_data = data.get("data")
                    if isinstance(log_data, dict) and log_data.get("label") == "Sense_Time_Info":
                        sense_time_info = log_data.get("data")

    except Exception as e:
        print(f"⚠️ 流读取异常: {e}")
        continue

    # 输出并保存结果
    ttfb_display = str(round((first_message_time - start_time),4)) if first_message_time else "N/A"
    final_answer = final_answer.strip() or "无有效响应"
    sense_time_str = json.dumps(sense_time_info, ensure_ascii=False) if sense_time_info else "无"

    result_text = f"\n\nQuery: {query_text}\nTTFB(ms): {ttfb_display}\nSense_Time_Info: {sense_time_str}\nFinal Answer: {final_answer}"
    results.append(result_text)
    fill_excel_data()

add_params_des()
output_excel()
print("✅ 写入完成\n")

# ===== 写入文件 =====

# try:
#     with open(output_file, "w", encoding="utf-8") as f:
#         f.write("\n".join(results))
#     print(f"📄 测试结果已保存到：{output_file}")
# except Exception as e:
#     print(f"❌ 写文件失败: {e}")
