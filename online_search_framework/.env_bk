OTEL_EXPORTER_OTLP_TRACES_ENDPOINT=http://gm-cloud.sensetime.com:8318/v1/traces
# will be overwriten by the value set in docker-compose.yaml
SELECTED_MODEL=senseauto
;
; POSTGRES_HOST=***********
; POSTGRES_USER=postgres
; POSTGRES_PASSWORD=pwQ7OtlzMRncl5Qx
; POSTGRES_DATABASE=kami_search
; POSTGRES_PORT=8432

; REDIS_HOST=***********
; REDIS_PORT=8379
; REDIS_PASSWORD=3213fc11c21e2583

; MEILISEARCH_URL=http://***********:8700
; MEILISEARCH_KEY=fb9bd200066cbe859acd819b8e5dd172
SCRAPE_URL=http://***********:8780/parse

# will be overwriten by the value set in docker-compose.yaml
# RERANK_URL=http://***************:8101/rerank
RERANK_URL=https://acplatform.sensetime.com/cabin/wsrch/rerank

EMBEDDING_URL=http://*************:8080/embed

SENSEAUTO_URL=http://*************:8000/v1
SENSEAUTO_KEY=ab9edab4-8780-11ef-a760-cbde10bdf191
# empty means backend will call v1/model api to get model_name
# 注意设为空时，等号后面不要有任何东西!!
SENSEAUTO_MODEL_NAME=

SENSEAUTO_DEV_URL=http://*************:8000/v1
SENSEAUTO_DEV_KEY=ab9edab4-8780-11ef-a760-cbde10bdf191
SENSEAUTO_DEV_MODEL_NAME=

# SENSEAUTO_DEV_URL=http://***********:18300/v1
# SENSEAUTO_DEV_KEY=sk-a4tpw5pMhKKSVpgiC11022A03b6d402fB5A04e124c347272
## empty means backend will call v1/model api to get model_name
# SENSEAUTO_DEV_MODEL_NAME="senseauto-chat-0.2.0" 
#"http://*************:8000/v1"
