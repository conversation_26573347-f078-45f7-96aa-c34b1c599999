apiVersion: v1
kind: Service
metadata:
  name: {{ include "license.fullname" . }}
  annotations:
    prometheus.io/scrape: 'true'
    prometheus.io/path: '/actuator/prometheus'
    prometheus.io/port: '8090'
  labels:
    {{- include "license.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.port }}
      protocol: TCP
      name: http
#    - port: {{ .Values.podProbe.liveness.port }}
#      targetPort: {{ .Values.podProbe.liveness.port }}
#      protocol: TCP
#      name: metrics
  selector:
    {{- include "license.selectorLabels" . | nindent 4 }}
