FROM python:3.11
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
RUN pip config set global.index-url https://repo.sensetime.com/repository/pypi/simple
COPY . /app
WORKDIR /app
RUN pip install --no-cache-dir -r requirements.txt
RUN rm requirements.txt

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080", "--workers", "2"]
