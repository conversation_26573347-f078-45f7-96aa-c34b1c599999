# -*- coding: utf-8 -*-
"""
集中管理 category 与 prompt/schema 的映射，便于扩展。
用法：
- 在 CATEGORY_PROMPT_MAP 中新增你的分类键，例如 "AI新闻"。
- 为每个分类配置 prompt 生成器（或静态字符串）与 schema。
"""

import json
from typing import Dict, Any

# 通用 schema（各分类一致，如需个性化可在映射中覆盖）
COMMON_SCHEMA: Dict[str, Any] = {
    "real_output": "string",
    "expected_output": ["string"],
    "request_body": "<request_body>",
}

# 通用模板，差异点通过 extra 指令注入
BASE_PROMPT_TEMPLATE = (
    "请从下面的标题与描述文本中抽取结构化信息，输出一个 JSON 对象。\n"
    "要求：\n"
    "1) 严格输出 JSON；不允许出现注释或多余文本。\n"
    "2) 字段与示例 schema 对齐。\n"
    "3) 未识别的字段用 null（标量）或 []（数组）填充。\n\n"
    "示例 schema（字段与类型约束，仅作参考）：\n{schema}\n\n"
    "标题：\n{title}\n\n"
    "描述文本：\n{description}\n\n"
    "你需要注意：\n"
    "- 这是一段描述问题的文本，所以expected_output字段不能和real_output字段的内容相同，并且expected_output应该更倾向于描述问题与期望结果\n"
    "- queries字段的值应该是一个列表，每个列表元素包含字段'query'与'image_url'，image_url可能包含在描述中，需要你提取出来对应的url与query，并填充到列表queries中。\
        在描述中如果出现多轮对话的情况，则应该存在多个query，例如首轮query等，请你按照提问的逻辑顺序提取并填充到列表中。如果无法提取出query，请根据描述内容推断出一个合理的query值。\
        如果image_url不为空且不是一个http开头的路径，则在图片路径前拼接http://savvcenter.sensetime.com/resource\n"
    "{extra}"
    "- 描述文本中可能已经包含了request_body的信息，但是请你还是按照上述说明进行结构化提取。\n"
)

GEO_EXTRA = "- request_body字段的值应该是一个对象，必须包含字段'query'、以及与坐标经纬度相关的包含关键词'lat' 和 'lon'的字段,以及user_id字段等。如果没有显式包含坐标经纬度相关的字段，则不要在request_body中包含相关字段。\n"

DESKTOP_EXTRA = "- request_body字段的值应该是一个对象，必须包含字段queries。如果描述文本中提到了图片的宽高要求，请你在request_body中添加height与width字段，并根据描述内容合理推断其值。如果没有显式包含这些信息，则不要在request_body中包含相关字段。\n"


def build_prompt(
    title: str, description: str, schema: Dict[str, Any], extra: str = ""
) -> str:
    return BASE_PROMPT_TEMPLATE.format(
        schema=json.dumps(schema, ensure_ascii=False, indent=2),
        title=title,
        description=description,
        extra=extra,
    )


# 为保持兼容性，导出与原有名称一致的 schema 与构建函数。
NEWS_SCHEMA = COMMON_SCHEMA
TRAVELER_SCHEMA = COMMON_SCHEMA
LIFE_SERVICE_SCHEMA = COMMON_SCHEMA
MATCH_VIEWER_SCHEMA = COMMON_SCHEMA
DESKTOP_SCHEMA = COMMON_SCHEMA


def build_news_prompt(title: str, description: str, schema: Dict[str, Any]) -> str:
    return build_prompt(title, description, schema, extra=GEO_EXTRA)


def build_traveler_prompt(title: str, description: str, schema: Dict[str, Any]) -> str:
    return build_prompt(title, description, schema, extra=GEO_EXTRA)


def build_life_service_prompt(
    title: str, description: str, schema: Dict[str, Any]
) -> str:
    return build_prompt(title, description, schema, extra=GEO_EXTRA)


def build_match_viewer_prompt(
    title: str, description: str, schema: Dict[str, Any]
) -> str:
    return build_prompt(title, description, schema, extra=GEO_EXTRA)


def build_desktop_prompt(title: str, description: str, schema: Dict[str, Any]) -> str:
    return build_prompt(title, description, schema, extra=DESKTOP_EXTRA)


# 分类到 prompt/schema 的映射。
# prompt 可以是字符串，也可以是一个可调用（如 build_news_prompt)。
CATEGORY_PROMPT_MAP: Dict[str, Dict[str, Any]] = {
    "AI新闻": {
        "prompt": build_news_prompt,  # 使用生成函数
        "schema": NEWS_SCHEMA,
    },
    "AI旅行家": {
        "prompt": build_traveler_prompt,
        "schema": TRAVELER_SCHEMA,
    },
    "生活服务": {
        "prompt": build_life_service_prompt,
        "schema": LIFE_SERVICE_SCHEMA,
    },
    "观赛达人": {
        "prompt": build_match_viewer_prompt,
        "schema": MATCH_VIEWER_SCHEMA,
    },
    "交互桌面": {
        "prompt": build_desktop_prompt,
        "schema": DESKTOP_SCHEMA,
    },
}
