from bad_case_base_function import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    code_middle_fail,
    code_complete,
    ExcelResultWriter,
)
from typing_extensions import Optional
from typing import Any, Union, Dict
from dataclasses import dataclass
import os
import aiohttp
import time
import asyncio
import json
import logging
import colorlog
import requests
import base64


# 本脚本专用日志配置（不依赖项目其他部分）
def get_logger(level=logging.INFO):
    # 创建logger对象
    logger = logging.getLogger()
    logger.setLevel(level)
    # 创建控制台日志处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    # 定义颜色输出格式
    color_formatter = colorlog.ColoredFormatter(
        "%(log_color)s%(levelname)s: %(message)s",
        log_colors={
            "DEBUG": "cyan",
            "INFO": "green",
            "WARNING": "yellow",
            "ERROR": "red",
            "CRITICAL": "red,bg_white",
        },
    )
    # 将颜色输出格式添加到控制台日志处理器
    console_handler.setFormatter(color_formatter)
    # 移除默认的handler
    for handler in logger.handlers:
        logger.removeHandler(handler)
    # 将控制台日志处理器添加到logger对象
    logger.addHandler(console_handler)
    return logger


logger = get_logger(logging.DEBUG)

file_path_in = os.path.dirname(os.path.abspath(__file__)) + "/" + "GACCLM-缺陷.xlsx"

# 使用可读的时间格式作为文件名后缀，例如 2025-09-04_16-30-00
human_read_time = time.strftime("%Y-%m-%d_%H-%M-%S", time.localtime())
file_path_out = (
    os.path.dirname(os.path.abspath(__file__))
    + "/"
    + f"Bad_Case_Result_Ai_Desktop_{human_read_time}.xlsx"
)

excelHelper = ExcelHelper(file_path=file_path_in)
judgeHelper = JudgeHelper()

def convert_url_to_base64(url: str) -> Optional[str]:
    header = {
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "accept-encoding":"gzip, deflate",
        "upgrade-insecure-requests":"1",
        "Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"
    }
    try:
        with requests.get(url, headers=header, stream=True) as response:
            try:
                if 200 == response.status_code:
                    image_data = b''
                    for chunk in response.iter_content(chunk_size=1024):  # 每次下载 8KB
                        image_data += chunk

                    return "data:image/webp; base64," + base64.b64encode(image_data).decode("utf-8")
                else:
                    return None
            except BaseException as e:
                print(f"\n[error]: convert_url_to_base64(inner): url is {url}, e is {str(e)}")
                return None    
    except BaseException as e:
        print(f"\n[error]: convert_url_to_base64: url is {url}, e is {str(e)}")
        return None

@dataclass
class Result:
    code: int = code_middle_fail
    query: str = ""
    result_text: str = ""
    all_answers: list = None

class BadCaseRunner:
    @staticmethod
    def extract_phase_fields(answer: Union[str, Dict[str, Any], list]) -> Dict[str, Optional[Union[str, int]]]:
        """从 answer 中提取关键 phase 字段。

        输入：
        - answer: 可以是 JSON 字符串、字典，或直接为 results 数组

        输出：
        - dict，包含以下键：
          - phase_intent: str | None
          - phase_generation_url: str | None
          - width: int | None
          - height: int | None
          - phase_color: str | None

        说明：
        - 函数尽量健壮，解析失败时返回 None 值，不抛出异常。
        """
        extracted: Dict[str, Optional[Union[str, int]]] = {
            "phase_intent": None,
            "phase_generation_url": None,
            "width": None,
            "height": None,
            "phase_color": None,
        }

        try:
            if isinstance(answer, (dict, list)):
                obj = answer
            elif isinstance(answer, str):
                obj = json.loads(answer)
            else:
                logger.debug("extract_phase_fields: unsupported answer type")
                return extracted
        except Exception as e:
            logger.debug(f"extract_phase_fields: JSON parse failed: {e}")
            return extracted

        # 兼容顶层是 dict 且包含 results，或顶层直接就是一个 results 数组
        results = None
        if isinstance(obj, dict):
            results = obj.get("results")
        elif isinstance(obj, list):
            results = obj

        if not isinstance(results, list):
            logger.debug("extract_phase_fields: results missing or not a list")
            return extracted

        for item in results:
            if not isinstance(item, dict):
                continue
            phase_type = item.get("type")

            if phase_type == "phase_intent" and extracted["phase_intent"] is None:
                data = item.get("data")
                if isinstance(data, str):
                    extracted["phase_intent"] = data

            elif (
                phase_type == "phase_generation"
                and extracted["phase_generation_url"] is None
            ):
                data = item.get("data")
                if isinstance(data, str):
                    extracted["phase_generation_url"] = data
                extra = item.get("extraInfo") or item.get("extra_info") or {}
                if isinstance(extra, dict):
                    w = extra.get("width")
                    h = extra.get("height")
                    try:
                        if w is not None:
                            extracted["width"] = int(w)
                    except Exception:
                        pass
                    try:
                        if h is not None:
                            extracted["height"] = int(h)
                    except Exception:
                        pass

            elif phase_type == "phase_color" and extracted["phase_color"] is None:
                data = item.get("data")
                if isinstance(data, str):
                    extracted["phase_color"] = data

        return extracted
    @staticmethod
    def _extract_judge_fields(judge_text) -> tuple[str, str]:
        """从 LLM 判定文本中提取 result 与 reason。

        期望格式：
            {
                "result": <判断结果>,
                "reason": <判断依据>
            }

        兼容以下情况：
        - 前后有说明性文本或 Markdown 代码块
        - JSON 内部换行、空格
        - 直接返回 dict 或 message 结构
        - 返回为空或非 JSON 时，返回空字符串
        """
        if judge_text is None:
            return "", ""

        # A) 直接是 dict 的情况
        if isinstance(judge_text, dict):
            # 直接包含 result/reason 键
            if ("result" in judge_text) or ("reason" in judge_text):
                return (
                    "" if judge_text.get("result") is None else str(judge_text.get("result")),
                    "" if judge_text.get("reason") is None else str(judge_text.get("reason")),
                )
            # 兼容 message 结构：{"content":[{"type":"text","text":"...json..."}]}
            content = judge_text.get("content")
            if isinstance(content, list):
                parts = []
                for c in content:
                    if isinstance(c, dict) and c.get("type") == "text":
                        parts.append(str(c.get("text", "")))
                s = "\n".join(parts).strip()
            else:
                s = str(judge_text)
        else:
            # 字符串或其他可转字符串
            s = str(judge_text).strip()

        if not s:
            return "", ""

        # B) 去除常见的 Markdown 代码块包裹
        if s.startswith("```"):
            try:
                first_nl = s.find("\n")
                last_fence = s.rfind("```")
                if first_nl != -1 and last_fence > first_nl:
                    s = s[first_nl + 1 : last_fence].strip()
            except Exception:
                pass

        # C) 解析 JSON
        def try_parse_obj(text: str):
            try:
                obj = json.loads(text)
                if isinstance(obj, dict):
                    return obj
            except Exception:
                return None

        obj = try_parse_obj(s)
        if obj is None:
            # 尝试在文本里寻找第一个完整的大括号 JSON
            start = s.find("{")
            end = s.rfind("}")
            if start != -1 and end != -1 and end > start:
                candidate = s[start : end + 1]
                obj = try_parse_obj(candidate)

        if not isinstance(obj, dict):
            return "", ""

        result = obj.get("result")
        reason = obj.get("reason")
        return (
            "" if result is None else str(result),
            "" if reason is None else str(reason),
        )

    @staticmethod
    def build_payload(
        request_body: dict,
        round_index: int,
    ) -> dict:
        """封装单轮请求的 payload 构造逻辑。

        入参:
        - request_body: 原始请求体
        - round_index: 轮次索引，从 0 开始
        """

        logger.debug(f"request_body: {request_body}")

        payload = dict()

        queries = request_body.get("queries", [])
        if len(queries) == 0:
            raise ValueError("build_payload: payload.queries is empty")

        query = queries[round_index].get("query")
        image_url = queries[round_index].get("image_url")
        if not query or not isinstance(query, str) or not query.strip():
            raise ValueError("build_payload: payload.queries.query is empty or invalid")
        if not image_url or not isinstance(image_url, str) or not image_url.strip():
            raise ValueError("build_payload: payload.queries.image_url is empty or invalid")

        image_base64 = convert_url_to_base64(image_url)
        if not image_base64:
            raise ValueError(f"build_payload: convert_url_to_base64 failed for url: {image_url}")

        payload["query"] = query.strip()
        payload["remove_water_mark"] = False
        payload["user_info"] = {
            "car_id": "regression_car",
            "user_id": str(
                request_body.get("user_id", "regression_user") or "regression_user"
            ),
            "category": ["wallpaper_style_preference"],
        }

        if "output_size" in request_body:
            payload["output_size"] = request_body["output_size"]

        logger.debug(f"build_payload: round {round_index}, image_url: {image_url}, query: {query}")
        payload["image_base64"] = image_base64

        return payload

    @staticmethod
    async def run_single_case(request_body: dict) -> Result:
        """
        request_body的格式如下，lat与lon为可选参数，query为列表，如果是多轮对话，将存在多个元素
        request_body = {
            "querier": [
                "query": "",
                "image_url": "",
            ],
        }
        """

        result = Result()
        # 收集多轮答案（仅在此函数内部使用）
        all_answers: list[str] = []

        try:
            queries = request_body.get("queries")

            logger.debug(f"run_single_case: original queries: {queries}")

            # 兼容字符串或列表输入；最终转换为非空的字符串列表
            if isinstance(queries, list):
                rounds = [q.get("query") for q in queries if isinstance(q, dict) and q.get("query")]
            elif isinstance(queries, str):
                rounds = [queries] if queries.strip() else []
            else:
                rounds = []

            if not rounds:
                logger.error("run_single_case: request_body.query is empty or invalid")
                return result

            logger.debug(f"run_single_case: processed rounds: {rounds}")

            async with aiohttp.ClientSession() as session:
                for i, q in enumerate(rounds):
                    payload = BadCaseRunner.build_payload(
                        request_body=request_body,
                        round_index=i,
                    )
                    streamHelper = AgentStreamHelper()
                    try:
                        async with session.post(
                            "http://127.0.0.1:8080/gac-agent-desktop/v1/ai_desktop",
                            read_bufsize=4194304,
                            json=payload,
                        ) as response:
                            response.raise_for_status()

                            # 使用 AgentStreamHelper 的统一流式解析
                            async for raw_chunk in response.content.iter_any():
                                streamHelper.feed(raw_chunk)
                            streamHelper.flush()

                        result.code = code_complete
                        result.query = q

                        raw_response = streamHelper.get_raw_response()
                        logger.debug(f"[RAW_RESPONSE]: {raw_response}")

                        answer = streamHelper.get_json_data("result")
                        logger.debug(f"[ANSWER]: {answer}")

                        # 解析并打印关键信息，便于调试与回归核验
                        try:
                            extracted = BadCaseRunner.extract_phase_fields(answer)
                            logger.debug(
                                f"[EXTRACTED_PHASES]: intent={extracted.get('phase_intent')}, "
                                f"image_url={extracted.get('phase_generation_url')}, "
                                f"size=({extracted.get('width')}x{extracted.get('height')}), "
                                f"color={extracted.get('phase_color')}"
                            )

                            # 累积多轮答案
                            if extracted:
                                all_answers.append(extracted)
                        except Exception as _:
                            # 忽略解析异常
                            pass

                    except BaseException as e:
                        logger.error(
                            f"new_run_single_case: payload: {payload}, e: {str(e)}"
                        )
                        # 继续尝试后续轮次
                        continue

            # 循环结束后，合并所有轮次的问答
            if all_answers:
                merged_blocks = []
                for idx, ans in enumerate(all_answers, start=1):
                    q_text = rounds[idx - 1] if idx - 1 < len(rounds) else ""
                    ans_str = json.dumps(ans, ensure_ascii=False)
                    merged_blocks.append(f"--- Round {idx} ---\nQ: {q_text}\nA:\n{ans_str}")
                result.result_text = "\n\n".join(merged_blocks)
                result.all_answers = all_answers

        except BaseException as e:
            logger.error(
                f"new_run_single_case: request_body: {request_body}, e: {str(e)}"
            )

        return result

    async def run_bad_cases(self):
        writer = ExcelResultWriter(excelHelper)
        skipped_case_list = []
        async for row in excelHelper.read_rows("桌面"):
            id = row.get("id")
            original_title = row.get("original_title")
            requirement = row.get("expected_output")
            request_body = row.get("request_body")
            logger.info(f"Processing case. original_title: {original_title}")
            if isinstance(request_body, str) and request_body.strip():
                try:
                    request_body_dict = json.loads(request_body)
                except Exception:
                    logger.error("request_body 解析失败")
                    skipped_case_list.append(id)
                    continue
            else:
                request_body_dict = request_body

            if not isinstance(request_body_dict, dict):
                logger.warning("request_body 不是字典，跳过该用例")
                skipped_case_list.append(id)
                continue

            logger.debug(f"request_body_dict: {request_body_dict}")
            try: 
                single_case_result = await BadCaseRunner.run_single_case(request_body_dict)
            except Exception as e:
                logger.error(f"run_single_case exception: {e}")
                skipped_case_list.append(id)
                continue

            if single_case_result.code != code_complete:
                logger.error(f"run_single_case failed: {single_case_result}")

            answers = single_case_result.all_answers or []
            for i, answer in enumerate(answers, start=1):
                judge_result = await judgeHelper.judge_result(
                    query=json.dumps(request_body_dict, ensure_ascii=False)
                    if isinstance(request_body_dict, (dict, list))
                    else str(request_body_dict),
                    answer=answer,
                    requirement=str(requirement or ""),
                )
                logger.debug(f"judge_result: {judge_result}")
                # 解析判定 JSON，拆分为 judge_result 与 judge_reason
                judge_type, judge_reason = BadCaseRunner._extract_judge_fields(
                    judge_result or ""
                )

                answer = f"--- Round {i} ---\nQ: {request_body_dict['queries'][i-1]['query']}, img_url: {request_body_dict['queries'][i-1].get('image_url', '')}\nA:\n{answer}"

                # 收集并缓存在 writer 中
                await writer.append_row(
                    row,
                    regression_output=answer,
                    judge_result=judge_type or "",
                    judge_reason=judge_reason,
                )

        # 所有用例处理结束后，统一写入新 Excel
        writer.write_to(file_path_out)

        # 记录跳过的用例，写入到txt文件
        if len(skipped_case_list) > 0:
            with open("skipped_cases.txt", "w") as f:
                f.writelines([f"{case_id}\n" for case_id in skipped_case_list])


if __name__ == "__main__":
    loop = asyncio.new_event_loop()
    badCaseRunner = BadCaseRunner()
    try:
        asyncio.set_event_loop(loop=loop)
        loop.run_until_complete(badCaseRunner.run_bad_cases())
    finally:
        loop.close()
