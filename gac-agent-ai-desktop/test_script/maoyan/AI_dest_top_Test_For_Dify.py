import re
import requests
import time
import json
import os
import pandas as pd
import asyncio
import aiohttp
# ===== 配置区域 =====

# 耗时阈值常量配置
DIFY_TTFB_THRESHOLD = 10  # Dify-TTFB耗时阈值
T1_MEM_THRESHOLD = 0.1     # t1-mem耗时阈值
T2_INTENT_THRESHOLD = 0.5  # t2-intent耗时阈值
T3_GEN_THRESHOLD = 6.5       # t3-gen耗时阈值
T4_COLOR_THRESHOLD = 2     # t4-color耗时阈值
TOTAL_TIME_THRESHOLD = 10     # total_time耗时阈值

url = "https://agent-sit.senseauto.com/v1/chat-messages"
headers = {
    "Authorization": "Bearer app-MYaeIrJu5nOdReX1zyEOsRhy",
    "Content-Type": "application/json"
}
# url = "https://api.dify.ai/v1/chat-messages"
#
# headers = {
#     "Authorization": "Bearer app-PAd6Bp84DTbhMKbwQLK02fMY",
#     "Content-Type": "application/json"
# }
test_time = time.strftime("%Y%m%d_%H%M%S", time.localtime())
queries = [
    "帮我把这张图片变成卡通风格",
    "帮我把这张图片变成3D渲染风格",
    # "帮我把这张图片变成纯真儿童画风格",
    # "帮我把这张图片变成梵高星空风格",
    # "帮我把这张图片变成清新动漫风格",
    # "帮我把这张图片变成日式清新风格",
    # "帮我把这张图片变成水彩世界风格",
    # "帮我把这张图片变成细腻写实风格",
    # "帮我把这张图片变成绚丽水彩风格",
    # "帮我把这张图片变成质感油画风格",
    "再来一张"
]

# car_ids 数组，请确保其内容和长度与 queries 数组匹配
car_ids = [
    "A29VXHTESTVIN5211",
    "LMGHP1S2XJ1S00120",
    "LMGHP1S2XJ1S00120"
]

# desktop_path = os.path.expanduser("~/Desktop")
# output_file = os.path.join(desktop_path, "AI新闻_Dify_20250713_tencent.txt")

desktop_path = os.path.join(os.path.expanduser("~"),"桌面")
output_file = os.path.join("C:\\zhongyuanfeng_vendor\\桌面", "AI新闻_Dify_20250713_tencent.txt")
print(desktop_path)

# ===== 开始测试 =====

results = []

excelData = {
    'query': [],
    'Dify_start_time': [],
    'start_time_utf': [],
    'request_time_utf': [],
    'response_time_utf': [],
    'Dify-TTFB': [],
    't1-mem': [],
    't2-intent': [],
    't3-gen': [],
    't4-color': [],
    'total_time': [],
    'Answer': [],
    'Remark': []
}


def output_excel():


    df = pd.DataFrame(excelData)
    # 使用ExcelWriter和xlsxwriter引擎
    file_path = 'AI互动桌面_'+test_time+'.xlsx'
    writer = pd.ExcelWriter(file_path, engine='xlsxwriter')
    df.to_excel(writer, index=False, sheet_name='AI_dest')
    # 获取工作表和workbook对象
    workbook = writer.book
    worksheet = writer.sheets['AI_dest']
    # 定义格式
    header_format = workbook.add_format({
        'bold': True,
        'text_wrap': True,
        'valign': 'center',
        'fg_color': '#D7E4BC',
        'border': 1})
    data_format = workbook.add_format({'align': 'center', "valign": "vcenter",'num_format': '0.####'})
    # 设置列宽
    worksheet.set_column(0, len(excelData) - 2, 20, data_format)
    worksheet.set_column(len(excelData) - 1, len(excelData) - 1, 60, data_format)
    for row in range(1, len(queries) - 1):  # 假设设置前 100 行
        worksheet.set_row(row, 30)
    # 应用标题格式（第一行是标题行）
    worksheet.write_row('A1', df.columns, header_format)

    # 添加条件格式 - 黄色背景标记超时
    yellow_format = workbook.add_format({'bg_color': '#FFFF00'})  # 黄色背景
    
    # 需要检查的列及其对应的Excel列字母
    check_columns = {
        'F': 'Dify-TTFB',  # F列对应Dify-TTFB
        'G': 't1-mem',     # G列对应t1-mem
        'H': 't2-intent',  # H列对应t2-intent
        'I': 't3-gen',     # I列对应t3-gen
        'J': 't4-color',    # J列对应t4-color
        'K': 'total_time'    # K列对应total_time
    }
    
    # 应用条件格式到每一列
    for col_letter, col_name in check_columns.items():
        # 获取数据行范围（从第2行开始，到最后一行）
        start_row = 1  # Excel行号从0开始，第2行对应索引1
        end_row = len(excelData[col_name]) - 1  # 最后一行
        
        # 为当前列设置条件格式
        if col_letter == 'F':  # Dify-TTFB列特殊处理
            # 规则1: 值为N/A
            worksheet.conditional_format(
                f'{col_letter}{start_row + 1}:{col_letter}{end_row + 1}',
                {
                    'type': 'formula',
                    'criteria': f'={col_letter}2="N/A"',
                    'format': yellow_format
                }
            )
            # 规则2: 数值大于等于阈值
            worksheet.conditional_format(
                f'{col_letter}{start_row + 1}:{col_letter}{end_row + 1}',
                {
                    'type': 'formula',
                    'criteria': f'=AND(ISNUMBER(VALUE({col_letter}2)), VALUE({col_letter}2)>={DIFY_TTFB_THRESHOLD})',
                    'format': yellow_format
                }
            )
        else:
            # 获取对应列的阈值
            threshold_var = globals()[f"{col_name.replace('-', '_').upper()}_THRESHOLD"]
            worksheet.conditional_format(
                f'{col_letter}{start_row + 1}:{col_letter}{end_row + 1}',
                {
                    'type': 'formula',
                    'criteria': f'=AND(ISNUMBER({col_letter}2), {col_letter}2>={threshold_var})',
                    'format': yellow_format
                }
            )

    writer.close()

def fill_excel_data(query_text,start_time,ttfb_display,final_answer,sense_time_info):
    start_time_utf = float(sense_time_info.get("start_time_utf", 0)) if type(sense_time_info) is dict else 0
    request_time_utf = float(sense_time_info.get("request_time_utf", 0)) if type(sense_time_info) is dict else 0
    response_time_utf = float(sense_time_info.get("response_time_utf", 0)) if type(sense_time_info) is dict else 0
    t1_mem = float(sense_time_info.get("t1-mem", 0)) if type(sense_time_info) is dict else 0
    t2_intent = float(sense_time_info.get("t2-intent", 0)) if type(sense_time_info) is dict else 0
    t3_gen = float(sense_time_info.get("t3-gen", 0)) if type(sense_time_info) is dict else 0
    t4_color = float(sense_time_info.get("t4-color", 0)) if type(sense_time_info) is dict else 0
    total_time = float(sense_time_info.get("total_time", 0)) if type(sense_time_info) is dict else 0

    # 计算各阶段耗时
    t2_intent_display = t2_intent - t1_mem
    t3_gen_display = t3_gen - t2_intent
    t4_color_display = t4_color - t3_gen
    
    # 检查哪些列的值大于等于阈值或为N/A
    failed_columns = []
    if ttfb_display == "N/A":
        failed_columns.append(f"Dify-TTFB为N/A")
    # ===== 新增对Error情况的处理 =====
    elif ttfb_display != "Error":
        ttfb_value = float(ttfb_display)
        if ttfb_value >= DIFY_TTFB_THRESHOLD:
            failed_columns.append(f"Dify-TTFB超过{DIFY_TTFB_THRESHOLD}")
    if t1_mem >= T1_MEM_THRESHOLD:
        failed_columns.append(f"t1-mem超过{T1_MEM_THRESHOLD}")
    if t2_intent_display >= T2_INTENT_THRESHOLD:
        failed_columns.append(f"t2-intent超过{T2_INTENT_THRESHOLD}")
    if t3_gen_display >= T3_GEN_THRESHOLD:
        failed_columns.append(f"t3-gen超过{T3_GEN_THRESHOLD}")
    if t4_color_display >= T4_COLOR_THRESHOLD:
        failed_columns.append(f"t4-color超过{T4_COLOR_THRESHOLD}")
    if total_time >= TOTAL_TIME_THRESHOLD:
        failed_columns.append(f"total_time超过{TOTAL_TIME_THRESHOLD}")
    
    # 检查image_url和color字段是否有效
    answer_issues = []
    # 查找image_url字段
    image_url_match = re.search(r'"image_url"\s*:\s*"([^"]*)"', final_answer)
    if not image_url_match:
        answer_issues.append("image_url缺失")
    else:
        image_url = image_url_match.group(1)
        if not image_url or "http" not in image_url:
            answer_issues.append("image_url无效")

    # 查找color字段
    color_match = re.search(r'"color"\s*:\s*"([^"]*)"', final_answer)
    if not color_match:
        answer_issues.append("color缺失")
    else:
        color = color_match.group(1)
        if not color or not color.startswith("#"):
            answer_issues.append("color无效")
    
    # 生成备注信息
    remark_parts = []
    if failed_columns:
        remark_parts.append("; ".join(failed_columns))
    if answer_issues:
        remark_parts.append("; ".join(answer_issues))
    
    remark = "; ".join(remark_parts) if remark_parts else ""

    excelData['query'].append(query_text)
    excelData['Dify_start_time'].append(start_time)
    excelData['start_time_utf'].append(start_time_utf)
    excelData['request_time_utf'].append(request_time_utf)
    excelData['response_time_utf'].append(response_time_utf)
    excelData['Dify-TTFB'].append(ttfb_display)
    excelData['t1-mem'].append(t1_mem)
    excelData['t2-intent'].append(t2_intent - t1_mem)
    excelData['t3-gen'].append(t3_gen - t2_intent)
    excelData['t4-color'].append(t4_color - t3_gen)
    excelData['total_time'].append(total_time)
    excelData['Answer'].append(final_answer)
    excelData['Remark'].append(remark)


def add_params_des():
    excelData['query'].append(" ")
    excelData['Dify_start_time'].append("Dify智能体请求开始时间")
    excelData['start_time_utf'].append("插件工具开始执行时间")
    excelData['request_time_utf'].append("插件工具里开始请求原子能力接口时间")
    excelData['response_time_utf'].append("插件工具里得到响应时间")
    excelData['Dify-TTFB'].append("Dify智能体接口首字延迟")
    excelData['t1-mem'].append("获取用户画像")
    excelData['t2-intent'].append("提槽")
    excelData['t3-gen'].append("图片风格化")
    excelData['t4-color'].append("图片主题色")
    excelData['total_time'].append("总时间")
    excelData['Answer'].append(" ")
    excelData['Remark'].append("")


img_path = "img528_705.txt"


with open(img_path, 'r', encoding='utf-8') as file:
    img_base_64 = file.read()


async def process_img(idx, query_text, car_id, conversation_id=None):
    print(f"🚀 正在测试 Query {idx}/{len(queries)} | query: {query_text} | car_id: {car_id}")

    payload = {
        "query": query_text,
        "response_mode": "streaming",
        "inputs": {
            "car_id": car_id,
            "user_id": "fake_user_id",
            "is_async": "false",
            "image_base_64": img_base_64
        },
        "user": "abc-124"
    }

    # 如果传入了 conversation_id，就添加到 payload 中
    if conversation_id:
        payload["conversation_id"] = conversation_id
        print(f"💡 正在为 Query {idx} 传入 conversation_id: {conversation_id}")

    # response = await asyncio.to_thread(requests.post, url, headers=headers, json=payload, stream=True, timeout=50)
    # response.raise_for_status()

    start_time = time.time()
    print(f"✅ start_time ：{start_time}")

    # 初始化状态
    first_message_time = None
    final_answer = ""
    sense_time_info = None
    captured_conversation_id = None # 用于存储捕获的ID

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                    url,
                    headers=headers,
                    json=payload,
                    read_bufsize=2097152,
                    timeout=aiohttp.ClientTimeout(total=100)
            ) as response:
                response.raise_for_status()

                # 异步流式读取
                async for raw_chunk in response.content:
                    chunk = raw_chunk.decode('utf-8').strip()

                    for line in chunk.split('\n'): # 处理一个chunk中可能有多行data的情况
                        if not line.strip():
                            continue

                        if line.startswith("data: "):
                            json_str = line[len("data: "):]
                            try:
                                data = json.loads(json_str)
                            except json.JSONDecodeError as e:
                                print(f"error_event_type: {json_str}\n e = {str(e)}")
                                continue

                            event_type = data.get("event")
                            # print(f"event_type: {event_type}")

                            # 捕获 conversation_id
                            if event_type == "workflow_started" and "conversation_id" in data:
                                captured_conversation_id = data.get("conversation_id")
                                print(f"⭐ Query {idx} 成功捕获 conversation_id: {captured_conversation_id}")

                            if event_type == "message":
                                if first_message_time is None:
                                    first_message_time = time.time()
                                    ttfb = round((first_message_time - start_time), 4)

                                if "answer" in data:
                                    try:
                                        answer_obj = json.loads(data["answer"])
                                        text = answer_obj.get("data", "")
                                        final_answer += text
                                    except json.JSONDecodeError:
                                        continue

                            elif event_type == "agent_log":
                                log_data = data.get("data")
                                if isinstance(log_data, dict) and log_data.get("label") == "Sense_Time_Info":
                                    sense_time_info = log_data.get("data")

        except Exception as e:
            print(f"⚠️ Query {idx} 请求异常: {e}")
            # 即使异常也要填充数据，以便记录
            ttfb_display = "Error"
            final_answer = f"请求异常: {e}"
            fill_excel_data(query_text, start_time, ttfb_display, final_answer, sense_time_info or {})
            return None # 异常时返回 None


    # 输出并保存结果
    ttfb_display = str(round((first_message_time - start_time), 4)) if first_message_time else "N/A"
    final_answer = final_answer.strip() or "无有效响应"
    sense_time_str = json.dumps(sense_time_info, ensure_ascii=False) if sense_time_info else "无"

    result_text = f"\n\nQuery: {query_text}\nTTFB(ms): {ttfb_display}\nSense_Time_Info: {sense_time_str}\nFinal Answer: {final_answer}"
    results.append(result_text)
    fill_excel_data(query_text,start_time, ttfb_display, final_answer, sense_time_info)
    
    return captured_conversation_id


async def main():
    num_queries = len(queries)
    if num_queries < 2:
        # 如果用例少于2个，全部并发执行
        concurrent_tasks = [
            process_img(idx, query_text, car_ids[idx-1])
            for idx, query_text in enumerate(queries, 1)
        ]
        if concurrent_tasks:
            await asyncio.gather(*concurrent_tasks)
        return

    # 1. 并发执行除了最后两个之外的所有任务
    print("--- 正在并发执行前 N-2 个任务 ---")
    concurrent_tasks = [
        process_img(idx, query_text, car_ids[idx-1])
        for idx, query_text in enumerate(queries[:-2], 1)
    ]
    if concurrent_tasks:
        await asyncio.gather(*concurrent_tasks)
    
    # 2. 串行执行倒数第二个任务，并获取 conversation_id
    print("\n--- 正在串行执行倒数第二个任务以获取 conversation_id ---")
    second_to_last_idx = num_queries - 1
    second_to_last_query = queries[-2] # 索引是 num_queries - 2
    second_to_last_car_id = car_ids[-2]
    captured_id = await process_img(second_to_last_idx, second_to_last_query, second_to_last_car_id)

    # 3. 串行执行最后一个任务，并传入获取到的 conversation_id
    if captured_id:
        print(f"\n--- 正在串行执行最后一个任务，并传入ID: {captured_id} ---")
        last_idx = num_queries
        last_query = queries[-1] # 索引是 num_queries - 1
        last_car_id = car_ids[-1]
        await process_img(last_idx, last_query, last_car_id, conversation_id=captured_id)
    else:
        print("\n--- 未能捕获 conversation_id，最后一个任务将独立执行 ---")
        last_idx = num_queries
        last_query = queries[-1]
        last_car_id = car_ids[-1]
        await process_img(last_idx, last_query, last_car_id)


if __name__ == '__main__':
    loop = asyncio.new_event_loop()  # 创建新事件循环
    asyncio.set_event_loop(loop)  # 设置为当前循环
    try:
        # 运行重构后的 main 函数
        loop.run_until_complete(main())
    finally:
        add_params_des()
        output_excel()
        print("✅ 写入完成\n")
        loop.close()  # 清理资源

# ===== 写入文件 =====

# try:
#     with open(output_file, "w", encoding="utf-8") as f:
#         f.write("\n".join(results))
#     print(f"📄 测试结果已保存到：{output_file}")
# except Exception as e:
#     print(f"❌ 写文件失败: {e}")
