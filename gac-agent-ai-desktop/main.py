from fastapi import FastAPI
import uvicorn
import configuration
from src.schedule.scheduler import global_scheduler
from src.gac.router_gac import router as router_gac

# 以下两个task绝对不可删掉！否则会影响生图后的结果查询
from src.schedule.task.remove_task import remove_expired_image_result_task
from src.schedule.task.query_result_task import query_image_result_task

global_config = configuration.config
current_version = global_config['settings']['version']


log_name = "ai_desk_top"

app = FastAPI(title=log_name)
app.include_router(router_gac)

@app.on_event("startup")
async def startup():
    global_scheduler.start()

@app.on_event("shutdown")
async def shutdown():
    await global_scheduler.shutdown()

if __name__ == "__main__":

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=global_config["settings"]["port"],
        workers=2,
        use_colors=True,
    )