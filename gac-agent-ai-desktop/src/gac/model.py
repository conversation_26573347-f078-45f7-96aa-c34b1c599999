from dataclasses import dataclass

from pydantic import BaseModel
from typing import Optional
from src.constance_default import default_size_height, default_size_width

class UserMemoryReq(BaseModel):
    car_id: str
    user_id: str
    category: list[str] = []

class TargetSizeReq(BaseModel):
    height: int = default_size_height
    width: int = default_size_width

class AiDesktopReq(BaseModel):
    query: str
    image_base64: Optional[str] = None
    image_url: Optional[str] = None
    stream: bool = False
    is_async: bool = False
    user_info: Optional[UserMemoryReq] = None
    remove_water_mark: bool = False
    output_size: TargetSizeReq = TargetSizeReq()

class VehicleParameterReq(BaseModel):
    vin: str
    client_id: str
    client_secret: str
    company_id: str


class ErrorDataModel:
    def __init__(self, error_code: int, error_message: str = "Unknown error"):
        self.error_code = error_code
        self.error_message = error_message


@dataclass
class MemResultModel:
    result: str = ""
    time_cost: float = 0
    error_info: Optional[ErrorDataModel] = None

@dataclass
class IntentResultModel:
    result: Optional[str] = None
    error_info: Optional[ErrorDataModel] = None

@dataclass
class GenerationResultModel:
    result: Optional[str] = None
    error_info: Optional[ErrorDataModel] = None

@dataclass
class ColorResultModel:
    result: Optional[str] = None
    error_info: Optional[ErrorDataModel] = None

@dataclass
class TaskInfoResultModel:
    result: Optional[str] = None
    error_info: Optional[ErrorDataModel] = None