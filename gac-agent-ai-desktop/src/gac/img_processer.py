import json
import requests
import httpx
from loguru import logger
from src.gac.img_processer_state import ImgProcesserState, ApiKeyInfo
from typing_extensions import Optional
from src.gac.styles_list import style_dict
from src.sensitive_exception import GenerationErrorException, code_invalid_intent, code_intent_json_wrong, code_fail_to_run_style_intent, code_generation_inner_error
from src.util.time_util import addDurationAndEndTime
from src.util.format_util import extract_json_str_from_text
from src.util.key_util import generate_token
from configuration import config
from openai import OpenAI
from src.prompt.extra_style import prompt_extra_style
from src.prompt.extra_color import prompt_extra_color
from src.constance_default import default_size_height, default_size_width, default_stylization_name
from src.gac.model import ErrorDataModel, IntentResultModel, GenerationResultModel, ColorResultModel

CODE_SENSITIVE = 18

API_URL = "https://api.sensenova.cn/v1/llm/chat-completions"
MODEL = "SenseNova-V6-Turbo"

IMG_STYLE_API_URL = "https://mhapi.sensetime.com/v1/stylization/imgen_sync"

async_client = httpx.AsyncClient(timeout=60)

DEFAULT_STYLE = default_stylization_name

class ImgProcesser:

    def __init__(self, generation_key: ApiKeyInfo, modality_key: ApiKeyInfo, query: str, habit: Optional[str], img_url: Optional[str] = None, img_base64: Optional[str] = None
                 , multi_modal_model_name: str = MODEL, need_detect: bool = False, remove_water_mark: bool = False
                 , output_width: int = default_size_width, output_height: int = default_size_height):
        self.generation_key = generation_key
        self.modality_key = modality_key
        self.query = query
        self.habit = habit
        self.img_url = img_url
        self.img_base64 = img_base64
        self.multi_modal_model_name = multi_modal_model_name
        self.need_detect = need_detect
        self.remove_water_mark = remove_water_mark
        self.output_width = output_width
        self.output_height = output_height

    # query 用户输入
    # habit 记忆体信息，可能为空
    @addDurationAndEndTime
    async def style_intent(self, query: str, habit: Optional[str]) -> IntentResultModel:
        default_style = "细腻写实"
        error_data = None
        try:
            # 1. 从全局配置中获取API信息
            openai_api_key = config["openai_config"]["api_key"]
            openai_api_base = config["openai_config"]["base_url"]
            model_name = config["openai_config"]["model_name"]

            # 2. 初始化OpenAI同步客户端
            client = OpenAI(api_key=openai_api_key, base_url=openai_api_base)

            # 3. 提示词构建
            system_prompt = prompt_extra_style

            style_options = json.dumps(list(style_dict.keys()), ensure_ascii=False)
            habit_text = habit if habit else ""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": json.dumps({
                    "user_profile" : habit_text
                    , "current_query" : query
                    , "style_list" : style_options
                }, ensure_ascii=False)},
            ]

            first_choice_message = None
            try:
                # 4. 使用OpenAI客户端发起请求
                response = client.chat.completions.create(
                    model=model_name,
                    messages=messages,
                    stream=False
                )

                # 5. 解析OpenAI返回的结果
                from src.sensitive_exception import word_sensitive_query
                if word_sensitive_query == response.choices[0].finish_reason:
                    from src.sensitive_exception import code_sensitive_query
                    return IntentResultModel(result=None, error_info=ErrorDataModel(error_code=code_sensitive_query,
                                                                                    error_message=f"query: {query}, contains sensitive information."))
                first_choice_message = response.choices[0].message.content
                logger.info(f"style_intent result is: {first_choice_message}")
            except BaseException as e:
                from src.sensitive_exception import code_intent_http_wrong
                error_data = ErrorDataModel(error_code=code_intent_http_wrong, error_message=f"Intent http error, e: {str(e)}")

            if first_choice_message:
                try:
                    result_json = json.loads(extract_json_str_from_text(first_choice_message))
                    result_intent = result_json.get("intent", "")
                    if "generation" == result_intent.lower():
                        if "target_style" in result_json:
                            target_style = result_json["target_style"]
                            if isinstance(target_style, str):
                                return IntentResultModel(result=target_style, error_info=None)
                            else:
                                return IntentResultModel(result=default_style, error_info=None)
                    else:
                        error_data = ErrorDataModel(error_code=code_invalid_intent, error_message=f"Not generation intent, ai desktop request should deny it.")
                except BaseException as e:
                    error_data = ErrorDataModel(error_code=code_intent_json_wrong, error_message=f"Fail to get style intent from llm result, and llm result is {first_choice_message}")

            return IntentResultModel(result=None, error_info=error_data)

        except Exception as e:
            error_msg = f"Failed to run style_intent(). Error: {str(e)}"
            logger.error(error_msg)
            error_data = ErrorDataModel(error_code=code_fail_to_run_style_intent, error_message=error_msg)
            return IntentResultModel(result=None, error_info=error_data)

    @addDurationAndEndTime
    async def img_style(self, img_style_name: Optional[str]) -> GenerationResultModel:
        img_style_name_from_llm = img_style_name
        if not img_style_name:
            img_style_name = DEFAULT_STYLE
        if not (img_style_name in style_dict.keys()):
            img_style_name = DEFAULT_STYLE

        imgResult = None
        error_data = None
        # access_token有效期设置为1分钟
        access_token = generate_token(self.generation_key.ak, self.generation_key.sk, 60)
        print(f"img_style()  img_style_name_from_llm: {img_style_name_from_llm}  img_style_name: {img_style_name} access_token: {access_token}")
        headers = {'Content-Type': 'application/json', 'Authorization': 'Bearer ' + access_token}

        if style_dict.get(img_style_name) is not None:
            modelId = style_dict[img_style_name]
            data = {
                "model_id": modelId,
                "type": "Default",
                "samples": 1,
                "height": self.output_height,
                "width": self.output_width,
            }
            # 同时传入，使用优先级由秒画接口内部逻辑处理，当前（2025.08.07）秒画接口文档说明为优先使用url
            if self.img_url:
                data["img_url"] = self.img_url
            if self.img_base64:
                data["img_base64"] = self.img_base64
            if self.remove_water_mark:
                data["watermark_config"] = {"type": "Kz36db"}
            try:
                response = await async_client.post(IMG_STYLE_API_URL, headers=headers, json=data, timeout=30)
                response.raise_for_status()
                json_response = response.json()
                print(f"json_response: {json_response}")
                if "error" in json_response:
                    error_msg = f"stylization error, error: {json_response['error']}"
                    from src.sensitive_exception import code_generation_error
                    error_data = ErrorDataModel(error_code=code_generation_error, error_message=error_msg)
                if "state" in json_response:
                    if json_response['state'] == 'SUCCESS':
                        images = json_response['images']
                        if len(images) > 0:
                            imgResult = images[0]['raw']
                        else:
                            error_data = ErrorDataModel(error_code=code_generation_inner_error, error_message=f"Error: {json_response['state_message']}")
                    else:
                        error_data = ErrorDataModel(error_code=code_generation_inner_error, error_message=f"Error: {json_response['state_message']}")
            except BaseException as e:
                from src.sensitive_exception import code_generation_error
                error_data = ErrorDataModel(error_code=code_generation_error, error_message=f"Failed to complete stylization request. Error: {str(e)}")
            return GenerationResultModel(result=imgResult, error_info=error_data)
        else:
            from src.sensitive_exception import code_generation_error
            error_data = ErrorDataModel(error_code=code_generation_error, error_message=f"fail to find match img_style_name, img_style_name: {img_style_name}")
            return GenerationResultModel(result=None, error_info=error_data)

    @addDurationAndEndTime
    async def img_theme(self, img_url: str) -> ColorResultModel:
        if not img_url:
            return ColorResultModel(result=None, error_info=None)
        # access_token有效期设置为1分钟
        access_token = generate_token(self.modality_key.ak, self.modality_key.sk, 60)
        print(f"img_theme()  access_token: {access_token}")
        headers = {'Content-Type': 'application/json', 'Authorization': 'Bearer ' + access_token}
        data = {
            "stream": False,
            "model": self.multi_modal_model_name,
            "messages": [
                {
                    "role": "system",
                    "content": [
                        {
                            "type": "text",
                            # "text": "你是一个图像分析助手，主要功能是分析图像的主题颜色，分析结果为该图片对应的一个16进制的ARGB色值（例如纯白色为#FFFFFFFF，透明度为50%的黑色为#80000000），分析结果按如下json格式返回：{\"color\":\"#78787878\"}"
                            "text": prompt_extra_color
                        }
                    ]
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请根据输入图像，给出分析结果"
                        },
                        {
                            "type": "image_url",
                            "image_url": img_url
                        }
                    ]
                }
            ]
        }

        result_color = None
        error_data = None
        try:
            response = await async_client.post(API_URL, headers=headers, json=data)
            status_code = response.status_code
            print(f"ImgThemeTool_status_code: {status_code}")
            response.raise_for_status()
            response_json = response.json()
            print(response_json)
            response_data_json = response_json.get("data", {})
            response_choices_list = list(response_data_json.get("choices", []))
            if 0 < len(response_choices_list):
                first_choice_message = None
                try:
                    first_choice_message = response_choices_list[0].get("message", "")
                    print(first_choice_message)
                    index_start = first_choice_message.index("{")
                    index_end = first_choice_message.index("}")
                    if 0 <= index_start < index_end:
                        color_json = first_choice_message[index_start: (index_end + 1)]
                        print(f"color_json: {color_json}")
                        result_color = json.loads(color_json).get("color")
                except BaseException as e:
                    from src.sensitive_exception import code_color_return_error
                    error_data = ErrorDataModel(error_code=code_color_return_error, error_message=f"Get color failed, first choice_message: {first_choice_message}")
                    print(e)
        except BaseException as e:
            from src.sensitive_exception import code_color_http_error
            error_data = ErrorDataModel(error_code=code_color_http_error,
                                        error_message=f"Get color http failed, e: {str(e)}")
            print(e)

        return ColorResultModel(result=result_color, error_info=error_data)

    async def engage(self, state: ImgProcesserState, error_list: Optional[list] = None):
        message_id = state["message_id"]
        if error_list:
            from src.gac.phase_list import phase_error
            for error_item in error_list:
                if isinstance(error_item, ErrorDataModel):
                    yield {
                        "type": phase_error,
                        "code": error_item.error_code,
                        "data": error_item.error_message,
                        "message_id": message_id,
                    }

        logger.info("intent start")
        time_map = None
        intent_result_model, time_map = await self.style_intent(self.query, self.habit)
        result = intent_result_model.result
        if intent_result_model.error_info:
            from src.gac.phase_list import phase_error
            yield {
                "type": phase_error,
                "code": intent_result_model.error_info.error_code,
                "data": intent_result_model.error_info.error_message,
                "message_id": message_id,
            }
            from src.sensitive_exception import code_invalid_intent, code_sensitive_query
            if intent_result_model.error_info.error_code == code_invalid_intent:
                response_data_list = ['这个', '问题', '有点', '超出', '我', '的', '知识', '范畴', '啦', '']
                import asyncio
                from src.gac.phase_list import phase_message
                for temp_data in response_data_list:
                    # 模拟异步延迟
                    await asyncio.sleep(0.1)
                    yield {"data": temp_data, "type": phase_message, "messageId": message_id }
                return
            if intent_result_model.error_info.error_code == code_sensitive_query:
                # 存在敏感词，直接停止
                return
            result = None
        if isinstance(time_map, dict) and "end_time" in time_map:
            intent_time_cost = round((time_map["end_time"] - state["start_time"]), 4)
            state["t2_intent"] = intent_time_cost
            logger.info(f"intent end-----cost time:{intent_time_cost}-----habit:{result}")
        from src.gac.phase_list import phase_intent
        yield {
            "type": phase_intent,
            "data": result,
            "message_id": message_id,
        }

        if not result:
            return
        style_name = result

        logger.info("generation start")
        time_map = None
        generation_result_model, time_map = await self.img_style(style_name)
        result = generation_result_model.result
        if generation_result_model.error_info:
            from src.gac.phase_list import phase_error
            yield {
                "type": phase_error,
                "data": generation_result_model.error_info.error_message,
                "code": generation_result_model.error_info.error_code,
                "message_id": message_id
            }
            result = None

        if isinstance(time_map, dict) and "end_time" in time_map:
            generation_time_cost = round((time_map["end_time"] - state["start_time"]), 4)
            state["t3_generation"] = generation_time_cost
            logger.info(f"generation end-----cost time:{generation_time_cost}-----habit:{result}")
        from src.gac.phase_list import phase_generation
        yield {
            "type": phase_generation,
            "data": result,
            "extraInfo": {
                "width": self.output_width,
                "height": self.output_height,
            },
            "message_id": message_id
        }


        result_url = result

        logger.info("color start")
        color_result_model, time_map = await self.img_theme(result_url)
        color_result = color_result_model.result
        if isinstance(time_map, dict) and "end_time" in time_map:
            color_time_cost = round((time_map["end_time"] - state["start_time"]), 4)
            state["t4_color"] = color_time_cost
            logger.info(f"color end-----cost time:{color_time_cost}-----habit:{color_result}")
        if color_result_model.error_info:
            from src.gac.phase_list import phase_error
            yield {
                "type": phase_error,
                "code": color_result_model.error_info.error_code,
                "data": color_result_model.error_info.error_message,
                "message_id": message_id,
            }
        from src.gac.phase_list import phase_color
        yield {
            "type": phase_color,
            "data": color_result,
            "message_id": message_id,
        }




