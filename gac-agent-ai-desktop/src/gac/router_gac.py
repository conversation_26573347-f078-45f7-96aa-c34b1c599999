from fastapi import APIRouter, Response
from loguru import logger

from src.gac.model import AiDesktopReq, UserMemoryReq, VehicleParameterReq, ErrorDataModel, MemResultModel
from sse_starlette.sse import EventSourceResponse as StreamResponse
from starlette.responses import JSONResponse as Restfulesponse
from typing_extensions import Optional
import uuid, time, asyncio, secrets, json
from configuration import config
from src.gac.img_processer import ImgProcesser
from src.gac.img_processer_async import ImgProcesserAsync
from src.gac.img_processer_state import ImgProcesserState as State
from src.gac.img_processer_state import Api<PERSON>eyInfo
from src.sensitive_exception import GenerationErrorException
import configuration

import httpx

VEHICLE_API_BASE_URL = "https://pv-gmc-platform-access.gacicv.com"

router = APIRouter(prefix="/gac-agent-desktop", tags=["gac"])

api_version = configuration.config['settings']['version']

@router.get("/v1/health")
async def health() -> Response:
    """Health check."""
    response_body = {"status": "ok", "version": api_version}
    response_content = json.dumps(response_body).encode("utf-8")
    response_header = {"Content-Type": "application/json"}
    return Response(status_code=200, content=response_content, headers=response_header)


async def require_user_habit(source: Optional[UserMemoryReq], start_time: float, time_limit: float) -> MemResultModel:
    if not source or not source.car_id or not source.user_id or 0 >= len(source.category):
        return MemResultModel(result="", time_cost=round(time.perf_counter() - start_time, 4), error_info=None)

    async def require_habit_from_cache(source: UserMemoryReq):
        body_json = {
            "car_id": source.car_id,
            "face_id": source.user_id,
            "categories": source.category
        }
        error_data = None
        from src.util.user_habit_util import request_habit_from_user_memory_text
        result_str, error_data = await request_habit_from_user_memory_text(body=body_json, relation_filter="LIKES", time_limit=time_limit)
        if not result_str:
            result_str = ""
        return result_str, error_data

    try:
        result, error_data = await asyncio.wait_for(require_habit_from_cache(source), timeout=time_limit)
        return MemResultModel(result=result, time_cost=round(time.perf_counter() - start_time, 4), error_info=error_data)
    except BaseException as e:
        time_use = round(time.perf_counter() - start_time, 4)
        logger.error(f"require_habit_from_cache timeout, e: {e}  time_use: {time_use}")
        return MemResultModel(result="", time_cost=round(time.perf_counter() - start_time, 4), error_info=None)


async def common_post_restful(request: AiDesktopReq, state: State, habit: Optional[str], error_list:Optional[list], start_time: float, is_async: bool = False):
    response_header = {"request_id": state["request_id"]}
    try:
        message_id = secrets.token_hex(7)
        state["message_id"] = message_id
        width = request.output_size.width
        height = request.output_size.height
        # processor = ImgProcesser(ak=ak, sk=sk, query=request.query, habit=habit, img_base64=request.image_base64, remove_water_mark=request.remove_water_mark)
        if not is_async:
            processor = ImgProcesser(ApiKeyInfo(config["sense_model_key"]["ak"], config["sense_model_key"]["sk"]),
                                     ApiKeyInfo(config["multi_modality_key"]["ak"], config["multi_modality_key"]["sk"]), query=request.query, habit=habit, img_url=request.image_url, img_base64=request.image_base64, remove_water_mark=request.remove_water_mark, output_width=width, output_height=height)
        else:
            processor = ImgProcesserAsync(ApiKeyInfo(config["sense_model_key"]["ak"], config["sense_model_key"]["sk"]),
                                     ApiKeyInfo(config["multi_modality_key"]["ak"], config["multi_modality_key"]["sk"]), query=request.query, habit=habit, img_url=request.image_url, img_base64=request.image_base64, remove_water_mark=request.remove_water_mark, output_width=width, output_height=height)

        results_list = []
        async for result in processor.engage(state, error_list=error_list):
            results_list.append(result)

        full_response = {
            "message_id": str(message_id),
            "results": results_list,
            "time_cost": {
                "t0-pre": state.get("t0_pre"),
                "t1-mem": state.get("t1_mem"),
                "t2-intent": state.get("t2_intent"),
                "t3-gen": state.get("t3_generation"),
                "t4-color": state.get("t4_color"),
                "total_time": round(time.perf_counter() - start_time, 4)
            }
        }
        
        return Restfulesponse(full_response, headers=response_header)

    except GenerationErrorException as e:
        logger.error(e)
        import traceback
        traceback.print_exc()
        response = {"error": str(e)}
        return Restfulesponse(response, headers=response_header)

    except Exception as e:
        logger.error(e)
        import traceback
        traceback.print_exc()
        response = {"error": str(e)}
        return Restfulesponse(response, headers=response_header)

async def common_post_stream(request: AiDesktopReq, state: State, habit: Optional[str], error_list: Optional[list], start_time: float, is_async: bool = False):
    response_header = {"request_id": state["request_id"]}

    try:
        message_id = secrets.token_hex(7)
        state["message_id"] = message_id
        width = request.output_size.width
        height = request.output_size.height
        if not is_async:
            processor = ImgProcesser(ApiKeyInfo(config["sense_model_key"]["ak"], config["sense_model_key"]["sk"]),
                                     ApiKeyInfo(config["multi_modality_key"]["ak"], config["multi_modality_key"]["sk"]), query=request.query, habit=habit, img_url=request.image_url, img_base64=request.image_base64, remove_water_mark=request.remove_water_mark, output_width=width, output_height=height)
        else:
            processor = ImgProcesserAsync(ApiKeyInfo(config["sense_model_key"]["ak"], config["sense_model_key"]["sk"]),
                                     ApiKeyInfo(config["multi_modality_key"]["ak"], config["multi_modality_key"]["sk"]), query=request.query, habit=habit, img_url=request.image_url, img_base64=request.image_base64, remove_water_mark=request.remove_water_mark, output_width=width, output_height=height)

        async def event_generator():
            async for result in processor.engage(state, error_list=error_list):
                from src.gac.phase_list import phase_intent, phase_generation, phase_color, phase_error, phase_message
                if isinstance(result, dict) and result.get("type") == phase_intent:
                    yield dict(data=json.dumps(result, ensure_ascii=False))
                if isinstance(result, dict) and result.get("type") == phase_generation:
                    yield dict(data=json.dumps(result, ensure_ascii=False))
                if isinstance(result, dict) and result.get("type") == phase_color:
                    yield dict(data=json.dumps(result, ensure_ascii=False))
                if isinstance(result, dict) and result.get("type") == phase_error:
                    yield dict(data=json.dumps(result, ensure_ascii=False))
                if isinstance(result, dict) and result.get("type") == phase_message:
                    yield dict(data=json.dumps(result, ensure_ascii=False))
            message_end_json = {
                "type": "messageEnd",
                "messageId": str(message_id),
                "time_cost" : {
                    "t0-pre": state.get("t0_pre"),
                    "t1-mem" : state.get("t1_mem"),
                    "t2-intent" : state.get("t2_intent"),
                    "t3-gen" : state.get("t3_generation"),
                    "t4-color" : state.get("t4_color"),
                    "total_time": round(time.perf_counter() - start_time, 4)
                }
            }
            yield dict(data=json.dumps(message_end_json, ensure_ascii=False))


        return StreamResponse(event_generator(), headers=response_header)

    except GenerationErrorException as e:
        logger.error(e)
        raise GenerationErrorException(msg=e.msg, code=e.code)

    except Exception as e:
        logger.error(e)
        import traceback

        traceback.print_exc()
        response = {"error": str(e)}
        return Restfulesponse(response, headers=response_header)


@router.post("/v1/ai_desktop")
async def post_ai_desktop(request: AiDesktopReq):
    start_time = time.perf_counter()
    request_id = uuid.uuid4()
    logger.info(f"ai_desktop request_id: {str(request_id)}   request_query: {request.query} size: {request.output_size}")
    response_header = {"request_id": str(request_id)}

    if not request.image_url and not request.image_base64:
        message_id = secrets.token_hex(7)
        from src.gac.phase_list import phase_error
        from src.sensitive_exception import code_both_empty_url_base64
        if request.stream:
            async def empty_image_generator():
                yield dict(data=json.dumps({
                    "type": phase_error,
                    "messageId": str(message_id),
                    "data": "image_url and image_base64 are both EMPTY!!",
                    "code": code_both_empty_url_base64
                }, ensure_ascii=False))
                yield dict(data=json.dumps({
                    "type": "messageEnd",
                    "messageId": str(message_id),
                    "time_cost" : {
                        "t0-pre": 0,
                        "t1-mem" : 0,
                        "t2-intent" : 0,
                        "t3-gen" : 0,
                        "t4-color" : 0,
                        "total_time": round(time.perf_counter() - start_time, 4)
                    }
                }, ensure_ascii=False))

            return StreamResponse(empty_image_generator(), headers=response_header, ping=5)
        else:
            full_response = {
                "message_id": str(message_id),
                "results": [{
                    "type": phase_error,
                    "messageId": str(message_id),
                    "data": "image_url and image_base64 are both EMPTY!!",
                    "code": code_both_empty_url_base64
                }],
                "time_cost" : {
                        "t0-pre": 0,
                        "t1-mem" : 0,
                        "t2-intent" : 0,
                        "t3-gen" : 0,
                        "t4-color" : 0,
                        "total_time": round(time.perf_counter() - start_time, 4)
                }
            }

            return Restfulesponse(full_response, headers=response_header)

    try:
        state = State(start_time=start_time, request_id=str(request_id))
        logger.info("mem start")
        state["t0_pre"] = round((time.perf_counter() - start_time), 4)
        habit_model = await require_user_habit(request.user_info, start_time, 0.8)
        habit = habit_model.result
        habit_time_cost = habit_model.time_cost
        state["t1_mem"] = habit_time_cost
        logger.info(f"mem end-----cost time:{habit_time_cost}-----habit:{habit}")

        error_list = []
        if habit_model.error_info:
            error_list.append(habit_model.error_info)
        if request.stream:
            return await common_post_stream(request, state, habit, error_list, start_time=start_time, is_async=request.is_async)
        else:
            return await common_post_restful(request, state, habit, error_list, start_time=start_time)

    except GenerationErrorException as e:
        logger.error(e)
        import traceback
        traceback.print_exc()
        response = {"code":18, "error": "sensitive"}
        return Restfulesponse(response, headers=response_header)

    except Exception as e:
        logger.error(e)
        import traceback
        traceback.print_exc()
        response = {"error": str(e)}
        return Restfulesponse(response, headers=response_header)
    
@router.post("/v1/vehicle_parameter")
async def get_vehicle_parameter(request: VehicleParameterReq):
    request_id = uuid.uuid4()
    logger.info(f"vehicle_parameter request_id: {str(request_id)}   request_vin: {request.vin}")
    headers = {"request_id": str(request_id)}
    
    async with httpx.AsyncClient() as client:
        try:
            # 步骤1: 使用请求传入的凭证获取 access_token
            token_url = f"{VEHICLE_API_BASE_URL}/oauth/token"
            token_data = {
                "grant_type": "client_credentials",
                "client_id": request.client_id,
                "client_secret": request.client_secret,
            }
            token_response = await client.post(token_url, data=token_data)
            token_response.raise_for_status()
            token_json = token_response.json()

            if "error" in token_json or "access_token" not in token_json:
                error_desc = token_json.get("error_description", "Failed to get access token.")
                logger.error(f"Failed to get access_token: {error_desc}")
                return Restfulesponse(
                    status_code=401,
                    content={"code": 11231, "msg": error_desc, "data": None},
                    headers=headers
                )

            access_token = token_json["access_token"]

            # 步骤2: 使用请求传入的凭证及获取的token，获取车机参数
            parameter_url = f"{VEHICLE_API_BASE_URL}/vehicle-parameter/v1/get_parameter"
            parameter_headers = {
                "Content-Type": "application/json",
                "requestId": str(request_id),
                "companyId": request.company_id,
                "token": access_token,
                "clientId": request.client_id,
                "vin": request.vin,
            }
            parameter_body = {"vin": request.vin}

            param_response = await client.post(parameter_url, headers=parameter_headers, json=parameter_body)
            param_response.raise_for_status()
            param_json = param_response.json()
            
            if param_json.get("code") != 0:
                 logger.error(f"Error from vehicle parameter API: code={param_json.get('code')}, msg={param_json.get('msg')}")
            
            return Restfulesponse(content=param_json, headers=headers)

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error occurred: {e.response.status_code} - {e.response.text}")
            try:
                error_body = e.response.json()
                code = error_body.get("code", 11003)
                msg = error_body.get("msg") or error_body.get("error_description", "An unknown HTTP error occurred.")
            except json.JSONDecodeError:
                code = 11003
                msg = f"An unknown HTTP error occurred: Status {e.response.status_code}"
            return Restfulesponse(status_code=e.response.status_code, content={"code": code, "msg": msg, "data": None}, headers=headers)
        except Exception as e:
            logger.error(f"An unexpected error occurred: {e}")
            import traceback
            traceback.print_exc()
            return Restfulesponse(status_code=500, content={"code": 11003, "msg": "Internal server error", "data": None}, headers=headers)