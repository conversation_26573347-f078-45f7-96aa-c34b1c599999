code_both_empty_url_base64 = 1

code_intent_http_wrong = 101
code_invalid_intent = 102
code_intent_json_wrong = 103
code_fail_to_run_style_intent = 104
code_sensitive_query = 105
word_sensitive_query = "sensitive"

error_code_mem_http_error = 301
error_code_mem_return_error = 302

code_generation_error = 401
code_generation_inner_error = 402
code_generation_async_error = 403

code_color_http_error = 501
code_color_return_error = 502

class GenerationErrorException(Exception):
    def __init__(self, msg: str = "", code: int = 0, *args, **kwargs):
        Exception.__init__(self)
        self.msg = msg
        self.code = code