import json, re

def extract_json_str_from_text(text):
    if not text:
        return None
    # 匹配 ```json ... ``` 块
    match = re.search(r"```json\s*(\{.*?\}|\[.*?\])\s*```", text, re.DOTALL)

    if match:
        json_str = match.group(1)
        return json_str
    else:
        match = re.search(r"\s*(\{.*?\}|\[.*?\])\s*", text, re.DOTALL)
        if match:
            json_str = match.group(1)
            return json_str
    print("ERROR:没有找到 json 块", text)
    return None