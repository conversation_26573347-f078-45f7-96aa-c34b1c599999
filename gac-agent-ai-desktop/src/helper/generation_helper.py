from openai import BaseModel
from typing_extensions import Optional
from src.util.key_util import generate_token
from loguru import logger
import time, threading, aiohttp
import configuration

EXPIRE_DURATION=200

ak = configuration.config["sense_model_key"]["ak"]
sk = configuration.config["sense_model_key"]["sk"]

class GenerationResult(BaseModel):
    start_time: float
    task_id: str
    running: bool = True
    error: bool = False
    data: Optional[list] = None

    def is_expired(self) -> bool:
        return time.perf_counter() > (self.start_time + EXPIRE_DURATION)

# 创建锁
lock = threading.Lock()

global_result_map: dict[str, GenerationResult] = {}

to_query_task_ids: list[str] = []




class GenerationHelper:
    def __init__(self):
        self.name = ""

    def add_task_id(self, task_id: Optional[str] = None):
        print(f"add_task_id() task_id: {task_id}")
        if task_id:
            with lock:
                to_query_task_ids.append(task_id)
                item = GenerationResult(start_time=time.perf_counter(), task_id = task_id)
                global_result_map[task_id] = item

    def require_result(self, task_id):
        print(f"require_result() task_id: {task_id}")
        with lock:
            item = global_result_map.get(task_id)
        return item


    async def trigger_query_result(self):

        with lock:
            task_ids = to_query_task_ids.copy()
        task_ids_length = len(task_ids)
        print(f"trigger_query_result()  task_ids_length: {task_ids_length}")
        if 0 >= task_ids_length:
            return
        access_token = generate_token(ak, sk, 60)
        headers = {'Authorization': 'Bearer ' + access_token}
        url = "https://mhapi.sensetime.com/v1/stylization/results"
        if 49 < len(task_ids):
            task_ids = task_ids[:49]
        body = {
            "task_ids": task_ids
        }
        async with aiohttp.ClientSession() as session:
            async with session.post(url=url, headers=headers, json=body) as response:
                if response.status == 200:
                    response_json = await response.json()
                    result_list = response_json.get("results", [])
                    for result in result_list:
                        if isinstance(result, dict):
                            task_id = result.get("task_id", "")
                            if "SUCCESS" == result.get("state", ""):
                                data = result.get("images")
                                try:
                                    with lock:
                                        item = global_result_map.get(task_id)
                                        if item:
                                            item.running = False
                                            item.data = data
                                except:
                                    pass
                                try:
                                    with lock:
                                        to_query_task_ids.remove(task_id)
                                except:
                                    pass
                            elif "FAILED" == result.get("state", ""):
                                try:
                                    with lock:
                                        item = global_result_map.get(task_id)
                                        if item:
                                            item.running = False
                                            item.error = True
                                except:
                                    pass
                                try:
                                    with lock:
                                        to_query_task_ids.remove(task_id)
                                except:
                                    pass

                else:
                    logger.error(f"{response}")

    def check_expired(self):
        count = 0
        try:
            with lock:
                temp = global_result_map.copy()
            for task_id, value in temp.items():
                if value.is_expired():
                    count = count + 1
                    with lock:
                        global_result_map.pop(task_id)
                        try:
                            to_query_task_ids.remove(task_id)
                        except:
                            pass
        except Exception as e:
            print(e)
        print(f"check_expired   count: {count}")


generation_helper = GenerationHelper()