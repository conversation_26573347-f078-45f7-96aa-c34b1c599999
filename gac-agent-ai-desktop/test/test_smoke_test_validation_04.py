#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序04：验证 GAC Agent AI Desktop 冒烟测试脚本
测试冒烟测试脚本的基本功能和结构
"""

import sys
import os
import time
import json
from unittest.mock import Mock, patch, MagicMock

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_smoke_test_import():
    """测试冒烟测试脚本是否能正确导入"""
    print("=" * 70)
    print("🧪 测试程序04：验证 GAC Agent AI Desktop 冒烟测试脚本")
    print("=" * 70)
    
    try:
        from smoke_test import GacAgentAiDesktopSmokeTest
        print("✅ 冒烟测试脚本导入成功")
        return True, GacAgentAiDesktopSmokeTest
    except ImportError as e:
        print(f"❌ 冒烟测试脚本导入失败: {e}")
        return False, None
    except Exception as e:
        print(f"❌ 导入过程中出现异常: {e}")
        return False, None

def test_smoke_test_initialization():
    """测试冒烟测试类的初始化"""
    print("\n🔧 测试冒烟测试类初始化...")
    
    try:
        from smoke_test import GacAgentAiDesktopSmokeTest
        
        # 测试默认初始化
        smoke_test = GacAgentAiDesktopSmokeTest()
        assert smoke_test.base_url == "http://127.0.0.1:8080"
        assert smoke_test.health_url == "http://127.0.0.1:8080/gac-agent-desktop/v1/health"
        assert smoke_test.ai_desktop_url == "http://127.0.0.1:8080/gac-agent-desktop/v1/ai_desktop"
        assert smoke_test.vehicle_param_url == "http://127.0.0.1:8080/gac-agent-desktop/v1/vehicle_parameter"
        
        # 测试自定义URL初始化
        custom_url = "http://test.example.com:9090"
        smoke_test_custom = GacAgentAiDesktopSmokeTest(custom_url)
        assert smoke_test_custom.base_url == custom_url
        assert smoke_test_custom.health_url == f"{custom_url}/gac-agent-desktop/v1/health"
        
        print("✅ 冒烟测试类初始化测试通过")
        return True
    except Exception as e:
        print(f"❌ 冒烟测试类初始化测试失败: {e}")
        return False

def test_payload_generation():
    """测试请求体生成方法"""
    print("\n🔧 测试请求体生成方法...")
    
    try:
        from smoke_test import GacAgentAiDesktopSmokeTest
        
        smoke_test = GacAgentAiDesktopSmokeTest()
        
        # 测试AI Desktop请求体生成
        ai_payload = smoke_test.get_standard_ai_desktop_payload("测试查询")
        assert "image_base64" in ai_payload
        assert "query" in ai_payload
        assert "output_size" in ai_payload
        assert "user_info" in ai_payload
        assert ai_payload["query"] == "测试查询"
        assert ai_payload["output_size"] == "1920x1080"
        
        # 测试车辆参数请求体生成
        vehicle_payload = smoke_test.get_standard_vehicle_param_payload("TEST123")
        assert "vin" in vehicle_payload
        assert vehicle_payload["vin"] == "TEST123"
        
        # 测试图片base64生成
        image_base64 = smoke_test.get_sample_image_base64()
        assert image_base64.startswith("data:image/")
        assert "base64," in image_base64
        
        print("✅ 请求体生成方法测试通过")
        return True
    except Exception as e:
        print(f"❌ 请求体生成方法测试失败: {e}")
        return False

def test_log_functionality():
    """测试日志记录功能"""
    print("\n🔧 测试日志记录功能...")
    
    try:
        from smoke_test import GacAgentAiDesktopSmokeTest
        
        smoke_test = GacAgentAiDesktopSmokeTest()
        
        # 测试成功日志记录
        smoke_test.log_test_result("测试用例1", True, "测试成功", 1.23)
        assert len(smoke_test.test_results) == 1
        assert smoke_test.test_results[0]["test_name"] == "测试用例1"
        assert smoke_test.test_results[0]["success"] == True
        assert smoke_test.test_results[0]["message"] == "测试成功"
        assert smoke_test.test_results[0]["duration"] == 1.23
        
        # 测试失败日志记录
        smoke_test.log_test_result("测试用例2", False, "测试失败", 2.45)
        assert len(smoke_test.test_results) == 2
        assert smoke_test.test_results[1]["success"] == False
        
        print("✅ 日志记录功能测试通过")
        return True
    except Exception as e:
        print(f"❌ 日志记录功能测试失败: {e}")
        return False

def test_mock_http_requests():
    """测试模拟HTTP请求的冒烟测试"""
    print("\n🔧 测试模拟HTTP请求...")
    
    try:
        from smoke_test import GacAgentAiDesktopSmokeTest
        
        smoke_test = GacAgentAiDesktopSmokeTest()
        
        # 模拟成功的健康检查响应
        with patch('requests.get') as mock_get:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"status": "ok", "version": "1.0.36"}
            mock_get.return_value = mock_response
            
            result = smoke_test.test_health_endpoint()
            assert result == True
            mock_get.assert_called_once()
        
        # 模拟成功的AI Desktop响应
        with patch('requests.post') as mock_post:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"result": "success", "image_url": "test.jpg"}
            mock_post.return_value = mock_response
            
            result = smoke_test.test_ai_desktop_basic_request()
            assert result == True
            mock_post.assert_called_once()
        
        print("✅ 模拟HTTP请求测试通过")
        return True
    except Exception as e:
        print(f"❌ 模拟HTTP请求测试失败: {e}")
        return False

def test_test_methods_exist():
    """测试所有必要的测试方法是否存在"""
    print("\n🔧 测试方法存在性检查...")
    
    try:
        from smoke_test import GacAgentAiDesktopSmokeTest
        
        smoke_test = GacAgentAiDesktopSmokeTest()
        
        required_methods = [
            'test_service_connectivity',
            'test_health_endpoint', 
            'test_ai_desktop_basic_request',
            'test_vehicle_parameter_request',
            'test_image_processing_functionality',
            'test_parameter_boundaries',
            'test_response_time_performance',
            'test_error_recovery',
            'run_smoke_tests'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if not hasattr(smoke_test, method_name):
                missing_methods.append(method_name)
            elif not callable(getattr(smoke_test, method_name)):
                missing_methods.append(f"{method_name} (不可调用)")
        
        if missing_methods:
            print(f"❌ 缺少以下方法: {', '.join(missing_methods)}")
            return False
        else:
            print(f"✅ 所有必要的测试方法都存在 ({len(required_methods)} 个)")
            return True
            
    except Exception as e:
        print(f"❌ 测试方法存在性检查失败: {e}")
        return False

def test_configuration_compatibility():
    """测试配置兼容性"""
    print("\n🔧 测试配置兼容性...")
    
    try:
        from smoke_test import GacAgentAiDesktopSmokeTest
        
        # 测试不同的URL格式
        test_urls = [
            "http://127.0.0.1:8080",
            "https://api.example.com",
            "http://localhost:9090",
            "https://test.domain.com:8443"
        ]
        
        for url in test_urls:
            smoke_test = GacAgentAiDesktopSmokeTest(url)
            assert smoke_test.base_url == url
            assert smoke_test.health_url.startswith(url)
            assert "/gac-agent-desktop/v1/health" in smoke_test.health_url
        
        print("✅ 配置兼容性测试通过")
        return True
    except Exception as e:
        print(f"❌ 配置兼容性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 测试程序04：验证 GAC Agent AI Desktop 冒烟测试脚本")
    
    tests = [
        ("脚本导入", test_smoke_test_import),
        ("类初始化", test_smoke_test_initialization),
        ("请求体生成", test_payload_generation),
        ("日志功能", test_log_functionality),
        ("HTTP请求模拟", test_mock_http_requests),
        ("测试方法存在性", test_test_methods_exist),
        ("配置兼容性", test_configuration_compatibility)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_name == "脚本导入":
                success, _ = test_func()
            else:
                success = test_func()
            
            if success:
                passed_tests += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
        
        print("-" * 50)
    
    # 总结
    success_rate = (passed_tests / total_tests) * 100
    print(f"\n📊 验证总结:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   失败测试: {total_tests - passed_tests}")
    print(f"   成功率: {success_rate:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 GAC Agent AI Desktop 冒烟测试脚本验证全部通过！")
        print("   冒烟测试脚本已准备就绪，可以用于CI/CD流程")
        return True
    elif passed_tests >= total_tests * 0.8:
        print("\n✅ 大部分验证通过，冒烟测试脚本基本可用")
        return True
    else:
        print("\n⚠️  多项验证失败，请检查冒烟测试脚本")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
