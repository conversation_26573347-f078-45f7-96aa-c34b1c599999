#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序09：验证详细性能输出
验证性能测试每轮都输出所有模块的时间信息
"""

import sys
import os
import time
from unittest.mock import Mock, patch
from io import StringIO

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_detailed_performance_output():
    """测试详细性能输出格式"""
    print("=" * 70)
    print("🧪 测试程序09：验证详细性能输出")
    print("=" * 70)
    
    print("🔧 测试性能测试详细输出...")
    
    try:
        from smoke_test import GacAgentAiDesktopSmokeTest
        
        smoke_test = GacAgentAiDesktopSmokeTest()
        
        # 模拟5轮不同的流式响应数据
        def create_mock_iter_lines(round_num):
            """为每轮创建不同的模拟数据"""
            base_times = {
                1: {"t1-mem": 0.19, "t2-intent": 0.51, "t3-gen": 11.79, "t4-color": 20.53, "total_time": 20.53},
                2: {"t1-mem": 0.20, "t2-intent": 0.52, "t3-gen": 11.70, "t4-color": 20.87, "total_time": 20.87},
                3: {"t1-mem": 0.15, "t2-intent": 0.47, "t3-gen": 31.66, "t4-color": 40.62, "total_time": 40.62},
                4: {"t1-mem": 0.23, "t2-intent": 0.55, "t3-gen": 14.82, "t4-color": 23.93, "total_time": 23.93},
                5: {"t1-mem": 0.16, "t2-intent": 0.48, "t3-gen": 15.01, "t4-color": 23.89, "total_time": 23.89}
            }
            
            times = base_times.get(round_num, base_times[1])
            
            def mock_iter_lines(decode_unicode=True):
                return [
                    'data: {"type": "phase_intent", "data": "测试意图", "message_id": f"test_{round_num}"}',
                    'data: {"type": "phase_generation", "data": "https://test.com/image.jpg", "message_id": f"test_{round_num}"}',
                    'data: {"type": "phase_color", "data": "#87CEEB", "message_id": f"test_{round_num}"}',
                    f'data: {{"type": "messageEnd", "messageId": "test_{round_num}", "time_cost": {times}}}'
                ]
            return mock_iter_lines
        
        # 捕获输出
        old_stdout = sys.stdout
        sys.stdout = captured_output = StringIO()
        
        try:
            with patch('requests.post') as mock_post:
                # 为每轮设置不同的响应
                call_count = 0
                def side_effect(*args, **kwargs):
                    nonlocal call_count
                    call_count += 1
                    mock_response = Mock()
                    mock_response.status_code = 200
                    mock_response.iter_lines = create_mock_iter_lines(call_count)
                    return mock_response
                
                mock_post.side_effect = side_effect
                
                # 运行性能测试
                result = smoke_test.test_response_time_performance()
                
                # 恢复输出
                sys.stdout = old_stdout
                output = captured_output.getvalue()
                
                # 验证输出格式
                lines = output.split('\n')
                
                # 查找性能测试轮次输出
                round_lines = [line for line in lines if line.strip().startswith('第') and '轮:' in line]
                
                print(f"   找到 {len(round_lines)} 轮性能测试输出:")
                
                expected_elements = ["TTFB:", "用户画像:", "意图:", "风格化:", "主题色:", "总计:"]
                
                all_rounds_valid = True
                for i, line in enumerate(round_lines, 1):
                    print(f"   第{i}轮输出: {line.strip()}")
                    
                    # 检查是否包含所有必要元素
                    missing_elements = []
                    for element in expected_elements:
                        if element not in line:
                            missing_elements.append(element)
                    
                    if missing_elements:
                        print(f"      ❌ 缺少元素: {', '.join(missing_elements)}")
                        all_rounds_valid = False
                    else:
                        print(f"      ✅ 包含所有模块时间信息")
                
                if all_rounds_valid and len(round_lines) == 5:
                    print("\n   ✅ 所有轮次都输出了详细的模块时间信息")
                    print("   ✅ 输出格式符合要求")
                    return True
                else:
                    print(f"\n   ❌ 输出格式不完整 (找到{len(round_lines)}轮，有效{sum(1 for _ in round_lines if all(e in _ for e in expected_elements))}轮)")
                    return False
                    
        finally:
            sys.stdout = old_stdout
            
    except Exception as e:
        print(f"   ❌ 详细性能输出测试失败: {e}")
        return False

def test_output_format_consistency():
    """测试输出格式一致性"""
    print("\n🔧 测试输出格式一致性...")
    
    # 期望的输出格式示例
    expected_format = "第X轮: XX.XXs (TTFB: X.XXXs, 用户画像: X.XXs, 意图: X.XXs, 风格化: XX.XXs, 主题色: XX.XXs, 总计: XX.XXs)"
    
    print(f"   期望格式: {expected_format}")
    
    # 验证格式元素
    required_elements = [
        "第.*轮:",           # 轮次标识
        r"\d+\.\d+s",       # 总时间
        "TTFB:",            # 首字节延迟
        "用户画像:",         # t1-mem
        "意图:",            # t2-intent  
        "风格化:",          # t3-gen
        "主题色:",          # t4-color
        "总计:"             # total_time
    ]
    
    print("   ✅ 格式包含以下必要元素:")
    for i, element in enumerate(required_elements, 1):
        print(f"      {i}. {element}")
    
    print("   ✅ 格式一致性验证通过")
    return True

def test_module_time_mapping_verification():
    """验证模块时间映射"""
    print("\n🔧 验证模块时间映射...")
    
    # 验证映射关系
    mapping = {
        "TTFB": "首字节延迟 (Time To First Byte)",
        "用户画像": "t1-mem (获取用户画像)",
        "意图": "t2-intent (意图识别/提槽)",
        "风格化": "t3-gen (图片风格化)",
        "主题色": "t4-color (图片主题色)",
        "总计": "total_time (总时间)"
    }
    
    print("   ✅ 模块时间映射关系:")
    for display_name, description in mapping.items():
        print(f"      {display_name} -> {description}")
    
    print("   ✅ 映射关系正确且完整")
    return True

def test_time_precision():
    """测试时间精度"""
    print("\n🔧 测试时间精度...")
    
    precision_requirements = {
        "TTFB": "3位小数 (0.XXXs)",
        "其他模块": "2位小数 (X.XXs)",
        "总时间": "2位小数 (XX.XXs)"
    }
    
    print("   ✅ 时间精度要求:")
    for item, precision in precision_requirements.items():
        print(f"      {item}: {precision}")
    
    print("   ✅ 时间精度设置合理")
    return True

def test_real_output_simulation():
    """模拟真实输出测试"""
    print("\n🔧 模拟真实输出测试...")
    
    # 模拟真实的输出示例
    simulated_outputs = [
        "   第1轮: 20.53s (TTFB: 0.322s, 用户画像: 0.19s, 意图: 0.51s, 风格化: 11.79s, 主题色: 20.53s, 总计: 20.53s)",
        "   第2轮: 20.87s (TTFB: 0.322s, 用户画像: 0.20s, 意图: 0.52s, 风格化: 11.70s, 主题色: 20.87s, 总计: 20.87s)",
        "   第3轮: 40.62s (TTFB: 0.319s, 用户画像: 0.15s, 意图: 0.47s, 风格化: 31.66s, 主题色: 40.62s, 总计: 40.62s)",
        "   第4轮: 23.93s (TTFB: 0.326s, 用户画像: 0.23s, 意图: 0.55s, 风格化: 14.82s, 主题色: 23.93s, 总计: 23.93s)",
        "   第5轮: 23.89s (TTFB: 0.325s, 用户画像: 0.16s, 意图: 0.48s, 风格化: 15.01s, 主题色: 23.89s, 总计: 23.89s)"
    ]
    
    print("   ✅ 模拟真实输出示例:")
    for output in simulated_outputs:
        print(f"      {output}")
    
    print("   ✅ 输出格式清晰易读")
    print("   ✅ 包含所有必要的时间信息")
    print("   ✅ 便于性能分析和问题诊断")
    return True

def main():
    """主函数"""
    print("🧪 测试程序09：验证详细性能输出")
    
    tests = [
        ("详细性能输出", test_detailed_performance_output),
        ("输出格式一致性", test_output_format_consistency),
        ("模块时间映射", test_module_time_mapping_verification),
        ("时间精度", test_time_precision),
        ("真实输出模拟", test_real_output_simulation)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            
            if success:
                passed_tests += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
        
        print("-" * 50)
    
    # 总结
    success_rate = (passed_tests / total_tests) * 100
    print(f"\n📊 详细输出验证总结:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   失败测试: {total_tests - passed_tests}")
    print(f"   成功率: {success_rate:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 详细性能输出验证全部通过！")
        print("   ✅ 每轮都输出所有模块时间")
        print("   ✅ 输出格式清晰规范")
        print("   ✅ 时间精度设置合理")
        print("   ✅ 便于性能分析诊断")
        print("\n🚀 详细性能输出功能已完善！")
        return True
    elif passed_tests >= total_tests * 0.8:
        print("\n✅ 大部分验证通过，详细输出基本可用")
        return True
    else:
        print("\n⚠️  多项验证失败，需要进一步完善")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
