# GAC Agent AI Desktop 冒烟测试

## 概述

本目录包含 GAC Agent AI Desktop 服务的冒烟测试脚本，用于验证服务的基本功能和健康状态。

## 文件说明

- `smoke_test.py` - 主要的冒烟测试脚本
- `curl.sh` - 手动测试脚本（使用curl命令）
- `desktop.txt` - 测试请求体数据
- `test_smoke_test_validation_04.py` - 冒烟测试脚本的验证程序
- `README.md` - 本说明文档

## 测试内容

冒烟测试包含以下8个测试用例：

1. **服务连通性测试** - 验证服务是否可访问
2. **健康检查测试** - 验证 `/gac-agent-desktop/v1/health` 端点
3. **AI Desktop基础请求测试** - 验证主要的AI Desktop功能
4. **车辆参数接口测试** - 验证车辆参数查询功能
5. **图像处理功能测试** - 测试不同类型的查询请求
6. **参数边界测试** - 测试各种边界条件和参数组合
7. **性能测试** - 执行5轮性能测试，统计响应时间
8. **错误恢复测试** - 验证服务的错误恢复能力

## 使用方法

### 本地运行

```bash
# 进入测试目录
cd gac-agent-ai-desktop/test

# 安装依赖
pip install requests

# 运行冒烟测试（默认测试本地服务）
python smoke_test.py

# 测试指定服务地址
python smoke_test.py http://your-service-url:8080
```

### 在CI/CD中运行

冒烟测试已集成到GitLab CI/CD流程中，会在以下情况自动执行：

- 代码推送到仓库时
- 创建合并请求时
- 手动触发流水线时

CI配置位于项目根目录的 `.gitlab-ci.yml` 文件中。

## 测试配置

### 服务端点

- 基础URL: `http://127.0.0.1:8080` (默认)
- 健康检查: `/gac-agent-desktop/v1/health`
- AI Desktop: `/gac-agent-desktop/v1/ai_desktop`
- 车辆参数: `/gac-agent-desktop/v1/vehicle_parameter`

### 超时设置

- 默认超时时间: 300秒
- 可在脚本中修改 `time_out_time` 变量

### 测试数据

- 使用简化的1x1像素PNG图片作为测试图像
- 包含多种测试查询类型（导航、空调、音乐等）
- 测试不同的参数组合和边界条件

## 输出格式

测试结果以以下格式输出：

```
✅ PASS | 测试名称 | 测试消息 | 耗时
❌ FAIL | 测试名称 | 错误消息 | 耗时
```

最终会显示测试总结：

```
📊 测试总结:
   总测试数: 8
   通过测试: 7
   失败测试: 1
   成功率: 87.5%
```

## 性能基准

- 平均响应时间应 < 30秒
- 成功率应 > 60%
- 健康检查响应时间应 < 5秒

## 故障排除

### 常见问题

1. **连接失败**
   - 检查服务是否启动
   - 验证端口和URL是否正确
   - 检查网络连接

2. **超时错误**
   - 增加超时时间设置
   - 检查服务性能
   - 验证依赖服务状态

3. **认证错误**
   - 检查API密钥配置
   - 验证用户权限设置

### 调试模式

可以修改脚本中的日志级别来获取更详细的调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 扩展测试

如需添加新的测试用例：

1. 在 `GacAgentAiDesktopSmokeTest` 类中添加新的测试方法
2. 方法名以 `test_` 开头
3. 返回布尔值表示测试结果
4. 在 `run_smoke_tests` 方法的 `tests` 列表中添加新方法

示例：

```python
def test_new_feature(self) -> bool:
    """测试新功能"""
    test_name = "新功能测试"
    start_time = time.time()
    
    try:
        # 测试逻辑
        result = True
        duration = time.time() - start_time
        self.log_test_result(test_name, result, "测试成功", duration)
        return result
    except Exception as e:
        duration = time.time() - start_time
        self.log_test_result(test_name, False, f"测试失败: {str(e)}", duration)
        return False
```

## 维护说明

- 定期更新测试数据和用例
- 根据服务变更调整端点URL
- 监控测试性能基准的变化
- 及时修复失效的测试用例
