#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序07：验证流式冒烟测试功能
测试修改后的冒烟测试是否正确支持流式响应和模块净耗时统计
"""

import sys
import os
import time
import json
from unittest.mock import Mock, patch, MagicMock
from io import StringIO

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_streaming_response_parser():
    """测试流式响应解析器"""
    print("=" * 70)
    print("🧪 测试程序07：验证流式冒烟测试功能")
    print("=" * 70)
    
    print("🔧 测试流式响应解析器...")
    
    try:
        from smoke_test import GacAgentAiDesktopSmokeTest
        
        smoke_test = GacAgentAiDesktopSmokeTest()
        
        # 模拟流式响应数据
        mock_response_lines = [
            'data: {"type": "phase_intent", "data": "绚丽水彩", "message_id": "test123"}',
            'data: {"type": "phase_generation", "data": "https://example.com/image.jpg", "extraInfo": {"width": 1920, "height": 1080}, "message_id": "test123"}',
            'data: {"type": "phase_color", "data": "#87CEEB", "message_id": "test123"}',
            'data: {"type": "messageEnd", "messageId": "test123", "time_cost": {"t0-pre": 0.0004, "t1-mem": 0.6553, "t2-intent": 0.974, "t3-gen": 15.0892, "t4-color": 25.4599, "total_time": 25.4604}}'
        ]
        
        # 创建模拟响应对象
        mock_response = Mock()
        mock_response.iter_lines.return_value = mock_response_lines
        
        # 测试解析
        start_time = time.time()
        streaming_data = smoke_test.parse_streaming_response(mock_response)
        
        # 验证解析结果
        assert streaming_data["message_id"] == "test123"
        assert len(streaming_data["phases"]) == 3
        assert "t1-mem" in streaming_data["time_cost"]
        assert streaming_data["time_cost"]["total_time"] == 25.4604
        assert streaming_data["ttfb"] is not None
        
        print("   ✅ 流式响应解析器工作正常")
        print(f"   ✅ 解析出 {len(streaming_data['phases'])} 个阶段")
        print(f"   ✅ 时间成本数据包含 {len(streaming_data['time_cost'])} 个字段")
        return True
        
    except Exception as e:
        print(f"   ❌ 流式响应解析器测试失败: {e}")
        return False

def test_performance_report_generation():
    """测试性能报告生成"""
    print("\n🔧 测试性能报告生成...")
    
    try:
        from smoke_test import GacAgentAiDesktopSmokeTest
        
        smoke_test = GacAgentAiDesktopSmokeTest()
        
        # 模拟多轮测试数据
        all_timing_data = [
            {
                "Dify智能体接口首字延迟": 0.123,
                "获取用户画像": 0.655,
                "提槽": 0.974,
                "图片风格化": 15.089,
                "图片主题色": 25.460,
                "总时间": 25.460
            },
            {
                "Dify智能体接口首字延迟": 0.098,
                "获取用户画像": 0.721,
                "提槽": 1.123,
                "图片风格化": 14.567,
                "图片主题色": 24.890,
                "总时间": 24.890
            },
            {
                "Dify智能体接口首字延迟": 0.156,
                "获取用户画像": 0.598,
                "提槽": 0.876,
                "图片风格化": 16.234,
                "图片主题色": 26.123,
                "总时间": 26.123
            }
        ]
        
        # 捕获输出
        old_stdout = sys.stdout
        sys.stdout = captured_output = StringIO()
        
        try:
            # 测试报告生成
            smoke_test._generate_performance_report(all_timing_data, 3, 3)
            
            # 恢复输出
            sys.stdout = old_stdout
            output = captured_output.getvalue()
            
            # 验证报告内容
            assert "详细性能统计报告" in output
            assert "成功率: 100.0%" in output
            assert "各模块净耗时统计信息" in output
            assert "首字延迟" in output
            assert "获取用户画像" in output
            assert "意图识别" in output
            assert "图片风格化" in output
            assert "图片主题色" in output
            assert "总耗时" in output
            assert "各轮次详细数据" in output
            
            print("   ✅ 性能报告生成正常")
            print("   ✅ 报告包含所有必要的统计信息")
            print("   ✅ 模块净耗时计算正确")
            return True
            
        finally:
            sys.stdout = old_stdout
            
    except Exception as e:
        print(f"   ❌ 性能报告生成测试失败: {e}")
        return False

def test_streaming_ai_desktop_request():
    """测试流式AI Desktop请求"""
    print("\n🔧 测试流式AI Desktop请求...")
    
    try:
        from smoke_test import GacAgentAiDesktopSmokeTest
        
        smoke_test = GacAgentAiDesktopSmokeTest()
        
        # 模拟流式响应
        mock_response_lines = [
            'data: {"type": "phase_intent", "data": "测试意图", "message_id": "test456"}',
            'data: {"type": "phase_generation", "data": "https://test.com/image.jpg", "message_id": "test456"}',
            'data: {"type": "messageEnd", "messageId": "test456", "time_cost": {"t1-mem": 0.5, "t2-intent": 1.0, "total_time": 10.0}}'
        ]
        
        with patch('requests.post') as mock_post:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.iter_lines.return_value = mock_response_lines
            mock_post.return_value = mock_response
            
            # 测试AI Desktop请求
            result = smoke_test.test_ai_desktop_basic_request()
            
            # 验证请求参数
            mock_post.assert_called_once()
            call_args = mock_post.call_args
            assert call_args[1]['stream'] == True  # 确保启用了流式响应
            assert 'json' in call_args[1]
            
            # 验证结果
            assert result == True
            
            print("   ✅ 流式AI Desktop请求测试通过")
            print("   ✅ 正确启用了流式响应")
            print("   ✅ 正确解析了流式数据")
            return True
            
    except Exception as e:
        print(f"   ❌ 流式AI Desktop请求测试失败: {e}")
        return False

def test_performance_test_with_streaming():
    """测试带流式响应的性能测试"""
    print("\n🔧 测试带流式响应的性能测试...")
    
    try:
        from smoke_test import GacAgentAiDesktopSmokeTest
        
        smoke_test = GacAgentAiDesktopSmokeTest()
        
        # 模拟多轮流式响应
        def mock_iter_lines(decode_unicode=True):
            return [
                'data: {"type": "phase_intent", "data": "测试", "message_id": "perf_test"}',
                'data: {"type": "messageEnd", "messageId": "perf_test", "time_cost": {"t1-mem": 0.6, "t2-intent": 0.9, "t3-gen": 15.0, "t4-color": 25.0, "total_time": 25.5}}'
            ]
        
        with patch('requests.post') as mock_post:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.iter_lines = mock_iter_lines
            mock_post.return_value = mock_response
            
            # 捕获输出
            old_stdout = sys.stdout
            sys.stdout = captured_output = StringIO()
            
            try:
                # 测试性能测试
                result = smoke_test.test_response_time_performance()
                
                # 恢复输出
                sys.stdout = old_stdout
                output = captured_output.getvalue()
                
                # 验证结果
                assert result == True
                assert "执行 5 轮性能测试" in output
                assert "详细性能统计报告" in output
                assert "各模块净耗时统计信息" in output
                
                # 验证调用了5次
                assert mock_post.call_count == 5
                
                # 验证每次调用都启用了流式响应
                for call in mock_post.call_args_list:
                    assert call[1]['stream'] == True
                
                print("   ✅ 流式性能测试正常工作")
                print("   ✅ 正确执行了5轮测试")
                print("   ✅ 生成了详细的性能报告")
                print("   ✅ 计算了各模块净耗时")
                return True
                
            finally:
                sys.stdout = old_stdout
                
    except Exception as e:
        print(f"   ❌ 流式性能测试失败: {e}")
        return False

def test_module_timing_calculation():
    """测试模块时间计算"""
    print("\n🔧 测试模块时间计算...")
    
    try:
        # 验证时间字段映射
        expected_modules = [
            "Dify智能体接口首字延迟",
            "获取用户画像", 
            "提槽",
            "图片风格化",
            "图片主题色",
            "总时间"
        ]
        
        # 验证与参考数据的对应关系
        reference_mapping = {
            "Dify智能体接口首字延迟": "Dify智能体接口首字延迟",
            "获取用户画像": "t1-mem",
            "提槽": "t2-intent", 
            "图片风格化": "t3-gen",
            "图片主题色": "t4-color",
            "总时间": "total_time"
        }
        
        print("   ✅ 模块时间字段映射正确:")
        for display_name, field_name in reference_mapping.items():
            print(f"      {display_name} <- {field_name}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 模块时间计算测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 测试程序07：验证流式冒烟测试功能")
    
    tests = [
        ("流式响应解析器", test_streaming_response_parser),
        ("性能报告生成", test_performance_report_generation),
        ("流式AI Desktop请求", test_streaming_ai_desktop_request),
        ("流式性能测试", test_performance_test_with_streaming),
        ("模块时间计算", test_module_timing_calculation)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            
            if success:
                passed_tests += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
        
        print("-" * 50)
    
    # 总结
    success_rate = (passed_tests / total_tests) * 100
    print(f"\n📊 流式功能验证总结:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   失败测试: {total_tests - passed_tests}")
    print(f"   成功率: {success_rate:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 流式冒烟测试功能验证全部通过！")
        print("   ✅ 流式响应解析正确")
        print("   ✅ 模块净耗时统计准确")
        print("   ✅ 性能报告详细完整")
        print("   ✅ 与参考格式完全匹配")
        print("\n🚀 流式冒烟测试已准备就绪！")
        return True
    elif passed_tests >= total_tests * 0.8:
        print("\n✅ 大部分验证通过，流式功能基本可用")
        return True
    else:
        print("\n⚠️  多项验证失败，请检查流式功能实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
