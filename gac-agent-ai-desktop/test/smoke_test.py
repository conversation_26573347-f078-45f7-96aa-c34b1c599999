#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GAC Agent AI Desktop 冒烟测试
测试服务地址: http://127.0.0.1:8080

测试内容：
1. 基础功能测试
2. 健康检查测试
3. AI Desktop 接口测试
4. 车辆参数接口测试
5. 图像处理功能测试
6. 参数边界测试
7. 性能测试（5轮）
8. 错误恢复测试
"""

import requests
import time
import json
import sys
import base64
from typing import List, Dict, Any
from statistics import mean, median

time_out_time = 300

class GacAgentAiDesktopSmokeTest:
    """GAC Agent AI Desktop 冒烟测试类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.health_url = f"{base_url}/gac-agent-desktop/v1/health"
        self.ai_desktop_url = f"{base_url}/gac-agent-desktop/v1/ai_desktop"
        self.vehicle_param_url = f"{base_url}/gac-agent-desktop/v1/vehicle_parameter"
        self.headers = {"Content-Type": "application/json"}
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, message: str, duration: float = 0):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "duration": duration,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {test_name} | {message} | {duration:.2f}s")

    def get_sample_image_base64(self) -> str:
        """获取示例图片的base64编码（简化的测试图片）"""
        # 这里使用一个简单的1x1像素的PNG图片的base64编码作为测试
        # 实际使用时应该使用真实的桌面截图
        #读取当前目录下的base64.txt
        with open("base64.txt", "r") as f:
            base64_str = f.read()
            print('base 64 data len:', len(base64_str))
            return base64_str

    def get_standard_ai_desktop_payload(self, query: str = "帮我打开音乐播放器") -> Dict[str, Any]:
        """获取标准AI Desktop请求体"""
        return {
            "image_base64": self.get_sample_image_base64(),
            "query": query,
            "output_size": "1920x1080",
            "user_info": {
                "car_id": "test_car_001",
                "user_id": "test_user_001",
                "category": ["entertainment", "navigation"]
            }
        }

    def get_standard_vehicle_param_payload(self, vin: str = "TEST123456789") -> Dict[str, Any]:
        """获取标准车辆参数请求体"""
        return {
            "vin": vin
        }

    def test_service_connectivity(self) -> bool:
        """测试1: 服务连通性测试"""
        test_name = "服务连通性测试"
        start_time = time.time()
        
        try:
            response = requests.get(self.base_url, timeout=time_out_time)
            duration = time.time() - start_time
            
            # FastAPI默认会返回404，但能连通说明服务正常
            if response.status_code in [200, 404]:
                self.log_test_result(test_name, True, f"服务连通正常 (状态码: {response.status_code})", duration)
                return True
            else:
                self.log_test_result(test_name, False, f"服务响应异常 (状态码: {response.status_code})", duration)
                return False
                
        except requests.exceptions.ConnectionError:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, "无法连接到服务", duration)
            return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"连接异常: {str(e)}", duration)
            return False

    def test_health_endpoint(self) -> bool:
        """测试2: 健康检查端点测试"""
        test_name = "健康检查端点测试"
        start_time = time.time()
        
        try:
            response = requests.get(self.health_url, timeout=time_out_time)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    health_data = response.json()
                    if health_data.get("status") == "ok":
                        version = health_data.get("version", "未知")
                        self.log_test_result(test_name, True, f"健康检查通过 (版本: {version})", duration)
                        return True
                    else:
                        self.log_test_result(test_name, False, f"健康状态异常: {health_data}", duration)
                        return False
                except json.JSONDecodeError:
                    self.log_test_result(test_name, False, "健康检查响应格式错误", duration)
                    return False
            else:
                self.log_test_result(test_name, False, f"健康检查失败 (状态码: {response.status_code})", duration)
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"健康检查异常: {str(e)}", duration)
            return False

    def test_ai_desktop_basic_request(self) -> bool:
        """测试3: AI Desktop基础请求测试"""
        test_name = "AI Desktop基础请求测试"
        start_time = time.time()

        payload = self.get_standard_ai_desktop_payload("帮我打开音乐播放器")

        try:
            response = requests.post(
                self.ai_desktop_url,
                headers=self.headers,
                json=payload,
                timeout=time_out_time
            )
            duration = time.time() - start_time

            if response.status_code == 200:
                try:
                    result = response.json()
                    # 检查响应是否包含预期字段
                    if "result" in result or "image_url" in result or "message" in result:
                        self.log_test_result(test_name, True, f"AI Desktop请求成功 (响应包含结果字段)", duration)
                        return True
                    else:
                        self.log_test_result(test_name, True, f"AI Desktop服务正常响应", duration)
                        return True
                except json.JSONDecodeError:
                    self.log_test_result(test_name, False, "AI Desktop响应格式错误", duration)
                    return False
            else:
                error_msg = response.text[:200] if response.text else "无错误信息"
                self.log_test_result(test_name, False, f"AI Desktop请求失败 (状态码: {response.status_code}) - {error_msg}", duration)
                return False

        except requests.exceptions.Timeout:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, "AI Desktop请求超时", duration)
            return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"AI Desktop请求异常: {str(e)}", duration)
            return False

    def test_vehicle_parameter_request(self) -> bool:
        """测试4: 车辆参数接口测试"""
        test_name = "车辆参数接口测试"
        start_time = time.time()

        payload = self.get_standard_vehicle_param_payload("TEST123456789")

        try:
            response = requests.post(
                self.vehicle_param_url,
                headers=self.headers,
                json=payload,
                timeout=time_out_time
            )
            duration = time.time() - start_time

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.log_test_result(test_name, True, f"车辆参数请求成功", duration)
                    return True
                except json.JSONDecodeError:
                    self.log_test_result(test_name, False, "车辆参数响应格式错误", duration)
                    return False
            else:
                error_msg = response.text[:200] if response.text else "无错误信息"
                self.log_test_result(test_name, False, f"车辆参数请求失败 (状态码: {response.status_code}) - {error_msg}", duration)
                return False

        except requests.exceptions.Timeout:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, "车辆参数请求超时", duration)
            return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"车辆参数请求异常: {str(e)}", duration)
            return False

    def test_image_processing_functionality(self) -> bool:
        """测试5: 图像处理功能测试"""
        test_name = "图像处理功能测试"
        start_time = time.time()

        # 测试不同的查询类型
        test_queries = [
            "帮我打开导航",
            "调节空调温度",
            "播放音乐",
            "显示天气信息"
        ]

        successful_requests = 0

        for query in test_queries:
            try:
                payload = self.get_standard_ai_desktop_payload(query)
                response = requests.post(
                    self.ai_desktop_url,
                    headers=self.headers,
                    json=payload,
                    timeout=time_out_time
                )

                if response.status_code == 200:
                    successful_requests += 1
                    print(f"   ✓ 查询 '{query}': 成功")
                else:
                    print(f"   ✗ 查询 '{query}': 失败 (状态码: {response.status_code})")

            except Exception as e:
                print(f"   ✗ 查询 '{query}': 异常 - {str(e)}")

        duration = time.time() - start_time

        if successful_requests >= len(test_queries) // 2:
            self.log_test_result(test_name, True, f"图像处理功能测试通过 ({successful_requests}/{len(test_queries)})", duration)
            return True
        else:
            self.log_test_result(test_name, False, f"图像处理功能测试失败 ({successful_requests}/{len(test_queries)})", duration)
            return False

    def test_parameter_boundaries(self) -> bool:
        """测试6: 参数边界值测试"""
        test_name = "参数边界值测试"
        start_time = time.time()

        test_cases = [
            {"query": "帮我", "desc": "最短查询"},
            {"query": "帮我打开音乐播放器，调节音量到适中，然后播放我喜欢的歌曲类型，最好是轻音乐或者古典音乐", "desc": "长查询"},
            {"output_size": "800x600", "desc": "小分辨率"},
            {"output_size": "3840x2160", "desc": "4K分辨率"},
            {"user_info": {"car_id": "", "user_id": "", "category": []}, "desc": "空用户信息"},
            {"user_info": {"car_id": "very_long_car_id_" * 10, "user_id": "very_long_user_id_" * 10, "category": ["cat1", "cat2", "cat3", "cat4", "cat5"]}, "desc": "长用户信息"}
        ]

        successful_cases = 0

        for case in test_cases:
            try:
                payload = self.get_standard_ai_desktop_payload("测试查询")
                payload.update({k: v for k, v in case.items() if k != "desc"})

                response = requests.post(
                    self.ai_desktop_url,
                    headers=self.headers,
                    json=payload,
                    timeout=time_out_time
                )

                if response.status_code == 200:
                    successful_cases += 1
                    print(f"   ✓ {case['desc']}: 成功")
                else:
                    print(f"   ✗ {case['desc']}: 失败 (状态码: {response.status_code})")

            except Exception as e:
                print(f"   ✗ {case['desc']}: 异常 - {str(e)}")

        duration = time.time() - start_time

        if successful_cases >= len(test_cases) // 2:
            self.log_test_result(test_name, True, f"边界测试通过 ({successful_cases}/{len(test_cases)})", duration)
            return True
        else:
            self.log_test_result(test_name, False, f"边界测试失败 ({successful_cases}/{len(test_cases)})", duration)
            return False

    def test_response_time_performance(self) -> bool:
        """测试7: 响应时间性能测试（5轮）"""
        test_name = "响应时间性能测试"
        start_time = time.time()

        # 多样化的AI Desktop查询
        test_queries = [
            "帮我打开导航",
            "调节空调温度到22度",
            "播放音乐",
            "显示天气信息",
            "打开车窗"
        ]

        response_times = []
        successful_requests = 0
        test_rounds = 5

        print(f"   执行 {test_rounds} 轮性能测试...")

        for i in range(test_rounds):
            query = test_queries[i % len(test_queries)]
            payload = self.get_standard_ai_desktop_payload(query)

            try:
                req_start = time.time()
                response = requests.post(
                    self.ai_desktop_url,
                    headers=self.headers,
                    json=payload,
                    timeout=time_out_time
                )

                if response.status_code == 200:
                    successful_requests += 1
                    req_duration = time.time() - req_start
                    response_times.append(req_duration)

                    # 尝试解析响应获取更多信息
                    try:
                        result = response.json()
                        result_info = f"响应包含 {len(str(result))} 字符"
                    except:
                        result_info = "响应解析失败"

                    print(f"   第{i+1}轮: {req_duration:.2f}s ({result_info})")
                else:
                    print(f"   第{i+1}轮: 失败 (状态码: {response.status_code})")

            except Exception as e:
                print(f"   第{i+1}轮: 异常 - {str(e)}")

        duration = time.time() - start_time

        # 生成性能统计报告
        if response_times:
            avg_time = mean(response_times)
            median_time = median(response_times)
            max_time = max(response_times)
            min_time = min(response_times)
            success_rate = successful_requests / test_rounds

            print(f"\n   📊 性能测试统计报告:")
            print(f"   ================================")
            print(f"   总体响应时间:")
            print(f"     平均: {avg_time:.2f}s, 中位数: {median_time:.2f}s")
            print(f"     最快: {min_time:.2f}s, 最慢: {max_time:.2f}s")
            print(f"     成功率: {success_rate:.1%}")

            # 性能标准：平均响应时间 < 30秒，成功率 > 60%
            performance_ok = avg_time < 30 and success_rate > 0.6

            message = f"平均:{avg_time:.2f}s 中位数:{median_time:.2f}s 最大:{max_time:.2f}s 最小:{min_time:.2f}s 成功率:{success_rate:.1%}"
            self.log_test_result(test_name, performance_ok, message, duration)
            return performance_ok
        else:
            self.log_test_result(test_name, False, "所有请求都失败", duration)
            return False

    def test_error_recovery(self) -> bool:
        """测试8: 错误恢复能力测试"""
        test_name = "错误恢复能力测试"
        start_time = time.time()

        # 先发送一个错误请求
        invalid_payload = {
            "image_base64": "invalid_base64_data",
            "query": "",  # 空查询
            "invalid_field": "test"
        }

        try:
            # 发送无效请求
            response = requests.post(
                self.ai_desktop_url,
                headers=self.headers,
                json=invalid_payload,
                timeout=time_out_time
            )
            print(f"   无效请求响应: {response.status_code}")
        except Exception as e:
            print(f"   无效请求异常: {str(e)}")

        # 短暂等待
        time.sleep(1)

        # 然后发送正常请求，测试服务是否能恢复
        valid_payload = self.get_standard_ai_desktop_payload("帮我打开音乐")

        recovery_successful = False

        try:
            response = requests.post(
                self.ai_desktop_url,
                headers=self.headers,
                json=valid_payload,
                timeout=time_out_time
            )

            if response.status_code == 200:
                recovery_successful = True
                print("   服务成功从错误中恢复")
            else:
                print(f"   恢复失败，状态码: {response.status_code}")

        except Exception as e:
            print(f"   恢复测试异常: {str(e)}")

        duration = time.time() - start_time

        message = "服务能够从错误中恢复" if recovery_successful else "服务无法从错误中恢复"
        self.log_test_result(test_name, recovery_successful, message, duration)
        return recovery_successful

    def run_smoke_tests(self) -> bool:
        """运行所有冒烟测试"""
        print("=" * 80)
        print("🧪 GAC Agent AI Desktop 冒烟测试")
        print(f"🎯 测试目标: {self.base_url}")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

        # 执行测试序列
        tests = [
            self.test_service_connectivity,
            self.test_health_endpoint,
            self.test_ai_desktop_basic_request,
            self.test_vehicle_parameter_request,
            self.test_image_processing_functionality,
            self.test_parameter_boundaries,
            self.test_response_time_performance,
            self.test_error_recovery
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_func in tests:
            if test_func():
                passed_tests += 1
            print("-" * 80)

        # 输出测试总结
        success_rate = (passed_tests / total_tests) * 100
        overall_success = passed_tests == total_tests

        print("📊 测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   成功率: {success_rate:.1f}%")

        if overall_success:
            print("🎉 冒烟测试全部通过！服务功能正常")
        elif passed_tests >= total_tests * 0.75:
            print("✅ 大部分冒烟测试通过，服务基本正常")
        else:
            print("⚠️  多项冒烟测试失败，请检查服务状态")

        print("=" * 80)
        return overall_success


def main():
    """主函数"""
    # 可以通过命令行参数指定服务地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"

    smoke_test = GacAgentAiDesktopSmokeTest(base_url)
    success = smoke_test.run_smoke_tests()

    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
