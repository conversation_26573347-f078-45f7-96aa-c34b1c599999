#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GAC Agent AI Desktop 冒烟测试
测试服务地址: http://127.0.0.1:8080

测试内容：
1. 基础功能测试
2. 健康检查测试
3. AI Desktop 接口测试
4. 图像处理功能测试
5. 参数边界测试
6. 性能测试（5轮）
7. 错误恢复测试
"""

import requests
import time
import json
import sys
import base64
import re
from typing import List, Dict, Any
from statistics import mean, median

time_out_time = 300

class GacAgentAiDesktopSmokeTest:
    """GAC Agent AI Desktop 冒烟测试类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.health_url = f"{base_url}/gac-agent-desktop/v1/health"
        self.ai_desktop_url = f"{base_url}/gac-agent-desktop/v1/ai_desktop"
        self.vehicle_param_url = f"{base_url}/gac-agent-desktop/v1/vehicle_parameter"
        self.headers = {"Content-Type": "application/json"}
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, message: str, duration: float = 0):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "duration": duration,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)

        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {test_name} | {message} | {duration:.2f}s")

    def parse_streaming_response(self, response):
        """解析流式响应数据"""
        streaming_data = {
            "phases": [],
            "time_cost": {},
            "message_id": None,
            "ttfb": None,  # Time to First Byte
            "total_duration": 0
        }

        start_time = time.time()
        first_byte_time = None

        try:
            for line in response.iter_lines(decode_unicode=True):
                if line and line.startswith('data: '):
                    # 记录首字节时间
                    if first_byte_time is None:
                        first_byte_time = time.time()
                        streaming_data["ttfb"] = first_byte_time - start_time

                    # 解析JSON数据
                    json_str = line[6:]  # 去掉 'data: ' 前缀
                    try:
                        data = json.loads(json_str)

                        # 记录消息ID
                        if "message_id" in data:
                            streaming_data["message_id"] = data["message_id"]

                        # 处理不同类型的数据
                        if data.get("type") == "phase_intent":
                            streaming_data["phases"].append({
                                "type": "intent",
                                "data": data.get("data"),
                                "timestamp": time.time() - start_time
                            })
                        elif data.get("type") == "phase_generation":
                            streaming_data["phases"].append({
                                "type": "generation",
                                "data": data.get("data"),
                                "extraInfo": data.get("extraInfo"),
                                "timestamp": time.time() - start_time
                            })
                        elif data.get("type") == "phase_color":
                            streaming_data["phases"].append({
                                "type": "color",
                                "data": data.get("data"),
                                "timestamp": time.time() - start_time
                            })
                        elif data.get("type") == "messageEnd":
                            streaming_data["time_cost"] = data.get("time_cost", {})
                            streaming_data["total_duration"] = time.time() - start_time
                            break

                    except json.JSONDecodeError:
                        continue

        except Exception as e:
            print(f"解析流式响应时出错: {e}")

        return streaming_data

    def get_sample_image_base64(self) -> str:
        """获取示例图片的base64编码（简化的测试图片）"""
        # 这里使用一个简单的1x1像素的PNG图片的base64编码作为测试
        # 实际使用时应该使用真实的桌面截图
        #读取当前目录下的base64.txt
        with open("base64.txt", "r") as f:
            base64_str = f.read()
            # print('base 64 data len:', len(base64_str))
            return base64_str

    def get_standard_ai_desktop_payload(self, query: str = "把眼前的风景做成水墨画的桌面") -> Dict[str, Any]:
        """获取标准AI Desktop请求体"""
        return {
            "image_base64": self.get_sample_image_base64(),
            "query": query,
            "is_async": True,
            "output_size": {
                "height": 1080,
                "width": 1920
            },
            "query": "把眼前的风景做成一张壁纸吧",
            "remove_water_mark": False,
            "stream": True,
            "user_info": {
                "car_id": "aidesktop09",
                "category": [
                    "wallpaper_style_preference"
                ],
                "user_id": "1"
            }
        }

    def get_standard_vehicle_param_payload(self, vin: str = "TEST123456789") -> Dict[str, Any]:
        """获取标准车辆参数请求体"""
        return {
            "vin": vin
        }

    def test_service_connectivity(self) -> bool:
        """测试1: 服务连通性测试"""
        test_name = "服务连通性测试"
        start_time = time.time()
        
        try:
            response = requests.get(self.base_url, timeout=time_out_time)
            duration = time.time() - start_time
            
            # FastAPI默认会返回404，但能连通说明服务正常
            if response.status_code in [200, 404]:
                self.log_test_result(test_name, True, f"服务连通正常 (状态码: {response.status_code})", duration)
                return True
            else:
                self.log_test_result(test_name, False, f"服务响应异常 (状态码: {response.status_code})", duration)
                return False
                
        except requests.exceptions.ConnectionError:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, "无法连接到服务", duration)
            return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"连接异常: {str(e)}", duration)
            return False

    def test_health_endpoint(self) -> bool:
        """测试2: 健康检查端点测试"""
        test_name = "健康检查端点测试"
        start_time = time.time()
        
        try:
            response = requests.get(self.health_url, timeout=time_out_time)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    health_data = response.json()
                    if health_data.get("status") == "ok":
                        version = health_data.get("version", "未知")
                        self.log_test_result(test_name, True, f"健康检查通过 (版本: {version})", duration)
                        return True
                    else:
                        self.log_test_result(test_name, False, f"健康状态异常: {health_data}", duration)
                        return False
                except json.JSONDecodeError:
                    self.log_test_result(test_name, False, "健康检查响应格式错误", duration)
                    return False
            else:
                self.log_test_result(test_name, False, f"健康检查失败 (状态码: {response.status_code})", duration)
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"健康检查异常: {str(e)}", duration)
            return False

    def test_ai_desktop_basic_request(self) -> bool:
        """测试3: AI Desktop基础请求测试（流式）"""
        test_name = "AI Desktop基础请求测试"
        start_time = time.time()

        payload = self.get_standard_ai_desktop_payload("帮我生成一张壁纸")

        try:
            response = requests.post(
                self.ai_desktop_url,
                headers=self.headers,
                json=payload,
                timeout=time_out_time,
                stream=True  # 启用流式响应
            )

            if response.status_code == 200:
                # 解析流式响应
                streaming_data = self.parse_streaming_response(response)
                duration = time.time() - start_time

                # 检查是否收到了完整的流式数据
                if streaming_data["time_cost"] and streaming_data["phases"]:
                    phase_count = len(streaming_data["phases"])
                    ttfb = streaming_data.get("ttfb", 0)
                    self.log_test_result(test_name, True,
                        f"AI Desktop流式请求成功 (阶段数: {phase_count}, TTFB: {ttfb:.3f}s)", duration)
                    return True
                elif streaming_data["phases"]:
                    # 至少收到了一些阶段数据
                    phase_count = len(streaming_data["phases"])
                    self.log_test_result(test_name, True,
                        f"AI Desktop部分响应成功 (阶段数: {phase_count})", duration)
                    return True
                else:
                    self.log_test_result(test_name, False, "AI Desktop未收到有效的流式数据", duration)
                    return False
            else:
                duration = time.time() - start_time
                error_msg = response.text[:200] if response.text else "无错误信息"
                self.log_test_result(test_name, False, f"AI Desktop请求失败 (状态码: {response.status_code}) - {error_msg}", duration)
                return False

        except requests.exceptions.Timeout:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, "AI Desktop请求超时", duration)
            return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"AI Desktop请求异常: {str(e)}", duration)
            return False


    def test_image_processing_functionality(self) -> bool:
        """测试5: 图像处理功能测试（流式）"""
        test_name = "图像处理功能测试"
        start_time = time.time()

        # 测试不同的查询类型
        test_queries = [
            "水墨画风格的壁纸",
            "中国风的壁纸"
        ]

        successful_requests = 0
        phase_details = []

        for query in test_queries:
            try:
                payload = self.get_standard_ai_desktop_payload(query)
                response = requests.post(
                    self.ai_desktop_url,
                    headers=self.headers,
                    json=payload,
                    timeout=time_out_time,
                    stream=True  # 启用流式响应
                )

                if response.status_code == 200:
                    # 解析流式响应
                    streaming_data = self.parse_streaming_response(response)

                    if streaming_data["phases"] and streaming_data["time_cost"]:
                        successful_requests += 1
                        phase_count = len(streaming_data["phases"])
                        total_time = streaming_data["time_cost"].get("total_time", 0)
                        phase_details.append(f"'{query}': {phase_count}阶段, {total_time:.2f}s")
                        print(f"   ✓ 查询 '{query}': 成功 (阶段数: {phase_count}, 总时间: {total_time:.2f}s)")
                    else:
                        print(f"   ✗ 查询 '{query}': 响应不完整")
                else:
                    print(f"   ✗ 查询 '{query}': 失败 (状态码: {response.status_code})")

            except Exception as e:
                print(f"   ✗ 查询 '{query}': 异常 - {str(e)}")

        duration = time.time() - start_time

        if successful_requests >= len(test_queries) // 2:
            details = "; ".join(phase_details) if phase_details else "无详细信息"
            self.log_test_result(test_name, True, f"图像处理功能测试通过 ({successful_requests}/{len(test_queries)}) - {details}", duration)
            return True
        else:
            self.log_test_result(test_name, False, f"图像处理功能测试失败 ({successful_requests}/{len(test_queries)})", duration)
            return False

    def test_parameter_boundaries(self) -> bool:
        """测试6: 参数边界值测试（流式）"""
        test_name = "参数边界值测试"
        start_time = time.time()

        test_cases = [
            {"query": "水墨画壁纸", "desc": "最短查询"},
            {"query": "把眼前的风景做动画的桌面", "desc": "长查询"},
       ]

        successful_cases = 0

        for case in test_cases:
            try:
                payload = self.get_standard_ai_desktop_payload(case["query"])

                response = requests.post(
                    self.ai_desktop_url,
                    headers=self.headers,
                    json=payload,
                    timeout=time_out_time,
                    stream=True  # 启用流式响应
                )

                if response.status_code == 200:
                    # 解析流式响应
                    streaming_data = self.parse_streaming_response(response)

                    if streaming_data["phases"]:
                        successful_cases += 1
                        phase_count = len(streaming_data["phases"])
                        print(f"   ✓ {case['desc']}: 成功 (阶段数: {phase_count})")
                    else:
                        print(f"   ✗ {case['desc']}: 响应不完整")
                else:
                    print(f"   ✗ {case['desc']}: 失败 (状态码: {response.status_code})")

            except Exception as e:
                print(f"   ✗ {case['desc']}: 异常 - {str(e)}")

        duration = time.time() - start_time

        if successful_cases >= len(test_cases) // 2:
            self.log_test_result(test_name, True, f"边界测试通过 ({successful_cases}/{len(test_cases)})", duration)
            return True
        else:
            self.log_test_result(test_name, False, f"边界测试失败 ({successful_cases}/{len(test_cases)})", duration)
            return False

    def test_response_time_performance(self) -> bool:
        """测试7: 响应时间性能测试（5轮）- 流式响应和模块净耗时统计"""
        test_name = "响应时间性能测试"
        start_time = time.time()

        # 多样化的AI Desktop查询
        test_queries = [
            "把眼前的风景做成水墨画的桌面",
            "把眼前的风景做成宫崎骏风的的桌面",
            "把眼前的风景做成油墨画的桌面",
            "把眼前的风景做成中国风的桌面",
            "把眼前的风景做成油画的桌面"
        ]

        # 存储所有轮次的时间数据
        all_timing_data = []
        successful_requests = 0
        test_rounds = 1

        print(f"   执行 {test_rounds} 轮性能测试...")

        for i in range(test_rounds):
            query = test_queries[i % len(test_queries)]
            payload = self.get_standard_ai_desktop_payload(query)

            try:
                req_start = time.time()
                response = requests.post(
                    self.ai_desktop_url,
                    headers=self.headers,
                    json=payload,
                    timeout=time_out_time,
                    stream=True  # 启用流式响应
                )

                if response.status_code == 200:
                    # 解析流式响应
                    streaming_data = self.parse_streaming_response(response)
                    req_duration = time.time() - req_start

                    if streaming_data["time_cost"]:
                        successful_requests += 1

                        # 提取时间成本数据
                        time_cost = streaming_data["time_cost"]
                        timing_info = {
                            "Dify智能体接口首字延迟": streaming_data.get("ttfb", 0),
                            "获取用户画像": time_cost.get("t1-mem", 0),
                            "提槽": time_cost.get("t2-intent", 0),
                            "图片风格化": time_cost.get("t3-gen", 0),
                            "图片主题色": time_cost.get("t4-color", 0),
                            "总时间": time_cost.get("total_time", req_duration)
                        }
                        all_timing_data.append(timing_info)

                        # 打印详细的模块时间信息
                        ttfb = streaming_data.get('ttfb', 0)
                        t1_mem = time_cost.get("t1-mem", 0)
                        t2_intent = time_cost.get("t2-intent", 0)
                        t3_gen = time_cost.get("t3-gen", 0)
                        t4_color = time_cost.get("t4-color", 0)
                        total_time = time_cost.get("total_time", req_duration)

                        print(f"   第{i+1}轮: {req_duration:.2f}s (获取用户画像: {t1_mem:.2f}s, 提槽: {t2_intent:.2f}s, 图片风格化: {t3_gen:.2f}s, 图片主题色: {t4_color:.2f}s, 总计: {total_time:.2f}s)")
                    else:
                        print(f"   第{i+1}轮: {req_duration:.2f}s (未获取到时间统计)")
                else:
                    print(f"   第{i+1}轮: 失败 (状态码: {response.status_code})")

            except Exception as e:
                print(f"   第{i+1}轮: 异常 - {str(e)}")

        duration = time.time() - start_time

        # 生成详细的性能统计报告
        if all_timing_data:
            self._generate_performance_report(all_timing_data, successful_requests, test_rounds)

            # 计算总体成功率
            success_rate = successful_requests / test_rounds

            # 性能标准：成功率 > 60%
            performance_ok = success_rate > 0.6

            avg_total_time = mean([data["总时间"] for data in all_timing_data])
            message = f"成功率:{success_rate:.1%} 平均总时间:{avg_total_time:.2f}s 数据轮次:{len(all_timing_data)}"
            self.log_test_result(test_name, performance_ok, message, duration)
            return performance_ok
        else:
            self.log_test_result(test_name, False, "所有请求都失败", duration)
            return False

    def _generate_performance_report(self, all_timing_data: List[Dict], successful_requests: int, test_rounds: int):
        """生成详细的性能统计报告"""
        print(f"\n   📊 详细性能统计报告:")
        print(f"   ================================")
        print(f"   成功请求数: {successful_requests}/{test_rounds}")
        print(f"   成功率: {successful_requests/test_rounds:.1%}")
        print("")

        # 计算各模块的统计数据
        module_stats = {}
        for timing_info in all_timing_data:
            for field, duration_val in timing_info.items():
                if field not in module_stats:
                    module_stats[field] = []
                module_stats[field].append(duration_val)

        # 显示各模块净耗时统计信息
        print("   各模块净耗时统计信息（单独耗时，非累加）:")
        module_descriptions = {
            "获取用户画像": "获取用户画像",
            "提槽": "提槽",
            "图片风格化": "图片风格化",
            "图片主题色": "图片主题色",
            "总时间": "总耗时"
        }

        for field, description in module_descriptions.items():
            if field in module_stats and module_stats[field]:
                durations = module_stats[field]
                avg_time = mean(durations)
                median_time = median(durations)
                min_time = min(durations)
                max_time = max(durations)
                len_margin = 16
                print(f"     {description}{' '*(len_margin-len(str(description)))}: 平均 {avg_time:>6.2f}s, 中位数 {median_time:>6.2f}s, 最快 {min_time:>6.2f}s, 最慢 {max_time:>6.2f}s")

        print("")

        # 显示每轮的详细数据
        print("   各轮次详细数据:")
        for i, timing_info in enumerate(all_timing_data, 1):
            print(f"     第{i}轮: 获取用户画像={timing_info.get('获取用户画像', 0):.2f}s, "
                  f"提槽={timing_info.get('提槽', 0):.2f}s, "
                  f"图片风格化={timing_info.get('图片风格化', 0):.2f}s, "
                  f"图片主题色={timing_info.get('图片主题色', 0):.2f}s, "
                  f"总时间={timing_info.get('总时间', 0):.2f}s")

    def test_error_recovery(self) -> bool:
        """测试8: 错误恢复能力测试"""
        test_name = "错误恢复能力测试"
        start_time = time.time()

        # 先发送一个错误请求
        invalid_payload = {
            "image_base64": "invalid_base64_data",
            "query": "",  # 空查询
            "invalid_field": "test"
        }

        try:
            # 发送无效请求
            response = requests.post(
                self.ai_desktop_url,
                headers=self.headers,
                json=invalid_payload,
                timeout=time_out_time
            )
            print(f"   无效请求响应: {response.status_code}")
        except Exception as e:
            print(f"   无效请求异常: {str(e)}")

        # 短暂等待
        time.sleep(1)

        # 然后发送正常请求，测试服务是否能恢复
        valid_payload = self.get_standard_ai_desktop_payload("帮我打开音乐")

        recovery_successful = False

        try:
            response = requests.post(
                self.ai_desktop_url,
                headers=self.headers,
                json=valid_payload,
                timeout=time_out_time
            )

            if response.status_code == 200:
                recovery_successful = True
                print("   服务成功从错误中恢复")
            else:
                print(f"   恢复失败，状态码: {response.status_code}")

        except Exception as e:
            print(f"   恢复测试异常: {str(e)}")

        duration = time.time() - start_time

        message = "服务能够从错误中恢复" if recovery_successful else "服务无法从错误中恢复"
        self.log_test_result(test_name, recovery_successful, message, duration)
        return recovery_successful

    def run_smoke_tests(self) -> bool:
        """运行所有冒烟测试"""
        print("=" * 80)
        print("🧪 GAC Agent AI Desktop 冒烟测试")
        print(f"🎯 测试目标: {self.base_url}")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

        # 执行测试序列
        tests = [
            # self.test_service_connectivity,
            # self.test_health_endpoint,
            # self.test_ai_desktop_basic_request,
            # self.test_image_processing_functionality,
            # self.test_parameter_boundaries,
            self.test_response_time_performance,
            self.test_error_recovery
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_func in tests:
            if test_func():
                passed_tests += 1
            print("-" * 80)

        # 输出测试总结
        success_rate = (passed_tests / total_tests) * 100
        overall_success = passed_tests == total_tests

        print("📊 测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   成功率: {success_rate:.1f}%")

        if overall_success:
            print("🎉 冒烟测试全部通过！服务功能正常")
        elif passed_tests >= total_tests * 0.75:
            print("✅ 大部分冒烟测试通过，服务基本正常")
        else:
            print("⚠️  多项冒烟测试失败，请检查服务状态")

        print("=" * 80)
        return overall_success


def main():
    """主函数"""
    # 可以通过命令行参数指定服务地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"

    smoke_test = GacAgentAiDesktopSmokeTest(base_url)
    success = smoke_test.run_smoke_tests()

    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
