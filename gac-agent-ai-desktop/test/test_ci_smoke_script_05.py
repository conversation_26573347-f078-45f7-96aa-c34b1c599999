#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序05：验证 CI 冒烟测试脚本
测试 ci_smoke_test.sh 脚本的功能和结构
"""

import os
import sys
import subprocess
import time

def test_ci_script_exists():
    """测试CI脚本是否存在"""
    print("=" * 70)
    print("🧪 测试程序05：验证 CI 冒烟测试脚本")
    print("=" * 70)
    
    script_path = "ci_smoke_test.sh"
    
    if os.path.exists(script_path):
        print("✅ CI冒烟测试脚本存在")
        return True
    else:
        print("❌ CI冒烟测试脚本不存在")
        return False

def test_script_permissions():
    """测试脚本权限"""
    print("\n🔧 测试脚本权限...")
    
    script_path = "ci_smoke_test.sh"
    
    try:
        # 检查文件是否可读
        if os.access(script_path, os.R_OK):
            print("✅ 脚本可读")
        else:
            print("❌ 脚本不可读")
            return False
        
        # 尝试添加执行权限
        os.chmod(script_path, 0o755)
        
        # 检查是否可执行
        if os.access(script_path, os.X_OK):
            print("✅ 脚本可执行")
            return True
        else:
            print("❌ 脚本不可执行")
            return False
            
    except Exception as e:
        print(f"❌ 权限检查异常: {e}")
        return False

def test_script_syntax():
    """测试脚本语法"""
    print("\n🔧 测试脚本语法...")
    
    script_path = "ci_smoke_test.sh"
    
    try:
        # 使用bash -n检查语法
        result = subprocess.run(
            ["bash", "-n", script_path],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ 脚本语法正确")
            return True
        else:
            print(f"❌ 脚本语法错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 语法检查超时")
        return False
    except Exception as e:
        print(f"❌ 语法检查异常: {e}")
        return False

def test_script_content():
    """测试脚本内容"""
    print("\n🔧 测试脚本内容...")
    
    script_path = "ci_smoke_test.sh"
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查必要的内容
        required_elements = [
            "#!/bin/bash",
            "set -e",
            "SERVICE_URL",
            "MAX_WAIT_TIME",
            "check_service_health",
            "check_service_port",
            "smoke_test.py",
            "gac-agent-desktop/v1/health",
            "gac-agent-desktop/v1/ai_desktop"
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ 脚本缺少必要元素: {', '.join(missing_elements)}")
            return False
        else:
            print("✅ 脚本包含所有必要元素")
            return True
            
    except Exception as e:
        print(f"❌ 内容检查异常: {e}")
        return False

def test_environment_variables():
    """测试环境变量设置"""
    print("\n🔧 测试环境变量设置...")
    
    script_path = "ci_smoke_test.sh"
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查环境变量默认值
        env_vars = [
            "SERVICE_URL=${SERVICE_URL:-\"http://127.0.0.1:8080\"}",
            "MAX_WAIT_TIME=${MAX_WAIT_TIME:-120}",
            "HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-5}",
            "SERVICE_PORT=${SERVICE_PORT:-8080}"
        ]
        
        found_vars = 0
        for var in env_vars:
            if var in content:
                found_vars += 1
            else:
                # 检查变量名是否存在（可能格式略有不同）
                var_name = var.split('=')[0]
                if var_name in content:
                    found_vars += 1
        
        if found_vars >= len(env_vars) * 0.75:  # 至少75%的变量存在
            print(f"✅ 环境变量设置正确 ({found_vars}/{len(env_vars)})")
            return True
        else:
            print(f"❌ 环境变量设置不完整 ({found_vars}/{len(env_vars)})")
            return False
            
    except Exception as e:
        print(f"❌ 环境变量检查异常: {e}")
        return False

def test_function_definitions():
    """测试函数定义"""
    print("\n🔧 测试函数定义...")
    
    script_path = "ci_smoke_test.sh"
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查必要的函数
        required_functions = [
            "check_service_health()",
            "check_service_port()"
        ]
        
        found_functions = 0
        for func in required_functions:
            if func in content:
                found_functions += 1
        
        if found_functions == len(required_functions):
            print(f"✅ 所有必要函数都已定义 ({found_functions}/{len(required_functions)})")
            return True
        else:
            print(f"❌ 缺少必要函数 ({found_functions}/{len(required_functions)})")
            return False
            
    except Exception as e:
        print(f"❌ 函数定义检查异常: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🔧 测试错误处理...")
    
    script_path = "ci_smoke_test.sh"
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查错误处理相关内容
        error_handling_elements = [
            "set -e",  # 遇到错误立即退出
            "exit 1",  # 错误退出码
            "|| echo",  # 错误时的备用操作
            "timeout",  # 超时处理
            "try",  # Python异常处理
            "except"  # Python异常捕获
        ]
        
        found_elements = 0
        for element in error_handling_elements:
            if element in content:
                found_elements += 1
        
        if found_elements >= len(error_handling_elements) * 0.6:  # 至少60%的错误处理元素存在
            print(f"✅ 错误处理机制完善 ({found_elements}/{len(error_handling_elements)})")
            return True
        else:
            print(f"❌ 错误处理机制不完善 ({found_elements}/{len(error_handling_elements)})")
            return False
            
    except Exception as e:
        print(f"❌ 错误处理检查异常: {e}")
        return False

def test_output_format():
    """测试输出格式"""
    print("\n🔧 测试输出格式...")
    
    script_path = "ci_smoke_test.sh"
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查输出格式相关内容
        output_elements = [
            "echo \"🧪",  # 测试相关emoji
            "echo \"✅",  # 成功标记
            "echo \"❌",  # 失败标记
            "echo \"⏳",  # 等待标记
            "echo \"📋",  # 信息标记
            "$(date"      # 时间戳
        ]
        
        found_elements = 0
        for element in output_elements:
            if element in content:
                found_elements += 1
        
        if found_elements >= len(output_elements) * 0.7:  # 至少70%的输出元素存在
            print(f"✅ 输出格式规范 ({found_elements}/{len(output_elements)})")
            return True
        else:
            print(f"❌ 输出格式不规范 ({found_elements}/{len(output_elements)})")
            return False
            
    except Exception as e:
        print(f"❌ 输出格式检查异常: {e}")
        return False

def test_integration_with_gitlab_ci():
    """测试与GitLab CI的集成"""
    print("\n🔧 测试与GitLab CI的集成...")
    
    gitlab_ci_path = "../.gitlab-ci.yml"
    
    try:
        if os.path.exists(gitlab_ci_path):
            with open(gitlab_ci_path, 'r', encoding='utf-8') as f:
                ci_content = f.read()
            
            # 检查CI配置中的冒烟测试相关内容
            ci_elements = [
                "smoke_test",
                "ci_smoke_test.sh",
                "chmod +x ci_smoke_test.sh",
                "./ci_smoke_test.sh",
                "smoke_test_results.log"
            ]
            
            found_elements = 0
            for element in ci_elements:
                if element in ci_content:
                    found_elements += 1
            
            if found_elements >= len(ci_elements) * 0.8:  # 至少80%的CI元素存在
                print(f"✅ GitLab CI集成正确 ({found_elements}/{len(ci_elements)})")
                return True
            else:
                print(f"❌ GitLab CI集成不完整 ({found_elements}/{len(ci_elements)})")
                return False
        else:
            print("⚠️  GitLab CI配置文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ GitLab CI集成检查异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 测试程序05：验证 CI 冒烟测试脚本")
    
    tests = [
        ("脚本存在性", test_ci_script_exists),
        ("脚本权限", test_script_permissions),
        ("脚本语法", test_script_syntax),
        ("脚本内容", test_script_content),
        ("环境变量", test_environment_variables),
        ("函数定义", test_function_definitions),
        ("错误处理", test_error_handling),
        ("输出格式", test_output_format),
        ("GitLab CI集成", test_integration_with_gitlab_ci)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            
            if success:
                passed_tests += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
        
        print("-" * 50)
    
    # 总结
    success_rate = (passed_tests / total_tests) * 100
    print(f"\n📊 验证总结:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   失败测试: {total_tests - passed_tests}")
    print(f"   成功率: {success_rate:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 CI冒烟测试脚本验证全部通过！")
        print("   脚本已准备就绪，可以用于CI/CD流程")
        return True
    elif passed_tests >= total_tests * 0.8:
        print("\n✅ 大部分验证通过，CI脚本基本可用")
        return True
    else:
        print("\n⚠️  多项验证失败，请检查CI脚本")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
