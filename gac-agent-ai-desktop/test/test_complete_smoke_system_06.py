#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序06：完整冒烟测试体系验证
验证整个冒烟测试体系是否完整和正确
"""

import os
import sys
import subprocess
import json

def test_file_structure():
    """测试文件结构完整性"""
    print("=" * 70)
    print("🧪 测试程序06：完整冒烟测试体系验证")
    print("=" * 70)
    
    print("📁 检查文件结构...")
    
    required_files = [
        "smoke_test.py",
        "ci_smoke_test.sh", 
        "base64.txt",
        "README.md",
        "../.gitlab-ci.yml"
    ]
    
    optional_files = [
        "curl.sh",
        "desktop.txt",
        "test_smoke_test_validation_04.py",
        "test_ci_smoke_script_05.py"
    ]
    
    missing_required = []
    missing_optional = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_required.append(file)
        else:
            print(f"   ✅ {file}")
    
    for file in optional_files:
        if not os.path.exists(file):
            missing_optional.append(file)
        else:
            print(f"   ✅ {file} (可选)")
    
    if missing_required:
        print(f"\n❌ 缺少必需文件: {', '.join(missing_required)}")
        return False
    
    if missing_optional:
        print(f"\n⚠️  缺少可选文件: {', '.join(missing_optional)}")
    
    print("\n✅ 文件结构检查通过")
    return True

def test_smoke_test_script():
    """测试主冒烟测试脚本"""
    print("\n🔧 测试主冒烟测试脚本...")
    
    try:
        # 检查脚本是否可以导入
        result = subprocess.run(
            [sys.executable, "-c", "from smoke_test import GacAgentAiDesktopSmokeTest; print('Import successful')"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("   ✅ 脚本可以正常导入")
        else:
            print(f"   ❌ 脚本导入失败: {result.stderr}")
            return False
        
        # 检查脚本是否可以运行（语法检查）
        result = subprocess.run(
            [sys.executable, "-m", "py_compile", "smoke_test.py"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("   ✅ 脚本语法正确")
            return True
        else:
            print(f"   ❌ 脚本语法错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ 脚本测试异常: {e}")
        return False

def test_ci_script():
    """测试CI脚本"""
    print("\n🔧 测试CI脚本...")
    
    try:
        # 检查脚本语法
        result = subprocess.run(
            ["bash", "-n", "ci_smoke_test.sh"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("   ✅ CI脚本语法正确")
        else:
            print(f"   ❌ CI脚本语法错误: {result.stderr}")
            return False
        
        # 检查脚本权限
        if os.access("ci_smoke_test.sh", os.X_OK):
            print("   ✅ CI脚本可执行")
            return True
        else:
            print("   ❌ CI脚本不可执行")
            return False
            
    except Exception as e:
        print(f"   ❌ CI脚本测试异常: {e}")
        return False

def test_gitlab_ci_config():
    """测试GitLab CI配置"""
    print("\n🔧 测试GitLab CI配置...")
    
    ci_file = "../.gitlab-ci.yml"
    
    try:
        with open(ci_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键配置项
        required_elements = [
            "smoke_test",
            "smoke_test_job:",
            "ci_smoke_test.sh",
            "python:3.11",
            "artifacts:",
            "smoke_test_results.log"
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"   ❌ CI配置缺少元素: {', '.join(missing_elements)}")
            return False
        else:
            print("   ✅ CI配置包含所有必要元素")
            return True
            
    except Exception as e:
        print(f"   ❌ CI配置测试异常: {e}")
        return False

def test_base64_data():
    """测试base64数据文件"""
    print("\n🔧 测试base64数据文件...")
    
    try:
        with open("base64.txt", 'r', encoding='utf-8') as f:
            base64_data = f.read().strip()
        
        # 检查base64数据格式
        if base64_data.startswith("data:image/"):
            print("   ✅ base64数据格式正确")
            
            # 检查数据长度
            if len(base64_data) > 50:  # 至少有一些实际数据
                print(f"   ✅ base64数据长度合理 ({len(base64_data)} 字符)")
                return True
            else:
                print(f"   ❌ base64数据过短 ({len(base64_data)} 字符)")
                return False
        else:
            print(f"   ❌ base64数据格式错误: {base64_data[:50]}...")
            return False
            
    except Exception as e:
        print(f"   ❌ base64数据测试异常: {e}")
        return False

def test_documentation():
    """测试文档完整性"""
    print("\n🔧 测试文档完整性...")
    
    try:
        with open("README.md", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查文档关键章节
        required_sections = [
            "## 概述",
            "## 文件说明", 
            "## 测试内容",
            "## 使用方法",
            "### 本地运行",
            "### 在CI/CD中运行",
            "## 测试配置",
            "## 故障排除"
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in content:
                missing_sections.append(section)
        
        if missing_sections:
            print(f"   ❌ 文档缺少章节: {', '.join(missing_sections)}")
            return False
        else:
            print("   ✅ 文档结构完整")
            
            # 检查文档长度
            if len(content) > 1000:  # 至少1000字符
                print(f"   ✅ 文档内容充实 ({len(content)} 字符)")
                return True
            else:
                print(f"   ⚠️  文档内容较少 ({len(content)} 字符)")
                return True
            
    except Exception as e:
        print(f"   ❌ 文档测试异常: {e}")
        return False

def test_integration_consistency():
    """测试集成一致性"""
    print("\n🔧 测试集成一致性...")
    
    try:
        # 检查smoke_test.py中的URL配置
        with open("smoke_test.py", 'r', encoding='utf-8') as f:
            smoke_content = f.read()
        
        # 检查ci_smoke_test.sh中的URL配置
        with open("ci_smoke_test.sh", 'r', encoding='utf-8') as f:
            ci_content = f.read()
        
        # 检查GitLab CI中的URL配置
        with open("../.gitlab-ci.yml", 'r', encoding='utf-8') as f:
            gitlab_content = f.read()
        
        # 检查端点一致性
        endpoints = [
            "/gac-agent-desktop/v1/health",
            "/gac-agent-desktop/v1/ai_desktop",
            "/gac-agent-desktop/v1/vehicle_parameter"
        ]
        
        consistency_issues = []
        
        for endpoint in endpoints:
            in_smoke = endpoint in smoke_content
            in_ci = endpoint in ci_content
            
            if not (in_smoke and in_ci):
                consistency_issues.append(f"端点 {endpoint} 在脚本间不一致")
        
        # 检查端口一致性
        port_8080_in_smoke = ":8080" in smoke_content
        port_8080_in_ci = ":8080" in ci_content
        port_8080_in_gitlab = ":8080" in gitlab_content
        
        if not (port_8080_in_smoke and port_8080_in_ci and port_8080_in_gitlab):
            consistency_issues.append("端口配置在脚本间不一致")
        
        if consistency_issues:
            print(f"   ❌ 集成一致性问题: {'; '.join(consistency_issues)}")
            return False
        else:
            print("   ✅ 集成配置一致")
            return True
            
    except Exception as e:
        print(f"   ❌ 集成一致性测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 测试程序06：完整冒烟测试体系验证")
    
    tests = [
        ("文件结构", test_file_structure),
        ("主冒烟测试脚本", test_smoke_test_script),
        ("CI脚本", test_ci_script),
        ("GitLab CI配置", test_gitlab_ci_config),
        ("Base64数据", test_base64_data),
        ("文档完整性", test_documentation),
        ("集成一致性", test_integration_consistency)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            
            if success:
                passed_tests += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
        
        print("-" * 50)
    
    # 总结
    success_rate = (passed_tests / total_tests) * 100
    print(f"\n📊 体系验证总结:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   失败测试: {total_tests - passed_tests}")
    print(f"   成功率: {success_rate:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 完整冒烟测试体系验证全部通过！")
        print("   ✅ 冒烟测试脚本完整且功能正确")
        print("   ✅ CI脚本配置正确且可执行")
        print("   ✅ GitLab CI集成配置完善")
        print("   ✅ 文档完整且详细")
        print("   ✅ 各组件配置一致")
        print("\n🚀 冒烟测试体系已准备就绪，可以投入使用！")
        return True
    elif passed_tests >= total_tests * 0.85:
        print("\n✅ 大部分验证通过，冒烟测试体系基本可用")
        print("   建议修复失败的测试项以达到最佳状态")
        return True
    else:
        print("\n⚠️  多项验证失败，请检查冒烟测试体系")
        print("   需要修复失败的测试项才能正常使用")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
