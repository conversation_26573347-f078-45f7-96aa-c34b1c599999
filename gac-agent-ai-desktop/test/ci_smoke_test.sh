#!/bin/bash

# CI环境专用的冒烟测试脚本
# 专门针对Docker CI环境优化

set -e

# 配置参数
SERVICE_URL=${SERVICE_URL:-"http://127.0.0.1:8080"}
MAX_WAIT_TIME=${MAX_WAIT_TIME:-120}
HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-5}
SERVICE_PORT=${SERVICE_PORT:-8080}

echo "🧪 GAC Agent AI Desktop CI环境冒烟测试"
echo "=========================================="
echo "服务地址: $SERVICE_URL"
echo "服务端口: $SERVICE_PORT"
echo "最大等待时间: ${MAX_WAIT_TIME}秒"
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# 检查Python环境
echo "🔍 检查Python环境..."
python3 --version
pip3 --version

# 安装依赖（如果需要）
echo "📦 确保依赖包已安装..."
pip3 install requests 2>/dev/null || echo "依赖包已安装或安装失败"

# 函数：检查服务健康状态
check_service_health() {
    local health_url="${SERVICE_URL}/gac-agent-desktop/v1/health"
    
    python3 -c "
import requests
import sys
try:
    response = requests.get('$health_url', timeout=10)
    if response.status_code == 200:
        data = response.json()
        if data.get('status') == 'ok':
            version = data.get('version', 'unknown')
            print('✅ 服务健康检查通过 (版本: ' + version + ')')
            sys.exit(0)
        else:
            print('❌ 服务状态异常:', data.get('status', 'unknown'))
    else:
        print('❌ 健康检查失败，状态码:', response.status_code)
    sys.exit(1)
except requests.exceptions.ConnectionError:
    print('❌ 无法连接到服务')
    sys.exit(1)
except Exception as e:
    print('❌ 健康检查异常:', str(e))
    sys.exit(1)
"
    return $?
}

# 函数：检查服务端口
check_service_port() {
    python3 -c "
import socket
import sys
try:
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(5)
    result = sock.connect_ex(('127.0.0.1', $SERVICE_PORT))
    sock.close()
    if result == 0:
        print('✅ 服务端口 $SERVICE_PORT 可访问')
        sys.exit(0)
    else:
        print('❌ 服务端口 $SERVICE_PORT 不可访问')
        sys.exit(1)
except Exception as e:
    print('❌ 端口检查异常:', str(e))
    sys.exit(1)
"
    return $?
}

# 等待服务就绪
echo "⏳ 等待服务就绪..."
wait_time=0
service_ready=false

while [ $wait_time -lt $MAX_WAIT_TIME ]; do
    # 先检查端口
    if check_service_port; then
        # 端口可访问，再检查健康状态
        if check_service_health; then
            service_ready=true
            break
        fi
    fi
    
    echo "   等待中... (${wait_time}/${MAX_WAIT_TIME}秒)"
    sleep $HEALTH_CHECK_INTERVAL
    wait_time=$((wait_time + HEALTH_CHECK_INTERVAL))
done

if [ "$service_ready" = false ]; then
    echo "❌ 服务未就绪，等待超时"
    echo ""
    echo "📋 尝试显示服务信息:"
    
    # 显示进程信息
    echo "运行中的Python进程:"
    ps aux | grep python || echo "未找到Python进程"
    
    # 显示网络端口
    echo "监听的端口:"
    netstat -tlnp 2>/dev/null | grep :$SERVICE_PORT || echo "端口${SERVICE_PORT}未监听"
    
    # 显示服务日志
    if [ -f "../service.log" ]; then
        echo "服务日志（最后30行）:"
        tail -30 ../service.log
    elif [ -f "service.log" ]; then
        echo "服务日志（最后30行）:"
        tail -30 service.log
    else
        echo "未找到服务日志文件"
    fi
    
    # 尝试直接访问服务
    echo "尝试直接访问服务:"
    curl -s -m 10 "$SERVICE_URL" || echo "无法访问服务"
    
    exit 1
fi

echo ""
echo "🧪 开始运行冒烟测试..."

# 运行冒烟测试
test_result=0

if [ -f "smoke_test.py" ]; then
    echo "🧪 运行冒烟测试..."
    python3 smoke_test.py "$SERVICE_URL"
    basic_result=$?

    if [ $basic_result -eq 0 ]; then
        echo "✅ 冒烟测试通过"
    else
        echo "❌ 冒烟测试失败"
        test_result=1
    fi
else
    echo "❌ 错误: 未找到冒烟测试文件 smoke_test.py"
    test_result=1
fi

# 快速验证测试（如果冒烟测试失败）
if [ $test_result -ne 0 ]; then
    echo ""
    echo "⚠️ 冒烟测试失败，尝试快速验证..."
    
    # 简单的健康检查验证
    python3 -c "
import requests
import json
import sys

try:
    # 健康检查
    health_response = requests.get('$SERVICE_URL/gac-agent-desktop/v1/health', timeout=10)
    if health_response.status_code == 200:
        print('✅ 健康检查通过')
        
        # 简单的AI Desktop测试
        headers = {
            'Content-Type': 'application/json'
        }
        
        payload = {
            'image_base64': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            'query': '帮我打开音乐',
            'output_size': '1920x1080',
            'user_info': {
                'car_id': 'test_car_001',
                'user_id': 'test_user_001',
                'category': ['entertainment']
            }
        }
        
        ai_response = requests.post(
            '$SERVICE_URL/gac-agent-desktop/v1/ai_desktop',
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if ai_response.status_code == 200:
            print('✅ 基础AI Desktop功能正常')
            sys.exit(0)
        else:
            print(f'❌ AI Desktop功能异常: {ai_response.status_code}')
            sys.exit(1)
    else:
        print(f'❌ 健康检查失败: {health_response.status_code}')
        sys.exit(1)
        
except Exception as e:
    print(f'❌ 快速验证异常: {str(e)}')
    sys.exit(1)
"
    
    quick_result=$?
    if [ $quick_result -eq 0 ]; then
        echo "✅ 快速验证通过，基础功能正常"
        test_result=0
    else
        echo "❌ 快速验证也失败"
    fi
fi

echo ""
echo "⏰ 结束时间: $(date '+%Y-%m-%d %H:%M:%S')"

if [ $test_result -eq 0 ]; then
    echo "🎉 冒烟测试通过！服务功能正常"
    exit 0
else
    echo "❌ 冒烟测试失败"
    
    # 显示最终诊断信息
    echo ""
    echo "📋 最终诊断信息:"
    echo "服务地址: $SERVICE_URL"
    echo "健康检查: $SERVICE_URL/gac-agent-desktop/v1/health"
    echo "AI Desktop接口: $SERVICE_URL/gac-agent-desktop/v1/ai_desktop"
    echo "车辆参数接口: $SERVICE_URL/gac-agent-desktop/v1/vehicle_parameter"
    
    # 最后一次尝试访问
    echo "最后一次健康检查:"
    curl -s -m 5 "$SERVICE_URL/gac-agent-desktop/v1/health" || echo "健康检查失败"
    
    exit 1
fi
