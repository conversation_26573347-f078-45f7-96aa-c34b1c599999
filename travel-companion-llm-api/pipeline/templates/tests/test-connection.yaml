apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "license.fullname" . }}-test-connection"
  labels:
    {{- include "license.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "license.fullname" . }}:{{ .Values.podProbe.port }}{{ .Values.podProbe.path }}']
  restartPolicy: Never
