#!/usr/bin/env python
#-*- coding: utf-8 -*-
from src.utiles.utiles import *
from src.stage0_agent_intent import call_intent_agent
from src.stage1_classifier import call_classifier
from src.tool_filter import call_tool_filter
from src.tool_chat import call_tool_chat            

import json
import re
import requests
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor

# 设置URL
url = "https://sit-platform.senseauto.com/poi/v1/search/conditions"



# 设置headers
headers = {
    "client-id": "C4M79714h4t637t3eRRbd0P8ffq473o4",
    "Content-Type": "application/json"
}

poi_type_str = """
{
  "汽车服务": {
    "汽车服务相关": ["汽车服务相关"],
    "加油站": ["加油站", "中国石化", "中国石油", "壳牌", "美孚", "加德士", "东方", "中石油碧辟", "中石化碧辟", "道达尔", "埃索", "中化道达尔"],
    "其它能源站": ["其它能源站"],
    "加气站": ["加气站"],
    "汽车养护/装饰": ["汽车养护", "加水站"],
    "洗车场": ["洗车场"],
    "汽车俱乐部": ["汽车俱乐部"],
    "汽车救援": ["汽车救援"],
    "汽车配件销售": ["汽车配件销售"],
    "汽车租赁": ["汽车租赁", "汽车租赁还车"],
    "二手车交易": ["二手车交易"],
    "充电站": ["充电站", "换电站", "充换电站", "专用充电站"]
  },
  "餐饮服务": {
    "餐饮相关场所": ["餐饮相关"],
    "中餐厅": ["中餐厅", "综合酒楼", "四川菜(川菜)", "广东菜(粤菜)", "山东菜(鲁菜)", "江苏菜", "浙江菜", "上海菜", "湖南菜(湘菜)", "安徽菜(徽菜)", "福建菜", "北京菜", "湖北菜(鄂菜)", "东北菜", "云贵菜", "西北菜", "老字号", "火锅店", "特色/地方风味餐厅", "海鲜酒楼", "中式素菜馆", "清真菜馆", "台湾菜", "潮州菜"],
    "外国餐厅": ["外国餐厅", "西餐厅(综合风味)", "日本料理", "韩国料理", "法式菜品餐厅", "意式菜品餐厅", "泰国/越南菜品餐厅", "地中海风格菜品", "美式风味", "印度风味", "英国式菜品餐厅", "牛扒店(扒房)", "俄国菜", "葡国菜", "德国菜", "巴西菜", "墨西哥菜", "其它亚洲菜"],
    "快餐厅": ["快餐厅", "肯德基", "麦当劳", "必胜客", "永和豆浆", "茶餐厅", "大家乐", "大快活", "美心", "吉野家", "仙跡岩", "呷哺呷哺"],
    "休闲餐饮场所": ["休闲餐饮场所"],
    "咖啡厅": ["咖啡厅", "星巴克咖啡", "上岛咖啡", "Pacific CoffeeCompany", "巴黎咖啡店"],
    "茶艺馆": ["茶艺馆"],
    "冷饮店": ["冷饮店"],
    "糕饼店": ["糕饼店"],
    "甜品店": ["甜品店"]
  },
  "购物服务": {
    "购物相关场所": ["购物相关场所"],
    "商场": ["商场", "购物中心", "普通商场", "免税品店"],
    "便民商店/便利店": ["便民商店/便利店", "7-ELEVEn便利店", "OK便利店"],
    "家电电子卖场": ["家电电子卖场", "综合家电商场", "国美", "大中", "苏宁", "手机销售", "数码电子", "丰泽", "苏宁镭射"],
    "超级市场": ["超市", "家乐福", "沃尔玛", "华润", "北京华联", "上海华联", "麦德龙", "乐天玛特", "华堂", "卜蜂莲花", "屈臣氏", "惠康超市", "百佳超市", "万宁超市"],
    "花鸟鱼虫市场": ["花鸟鱼虫市场", "花卉市场", "宠物市场"],
    "家居建材市场": ["家居建材市场", "家具建材综合市场", "家具城", "建材五金市场", "厨卫市场", "布艺市场", "灯具瓷器市场"],
    "综合市场": ["综合市场", "小商品市场", "旧货市场", "农副产品市场", "果品市场", "蔬菜市场", "水产海鲜市场"],
    "文化用品店": ["文化用品店"],
    "体育用品店": ["体育用品店", "李宁专卖店", "耐克专卖店", "阿迪达斯专卖店", "锐步专卖店", "彪马专卖店", "高尔夫用品店", "户外用品"],
    "特色商业街": ["特色商业街", "步行街"],
    "服装鞋帽皮具店": ["服装鞋帽皮具店", "品牌服装店", "品牌鞋店", "品牌皮具店", "品牌箱包店"],
    "专卖店": ["专营店", "古玩字画店", "珠宝首饰工艺品", "钟表店", "眼镜店", "书店", "音像店", "儿童用品店", "自行车专卖店", "礼品饰品店", "烟酒专卖店", "宠物用品店", "摄影器材店", "宝马生活方式", "土特产专卖店"],
    "特殊买卖场所": ["特殊买卖场所", "拍卖行", "典当行"],
    "个人用品/化妆品店": ["其它个人用品店", "莎莎"]
  },
  "交通设施服务": {
    "交通服务相关": ["交通服务相关"],
    "机场相关": ["机场相关", "候机室", "摆渡车站", "飞机场", "机场出发/到达", "直升机场", "机场货运处"],
    "火车站": ["火车站", "候车室", "进站口/检票口", "出站口", "站台", "售票", "退票", "改签", "公安制证", "票务相关", "货运火车站"],
    "港口码头": ["港口码头", "客运港", "车渡口", "人渡口", "货运港口码头", "进港", "出港", "候船室"],
    "长途汽车站": ["长途汽车站", "进站", "出站", "候车室"],
    "地铁站": ["地铁站", "出入口"],
    "轻轨站": ["轻轨站"],
    "公交车站": ["公交车站相关", "旅游专线车站", "普通公交站", "机场巴士", "快速公交站", "电车站", "智轨车站"],
    "班车站": ["班车站"],
    "停车场": ["停车场相关", "换乘停车场", "公共停车场", "专用停车场", "路边停车场", "停车场入口", "停车场出口", "停车场出入口"]
  },
  "体育休闲服务": {
    "影剧院": ["电影院"]
  },
  "住宿服务": {
    "住宿服务相关": ["住宿服务相关"],
    "宾馆酒店": ["宾馆酒店", "奢华酒店", "五星级宾馆", "四星级宾馆", "三星级宾馆", "经济型连锁酒店"],
    "旅馆招待所": ["旅馆招待所", "青年旅舍"]
  },
  "风景名胜": {
    "风景名胜相关": ["旅游景点"],
    "公园广场": ["公园广场", "公园", "动物园", "植物园", "水族馆", "城市广场", "公园内部设施"],
    "风景名胜": ["风景名胜", "世界遗产", "国家级景点", "省级景点", "纪念馆", "寺庙道观", "教堂", "回教寺", "海滩", "观景点", "红色景区"]
  }
}
"""


retry_count = 0

first_level_list = ["汽车服务", "餐饮服务", "购物服务", "交通设施服务", "体育休闲服务", "住宿服务", "风景名胜"]
second_level_dict = {
    '汽车服务': ['汽车服务相关', '加油站', '其它能源站', '加气站', '汽车养护/装饰', '洗车场', '汽车俱乐部', '汽车救援', '汽车配件销售', '汽车租赁', '二手车交易', '充电站'], 
    '餐饮服务': ['餐饮相关场所', '中餐厅', '外国餐厅', '快餐厅', '休闲餐饮场所', '咖啡厅', '茶艺馆', '冷饮店', '糕饼店', '甜品店'], 
    '购物服务': ['购物相关场所', '商场', '便民商店/便利店', '家电电子卖场', '超级市场', '花鸟鱼虫市场', '家居建材市场', '综合市场', '文化用品店', '体育用品店', '特色商业街', '服装鞋帽皮具店', '专卖店', '特殊买卖场所', '个人用品/化妆品店'],
    '交通设施服务': ['交通服务相关', '机场相关', '火车站', '港口码头', '长途汽车站', '地铁站', '轻轨站', '公交车站', '班车站', '停车场'],
    '体育休闲服务': ['影剧院'], 
    '住宿服务': ['住宿服务相关', '宾馆酒店', '旅馆招待所'], '风景名胜': ['风景名胜相关', '公园广场', '风景名胜']
    }
third_level_dict = {
    '汽车服务相关': ['汽车服务相关'], 
    '加油站': ['加油站', '中国石化', '中国石油', '壳牌', '美孚', '加德士', '东方', '中石油碧辟', '中石化碧辟', '道达尔', '埃索', '中化道达尔'], 
    '其它能源站': ['其它能源站'], 
    '加气站': ['加气站'], 
    '汽车养护/装饰': ['汽车养护', '加水站'], 
    '洗车场': ['洗车场'], 
    '汽车俱乐部': ['汽车俱乐部'], 
    '汽车救援': ['汽车救援'], 
    '汽车配件销售': ['汽车配件销售'], 
    '汽车租赁': ['汽车租赁', '汽车租赁还车'], 
    '二手车交易': ['二手车交易'], 
    '充电站': ['充电站', '换电站', '充换电站', '专用充电站'], 
    '餐饮相关场所': ['餐饮相关'], 
    '中餐厅': ['中餐厅', '综合酒楼', '四川菜(川菜)', '广东菜(粤菜)', '山东菜(鲁菜)', '江苏菜', '浙江菜', '上海菜', '湖南菜(湘菜)', '安徽菜(徽菜)', '福建菜', '北京菜', '湖北菜(鄂菜)', '东北菜', '云贵菜', '西北菜', '老字号', '火锅店', '特色/地方风味餐厅', '海鲜酒楼', '中式素菜馆', '清真菜馆', '台湾菜', '潮州菜'], 
    '外国餐厅': ['外国餐厅', '西餐厅(综合风味)', '日本料理', '韩国料理', '法式菜品餐厅', '意式菜品餐厅', '泰国/越南菜品餐厅', '地中海风格菜品', '美式风味', '印度风味', '英国式菜品餐厅', '牛扒店(扒房)', '俄国菜', '葡国菜', '德国菜', '巴西菜', '墨西哥菜', '其它亚洲菜'],
    '快餐厅': ['快餐厅', '肯德基', '麦当劳', '必胜客', '永和豆浆', '茶餐厅', '大家乐', '大快活', '美心', '吉野家', '仙跡岩', '呷哺呷哺'], 
    '休闲餐饮场所': ['休闲餐饮场所'], 
    '咖啡厅': ['咖啡厅', '星巴克咖啡', '上岛咖啡', 'Pacific CoffeeCompany', '巴黎咖啡店'], 
    '茶艺馆': ['茶艺馆'],
    '冷饮店': ['冷饮店'],
    '糕饼店': ['糕饼店'],
    '甜品店': ['甜品店'],
    '购物相关场所': ['购物相关场所'], 
    '商场': ['商场', '购物中心', '普通商场', '免税品店'],
    '便民商店/便利店': ['便民商店/便利店', '7-ELEVEn便利店', 'OK便利店'],
    '家电电子卖场': ['家电电子卖场', '综合家电商场', '国美', '大中', '苏宁', '手机销售', '数码电子', '丰泽', '苏宁镭射'],
    '超级市场': ['超市', '家乐福', '沃尔玛', '华润', '北京华联', '上海华联', '麦德龙', '乐天玛特', '华堂', '卜蜂莲花', '屈臣氏', '惠康超市', '百佳超市', '万宁超市'],
    '花鸟鱼虫市场': ['花鸟鱼虫市场', '花卉市场', '宠物市场'],
    '家居建材市场': ['家居建材市场', '家具建材综合市场', '家具城', '建材五金市场', '厨卫市场', '布艺市场', '灯具瓷器市场'],
    '综合市场': ['综合市场', '小商品市场', '旧货市场', '农副产品市场', '果品市场', '蔬菜市场', '水产海鲜市场'],
    '文化用品店': ['文化用品店'],
    '体育用品店': ['体育用品店', '李宁专卖店', '耐克专卖店', '阿迪达斯专卖店', '锐步专卖店', '彪马专卖店', '高尔夫用品店', '户外用品'],
    '特色商业街': ['特色商业街', '步行街'],
    '服装鞋帽皮具店': ['服装鞋帽皮具店', '品牌服装店', '品牌鞋店', '品牌皮具店', '品牌箱包店'],
    '专卖店': ['专营店', '古玩字画店', '珠宝首饰工艺品', '钟表店', '眼镜店', '书店', '音像店', '儿童用品店', '自行车专卖店', '礼品饰品店', '烟酒专卖店', '宠物用品店', '摄影器材店', '宝马生活方式', '土特产专卖店'], 
    '特殊买卖场所': ['特殊买卖场所', '拍卖行', '典当行'], 
    '个人用品/化妆品店': ['其它个人用品店', '莎莎'], 
    '交通服务相关': ['交通服务相关'], 
    '机场相关': ['机场相关', '候机室', '摆渡车站', '飞机场', '机场出发/到达', '直升机场', '机场货运处'], 
    '火车站': ['火车站', '候车室', '进站口/检票口', '出站口', '站台', '售票', '退票', '改签', '公安制证', '票务相关', '货运火车站'], 
    '港口码头': ['港口码头', '客运港', '车渡口', '人渡口', '货运港口码头', '进港', '出港', '候船室'], 
    '长途汽车站': ['长途汽车站', '进站', '出站', '候车室'], 
    '地铁站': ['地铁站', '出入口'], 
    '轻轨站': ['轻轨站'], 
    '公交车站': ['公交车站相关', '旅游专线车站', '普通公交站', '机场巴士', '快速公交站', '电车站', '智轨车站'], 
    '班车站': ['班车站'], 
    '停车场': ['停车场相关', '换乘停车场', '公共停车场', '专用停车场', '路边停车场', '停车场入口', '停车场出口', '停车场出入口'], 
    '影剧院': ['电影院'], 
    '住宿服务相关': ['住宿服务相关'], 
    '宾馆酒店': ['宾馆酒店', '奢华酒店', '五星级宾馆', '四星级宾馆', '三星级宾馆', '经济型连锁酒店'], 
    '旅馆招待所': ['旅馆招待所', '青年旅舍'], 
    '风景名胜相关': ['旅游景点'], 
    '公园广场': ['公园广场', '公园', '动物园', '植物园', '水族馆', '城市广场', '公园内部设施'], 
    '风景名胜': ['风景名胜', '世界遗产', '国家级景点', '省级景点', '纪念馆', '寺庙道观', '教堂', '回教寺', '海滩', '观景点', '红色景区']
    }

sec_trd_level_dict = {
    '汽车服务': ['汽车服务相关', '加油站', '中国石化', '中国石油', '壳牌', '美孚', '加德士', '东方', '中石油碧辟', '中石化碧辟', '道达尔', '埃索', '中化道达尔', '其它能源站', '加气站', '汽车养护', '加水站', '洗车场', '汽车俱乐部', '汽车救援', '汽车配件销售', '汽车租赁', '汽车租赁还车', '二手车交易', '充电站', '换电站', '充换电站', '专用充电站'], 
    '餐饮服务': ['中餐厅', '综合酒楼', '四川菜(川菜)', '广东菜(粤菜)', '山东菜(鲁菜)', '江苏菜', '浙江菜', '上海菜', '湖南菜(湘菜)', '安徽菜(徽菜)', '福建菜', '北京菜', '湖北菜(鄂菜)', '东北菜', '云贵菜', '西北菜', '老字号', '火锅店', '特色/地方风味餐厅', '海鲜酒楼', '中式素菜馆', '清真菜馆', '台湾菜', '潮州菜', '外国餐厅', '西餐厅(综合风味)', '日本料理', '韩国料理', '法式菜品餐厅', '意式菜品餐厅', '泰国/越南菜品餐厅', '地中海风格菜品', '美式风味', '印度风味', '英国式菜品餐厅', '牛扒店(扒房)', '俄国菜', '葡国菜', '德国菜', '巴西菜', '墨西哥菜', '其它亚洲菜', '快餐厅', '肯德基', '麦当劳', '必胜客', '永和豆浆', '茶餐厅', '大家乐', '大快活', '美心', '吉野家', '仙跡岩', '呷哺呷哺', '休闲餐饮场所', '咖啡厅', '星巴克咖啡', '上岛咖啡', 'Pacific CoffeeCompany', '巴黎咖啡店', '茶艺馆', '冷饮店', '糕饼店', '甜品店'], 
    '购物服务': ['购物相关场所', '商场', '购物中心', '普通商场', '免税品店', '便民商店/便利店', '7-ELEVEn便利店', 'OK便利店', '家电电子卖场', '综合家电商场', '国美', '大中', '苏宁', '手机销售', '数码电子', '丰泽', '苏宁镭射', '超市', '家乐福', '沃尔玛', '华润', '北京华联', '上海华联', '麦德龙', '乐天玛特', '华堂', '卜蜂莲花', '屈臣氏', '惠康超市', '百佳超市', '万宁超市', '花鸟鱼虫市场', '花卉市场', '宠物市场', '家居建材市场', '家具建材综合市场', '家具城', '建材五金市场', '厨卫市场', '布艺市场', '灯具瓷器市场', '综合市场', '小商品市场', '旧货市场', '农副产品市场', '果品市场', '蔬菜市场', '水产海鲜市场', '文化用品店', '体育用品店', '李宁专卖店', '耐克专卖店', '阿迪达斯专卖店', '锐步专卖店', '彪马专卖店', '高尔夫用品店', '户外用品', '特色商业街', '步行街', '服装鞋帽皮具店', '品牌服装店', '品牌鞋店', '品牌皮具店', '品牌箱包店', '专营店', '古玩字画店', '珠宝首饰工艺品', '钟表店', '眼镜店', '书店', '音像店', '儿童用品店', '自行车专卖店', '礼品饰品店', '烟酒专卖店', '宠物用品店', '摄影器材店', '宝马生活方式', '土特产专卖店', '特殊买卖场所', '拍卖行', '典当行', '其它个人用品店', '莎莎'], 
    '交通设施服务': ['交通服务相关', '机场相关', '候机室', '摆渡车站', '飞机场', '机场出发/到达', '直升机场', '机场货运处', '火车站', '候车室', '进站口/检票口', '出站口', '站台', '售票', '退票', '改签', '公安制证', '票务相关', '货运火车站', '港口码头', '客运港', '车渡口', '人渡口', '货运港口码头', '进港', '出港', '候船室', '长途汽车站', '进站', '出站', '候车室', '地铁站', '出入口', '轻轨站', '公交车站相关', '旅游专线车站', '普通公交站', '机场巴士', '快速公交站', '电车站', '智轨车站', '班车站', '停车场相关', '换乘停车场', '公共停车场', '专用停车场', '路边停车场', '停车场入口', '停车场出口', '停车场出入口'], 
    '体育休闲服务': ['电影院'], 
    '住宿服务': ['住宿服务相关', '宾馆酒店', '奢华酒店', '五星级宾馆', '四星级宾馆', '三星级宾馆', '经济型连锁酒店', '旅馆招待所', '青年旅舍'], 
    '风景名胜': ['旅游景点', '公园广场', '公园', '动物园', '植物园', '水族馆', '城市广场', '公园内部设施', '风景名胜', '世界遗产', '国家级景点', '省级景点', '纪念馆', '寺庙道观', '教堂', '回教寺', '海滩', '观景点', '红色景区']
    }

def delete_by_indices(data_list, indices):
    try:
        # 按降序排序索引列表以避免删除时索引出错
        for index in sorted(indices, reverse=True):
            del data_list[index]
    except IndexError:
        return "One or more indices are out of range."
    return data_list



# current_dir = os.path.dirname(__file__)
# prompts_path = os.path.join(current_dir,'','prompts')

async def get_class(queries, model_name="qwen72b"):
    class_res = call_classifier([queries], first_level_list, model_name)
    first_class_type = json.loads(class_res)['Type']
    print(f"first type: {first_class_type}")

    if first_class_type not in first_level_list:
        return "", "", ""

        # class_res = call_classifier([queries], sec_trd_level_dict[first_class_type], model_name)
        # sec_trd_class_type = json.loads(class_res)['Type']
        # print(f"sec_trd type: {sec_trd_class_type}")
        
    class_res = call_classifier([queries], second_level_dict[first_class_type], model_name)
    second_class_type = json.loads(class_res)['Type']
    print(f"second type: {second_class_type}")

    if second_class_type not in second_level_dict[first_class_type]:
        return first_class_type, "", ""
        
    class_res = call_classifier([queries], third_level_dict[second_class_type], model_name)
    third_class_type = json.loads(class_res)['Type']
    print(f"third type: {third_class_type}")

    if third_class_type not in third_level_dict[second_class_type]:
        return first_class_type, second_class_type, ""
    
    return first_class_type, second_class_type, third_class_type

async def main():
    # model_name = "gpt-4o-mini"
    # model_name = "gpt-4o"
    model_name = "qwen72b"
    # model_name = "SenseChat-5"
    queries = ''
    history = ''
    # 找个博物馆,不要故宫

    query = input("用户: ")
    # queries= queries+ "###用户: "+query
    queries= queries+  '###Action_output:{'+ "###用户: "+query +'}'
    history= history+  '###Action_output:{'+ "###用户: "+query +'}'

    last_list={}
    retry_count = 0

    while True:
        
        # 检查queries的长度是否超过3万
        if len(queries) > 30000:
            queries = queries[-30000:]

        print('-----------------------------queries START------------------------------------------------------')
        print('queries length', len(queries))
        print( queries)

        print('-----------------------------queries END------------------------------------------------------')

        import time

        start_time = time.time()
        
        loop = asyncio.get_running_loop()
        classing = asyncio.create_task(get_class([queries]))
        with ThreadPoolExecutor() as executor:
            intenting_task = loop.run_in_executor(
                executor, call_intent_agent, [queries], model_name
            )
            call_result = await intenting_task
        first_class_type, second_class_type, third_class_type = await classing
        print(f"call_result: {call_result}")

        # first_class_type, second_class_type, third_class_type = get_class([queries])
        #输入是动作输出，输出是三元组
        # call_result = call_intent_agent([queries], model_name)
        end_time = time.time()
        elapsed_time = end_time - start_time

        print(f"++++++++++++++++++++++++call_intent_agent函数调用持续时间: {elapsed_time:.2f} 秒")


        call_result = call_result.strip('###LLM:')
        call_result = call_result.strip('```')
        # call_result = re.split(r'||', call_result.strip('###LLM:').strip('```'))

        call_result_all = call_result.split(r'||')
        print(f"call_result_all: \n{call_result_all}")

        action_valid = True
        for call_result in call_result_all:
            # 检查action是否在工具箱中并且合法

            try:
             
                call_result = json.loads(call_result)
                
            except json.JSONDecodeError as e:
                print('e==', e)
                print('call_result==', call_result)
                call_result = json.loads('{"Thought": "None.", "Action": "response", "Params":"' +call_result+ ', 格式错误，请重新生成"}')
                queries += "###Action_output:{警告: Params 格式错误}"
                history += "###Action_output:{警告: Params 格式错误}"
                action_valid = False

            valid_actions = ['search', 'navigation', 'sort', 'filter_chat', 'response']
            if call_result["Action"] not in valid_actions:
                print(f"警告: 非法action '{call_result['Action']}'")
                queries += f"###Action_output:{{警告: 非法action '{call_result['Action']}'}}"
                history += f"###Action_output:{{警告: 非法action '{call_result['Action']}'}}"
                action_valid = False
            
            # 检查Params是否为字典类型
            # if not isinstance(call_result["Params"], dict):
            #     print(f"警告: Params 不是字典类型")
            #     queries += "###Action_output:{警告: Params 格式错误}"
            #     history += "###Action_output:{警告: Params 格式错误}"
            #     action_valid = False

        if not action_valid:
            continue


        for call_result in call_result_all:
            if "filter_json_schema_str" in call_result:
                call_result = '''
                {
                    "Action": "filter_chat",
                    "Params": {
                        "keyword": []
                    }
                }
                '''

            print('-----------------------------LLM_result START------------------------------------------------------')

            print('LLM_result ', call_result)

            print('-----------------------------LLM_result END------------------------------------------------------')


            try:
                phrase_result = json.loads(call_result)
            except json.JSONDecodeError as e:
                print('e==', e)
                print('call_result==', call_result)
                phrase_result = json.loads('{"Thought": "None.", "Action": "response", "Params":"' +call_result+ ', 格式错误，请重新生成"}')


            action_ = phrase_result["Action"]
            action_input1 = phrase_result["Params"]


            queries= queries+ '###LLM:' + call_result

            history= history+ '###LLM:' + call_result



            if 'search' in action_:

                # 添加经纬度到action_input1
                action_input1["latitude"] = "40.081839"
                action_input1["longitude"] = "116.586733"


                # 检查action_input1的合法性
                def check_poi_type_validity(poi_type_big, poi_type_big_c, poi_type_mid, poi_type_sub):
                    # import pdb;pdb.set_trace()
                    if poi_type_big != poi_type_big_c: return False

                    poi_type_dict = json.loads(poi_type_str)
                    
                    if poi_type_big in poi_type_dict:
                        if not poi_type_mid and not poi_type_sub:
                            return True
                        if poi_type_mid in poi_type_dict[poi_type_big]:
                            if not poi_type_sub:
                                return True
                            if poi_type_sub in poi_type_dict[poi_type_big][poi_type_mid]:
                                return True
                    return False

                #合并宇航的 poiTypeMid, poiTypeSub

                # is_valid = check_poi_type_validity(
                #     action_input1.get("poiTypeBig", ""),
                #     first_class_type,
                #     second_class_type,
                #     third_class_type
                # )

                # if not is_valid:
                #     print("警告：POI类型不合法")
                #     queries= queries+  '###Action_output:{'+ 'POI类型不合法，请重新生成' +'}'
                #     retry_count += 1
                #     if retry_count > 3:
                #         queries= queries+  '###Action_output:{'+ '已达到最大重试次数，告知用户出现问题稍后再试。' +'}'
                #         continue        
                #     continue

                retry_count = 0
                # action_input1["pageSize"] = 200

                # import pdb;pdb.set_trace()


                # action_input1["poiTypeMid"] = second_class_type
                action_input1["poiType"] = third_class_type

                if "poiTypeBig" in action_input1:
                    del action_input1["poiTypeBig"]


                print('action_input1 ', action_input1)


                
                # 更新action_input字符串
                action_input = json.dumps(action_input1, ensure_ascii=False)

                print('action_input ', action_input)
                start_time = time.time()


                poi_list = requests.post(url, headers=headers, data=action_input)




                end_time = time.time()
                elapsed_time = end_time - start_time

                print(f"+++++++++++++++++++requests函数调用持续时间: {elapsed_time:.2f} 秒")



                response_json=poi_list.json()
                # print('response_json ', response_json)

                # import pdb;pdb.set_trace()


                if 'data' in response_json:
                    json_data=poi_list.json()['data']

                    if json_data['total']>5:
                        json_data={
                            "total":5,
                            "dataList":json_data["dataList"][:5]
                        }
                            
                    last_list=json_data
                    data_str = json.dumps(json_data, ensure_ascii=False)
                
                    # 调用call_tool_chat函数
                    # chat_result = call_tool_chat([first_str], [queries], model_name)
                    
                    # # 将chat_result添加到queries中
                    # queries += f"###Action_output:{{{chat_result}}}"



                    # data_str = data_str.replace("\’", "'").replace("\n", " ").replace("\t", " ")

                else:
                    data_str="[]"



                

                # queries= queries+   '###Action_output:{'+ data_str +'}'

                queries= history+   '###Action_output:{'+ data_str +'}'

                history= history+   '###Action_output:{'+ '已忽略' +'}'




            elif 'navigation' in action_:
                
                queries= queries+  '###Action_output:{'+ '已导航' +'}'


                query = input("用户: ")
                queries= queries+ "###用户: "+query


            elif 'sort' in action_:


                
                sort_key=action_input1['method']
                reverse_bool=True if "descending" in action_input1['order'] else False

                # 当sort_key为'cost'时，如果取不到值，设置一个很大的默认值
                if sort_key == 'cost':
                    default_value = float('inf')  # 使用无穷大作为默认值
                    last_list['dataList'] = sorted(last_list['dataList'], key=lambda x: float(x.get(sort_key, default_value)), reverse=reverse_bool)
                else:
                    # 对于其他sort_key，保持原有的排序逻辑
                    last_list['dataList'] = sorted(last_list['dataList'], key=lambda x: float(x.get(sort_key, 0)), reverse=reverse_bool)
                
                

                json_string = json.dumps(last_list, ensure_ascii=False)

                # queries= queries+  '###Action_output:{'+ json_string +'}'

                queries= history+   '###Action_output:{'+ json_string +'}'

                history= history+   '###Action_output:{'+ '已忽略' +'}'


            elif 'filter_chat' in action_:

                if not last_list or not last_list.get('dataList'):
                    queries = history + '###Action_output:{' + '无需过滤' + '}'
                    history = history + '###Action_output:{' + '无需过滤' + '}'
                    continue
                poi_list = last_list['dataList']

                # import pdb;pdb.set_trace()

                filter_keyword = action_input1['keyword']
                import time
                start_time = time.time()
                filter_list = call_tool_filter([poi_list], filter_keyword, model_name)
                end_time = time.time()
                print(f"过滤操作执行时间: {end_time - start_time:.2f} 秒")

                # import pdb;pdb.set_trace()

                filter_list = filter_list.strip('```').strip('json')

                filter_list = json.loads(filter_list)
                delete_list = [int(index) for index in filter_list['filter_id']]

                # delete_list=action_input1['delete_list']

                last_list['total'] -= len(delete_list)

                last_list['dataList']=delete_by_indices(last_list['dataList'] , delete_list)

                json_string = json.dumps(last_list, ensure_ascii=False)


                print('recommend ', filter_list['recommend'])


                # queries= queries+  '###Action_output:{'+ json_string +'}'

                queries= history+   '###Action_output:{'+ json_string +'}'

                history= history+   '###Action_output:{'+ '已忽略' +'}'

        


            elif 'response' in action_:

                print(' ')

                print('LLM.response:', phrase_result["Params"])
                # queries= queries+  '###Action_output:{'+ 'OK' +'}'


                query = input("用户: ")
                # queries= queries+ "###用户: "+query
                queries= queries+  '###Action_output:{'+ "###用户: "+query +'}'


        print('action 搞完 last_list ', last_list)

        query = input("用户: ")
        # queries= queries+ "###用户: "+query
        queries= queries+  '###Action_output:{'+ "###用户: "+query +'}'       


if __name__ == "__main__":
    asyncio.run(main())
