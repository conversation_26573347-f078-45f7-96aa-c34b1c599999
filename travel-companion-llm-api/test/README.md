# Travel Companion LLM API 测试套件

## 概述

这个目录包含了 Travel Companion LLM API 的测试套件，主要用于冒烟测试和CI/CD流程。

## 文件说明

### 测试文件

- **`smoke_test.py`** - 主要的冒烟测试脚本
  - 包含9个测试用例，覆盖所有主要功能
  - 支持生成JUnit XML格式的测试报告
  - 可以单独运行或集成到CI/CD流程中

- **`run_service_and_smoke_test.sh`** - 服务启动和测试运行脚本
  - 自动检查服务状态
  - 如果需要，启动服务
  - 等待服务就绪
  - 运行冒烟测试
  - 清理资源

### 测试内容

1. **服务连通性测试** - 验证服务基本连接
2. **健康检查测试** - 测试健康检查端点
3. **版本信息测试** - 验证版本信息端点
4. **聊天接口测试** - 测试基本聊天功能
5. **无关问题识别测试** - 验证无关问题识别能力
6. **流式响应测试** - 测试流式聊天功能
7. **参数边界测试** - 验证各种参数边界条件
8. **性能测试** - 5轮性能测试，包含详细的模块耗时分析
9. **错误恢复测试** - 测试服务错误恢复能力

## 使用方法

### 本地运行

#### 方法1: 使用集成脚本（推荐）

```bash
# 进入测试目录
cd test

# 运行服务启动和冒烟测试
./run_service_and_smoke_test.sh

# 或指定服务地址
SERVICE_URL=http://localhost:8080 ./run_service_and_smoke_test.sh
```

#### 方法2: 单独运行冒烟测试

```bash
# 确保服务已启动
cd test

# 运行冒烟测试
python3 smoke_test.py

# 或指定服务地址
python3 smoke_test.py http://localhost:8080
```

### CI/CD集成

测试套件已集成到GitLab CI流程中：

1. **冒烟测试阶段** (`smoke_test`) - 在所有其他阶段之前运行
2. 只有冒烟测试通过后，才会执行后续的构建和部署阶段
3. 测试失败会阻止整个流水线继续执行

## 环境要求

### Python依赖

- Python 3.6+
- requests库

### 系统依赖（CI环境）

- curl（用于健康检查）
- procps（用于进程管理）

## 配置选项

### 环境变量

- `SERVICE_PORT` - 服务端口（默认: 8080）
- `SERVICE_HOST` - 服务主机（默认: 127.0.0.1）
- `MAX_WAIT_TIME` - 最大等待时间（默认: 120秒）
- `HEALTH_CHECK_INTERVAL` - 健康检查间隔（默认: 5秒）

### 示例

```bash
# 自定义配置运行测试
SERVICE_PORT=9000 \
SERVICE_HOST=localhost \
MAX_WAIT_TIME=180 \
./run_service_and_smoke_test.sh
```

## 输出说明

### 测试结果

- ✅ PASS - 测试通过
- ❌ FAIL - 测试失败
- 每个测试显示耗时和详细信息

### 性能测试报告

包含详细的性能分析：
- 总体响应时间统计
- 各模块平均耗时（中文显示）
- 成功率统计

### 生成的文件

- `smoke_test_results.xml` - JUnit格式的测试报告
- `service.log` - 服务运行日志（如果启动了服务）

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 查看service.log了解详细错误
   - 确认依赖包是否安装完整

2. **测试超时**
   - 增加MAX_WAIT_TIME
   - 检查网络连接
   - 确认服务配置正确

3. **依赖缺失**
   - 安装Python依赖: `pip install requests`
   - 确认Python版本 >= 3.6

### 调试建议

1. 查看详细日志
2. 单独运行各个测试方法
3. 检查服务健康状态
4. 验证网络连接

## 扩展

### 添加新测试

1. 在`TravelCompanionSmokeTest`类中添加新的测试方法
2. 方法名以`test_`开头
3. 在`run_smoke_tests()`中添加到测试列表
4. 更新文档说明

### 自定义报告

可以修改`generate_junit_xml()`方法来自定义测试报告格式。

## 版本信息

- **创建时间**: 2025年
- **适用服务**: Travel Companion LLM API v2
- **CI/CD**: GitLab CI集成
