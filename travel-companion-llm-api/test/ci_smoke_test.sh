#!/bin/bash

# CI环境专用的冒烟测试脚本
# 简化版本，专门针对Docker CI环境优化

set -e

# 配置参数
SERVICE_URL=${SERVICE_URL:-"http://127.0.0.1:8080"}
MAX_WAIT_TIME=${MAX_WAIT_TIME:-60}
HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-3}

echo "🧪 CI环境冒烟测试"
echo "=================="
echo "服务地址: $SERVICE_URL"
echo "最大等待时间: ${MAX_WAIT_TIME}秒"
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# 检查Python环境
echo "🔍 检查Python环境..."
python3 --version
pip3 --version

# 安装依赖（如果需要）
echo "📦 确保依赖包已安装..."
pip3 install requests 2>/dev/null || echo "requests already installed"

# 函数：检查服务健康状态
check_service_health() {
    local health_url="${SERVICE_URL}/gac/travel-companion/health"
    
    python3 -c "
import requests
import sys
try:
    response = requests.get('$health_url', timeout=5)
    if response.status_code == 200:
        data = response.json()
        if data.get('status') == 'up':
            print('✅ 服务健康检查通过')
            sys.exit(0)
        else:
            print('❌ 服务状态异常:', data.get('status', 'unknown'))
    else:
        print('❌ 健康检查失败，状态码:', response.status_code)
    sys.exit(1)
except requests.exceptions.ConnectionError:
    print('❌ 无法连接到服务')
    sys.exit(1)
except Exception as e:
    print('❌ 健康检查异常:', str(e))
    sys.exit(1)
"
    return $?
}

# 等待服务就绪
echo "⏳ 等待服务就绪..."
wait_time=0
service_ready=false

while [ $wait_time -lt $MAX_WAIT_TIME ]; do
    if check_service_health; then
        service_ready=true
        break
    fi
    
    echo "   等待中... (${wait_time}/${MAX_WAIT_TIME}秒)"
    sleep $HEALTH_CHECK_INTERVAL
    wait_time=$((wait_time + HEALTH_CHECK_INTERVAL))
done

if [ "$service_ready" = false ]; then
    echo "❌ 服务未就绪，等待超时"
    echo ""
    echo "📋 尝试显示服务信息:"
    
    # 显示进程信息
    echo "运行中的Python进程:"
    ps aux | grep python || echo "未找到Python进程"
    
    # 显示网络端口
    echo "监听的端口:"
    netstat -tlnp 2>/dev/null | grep :8080 || echo "端口8080未监听"
    
    # 显示服务日志
    if [ -f "../service.log" ]; then
        echo "服务日志（最后20行）:"
        tail -20 ../service.log
    elif [ -f "service.log" ]; then
        echo "服务日志（最后20行）:"
        tail -20 service.log
    else
        echo "未找到服务日志文件"
    fi
    
    exit 1
fi

echo ""
echo "🧪 开始运行冒烟测试..."

# 运行冒烟测试
if [ -f "smoke_test.py" ]; then
    echo "🧪 运行完整冒烟测试..."
    python3 smoke_test.py "$SERVICE_URL"
    test_result=$?

    # 如果完整测试失败，尝试快速测试
    if [ $test_result -ne 0 ] && [ -f "quick_smoke_test.py" ]; then
        echo ""
        echo "⚠️ 完整冒烟测试失败，尝试快速测试..."
        python3 quick_smoke_test.py "$SERVICE_URL"
        test_result=$?
    fi
else
    echo "❌ 错误: 未找到冒烟测试文件 smoke_test.py"
    exit 1
fi

echo ""
echo "⏰ 结束时间: $(date '+%Y-%m-%d %H:%M:%S')"

if [ $test_result -eq 0 ]; then
    echo "🎉 冒烟测试全部通过！"
    exit 0
else
    echo "❌ 冒烟测试失败"
    exit 1
fi
