# coding: utf-8
from .client import Client
import sensenova

key_list = [
    {
        "access_key_id" : "E206DF16D0BC4B18B608659A882CF5C7",  # 王宇航的nova账号
        "secret_access_key" : "92037E83B9DE49F8A421553A1E143064",  # 王宇航的nova账号
    },
]

class SenseNovaClient_KEY(Client):
    def __init__(self, key_index) -> None:
        super().__init__()
        self.key_index = key_index

    def call(self, messages, t):
        access_key_id = key_list[self.key_index]["access_key_id"]
        secret_access_key = key_list[self.key_index]["secret_access_key"]
        response = sensenova.ChatCompletion.create(
            # messages=[{"role": "user", "content": "你好"}],
            messages = messages,
            model='SenseChat-5',
            access_key_id=access_key_id,
            secret_access_key=secret_access_key,
            stream = False,
            max_new_tokens=512,  # SenseChat-5支持的模型输出的最大token长度
            n=1,  # 生成回复数量，响应参数中的index即为回复序号 范围 [1,4]
            repetition_penalty=1.05,  # 重复惩罚系数，1代表不惩罚，大于1倾向于生成不重复token，小于1倾向于生成重复token，推荐使用范围为[1,1.2] 默认1.05
            temperature=t,  # 温度采样参数，大于1的值倾向于生成更加多样的回复，小于1倾向于生成更加稳定的回复（最多支持小数点后六位） 范围 (0,2] 默认 0.8
            top_p = 0.7, # 核采样参数，解码生成token时，在概率和大于等于top_p的最小token集合中进行采样 范围 (0,1) 默认0.7
            plugins={
            "web_search": {
                "search_enable": True,
                "result_enable": False
            },
        }
        )
        return self.api_extract(response)

    def api_extract(self, response):
        resp_text = response.data.choices[0].message
        return resp_text

if __name__ == "__main__":
    chatbot = SenseNovaClient_KEY("0")
