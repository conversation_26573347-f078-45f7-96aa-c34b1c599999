from distutils.version import StrictVersion

import openai
from loguru import logger

from .client import Client

if StrictVersion(openai.__version__) < StrictVersion("1.0"):
    from openai import ChatCompletion

    def create_chat_completion(messages, temperature, model, api_key, base_url):
        return ChatCompletion.create(
            model=model,
            messages=messages,
            temperature=temperature,
            api_key=api_key,
            api_base=base_url
        )
else:
    from openai import OpenAI

    def create_chat_completion(messages, temperature, model, api_key, base_url):
        return OpenAI(
            api_key=api_key,
            base_url=base_url
        ).chat.completions.create(
            model=model,
            messages=messages,
            temperature=temperature
        )


OPENAI_KEY = "sk-Z27Z8xLx5zoysNxL36B1D6FfC96d4a69A73cC13bC0FeBbB3"
HOSTNAME = "***********"
PORT = "8457"


class OpenAIClient(Client):
    def __init__(self, hostname=None, port=None) -> None:
        super().__init__()
        self.hostname = hostname or HOSTNAME
        self.port = port or PORT

    def call(self, messages, t=0.5):
        try:
            model = "gpt-4o"
            api_key = OPENAI_KEY
            base_url = f"http://{self.hostname}:{self.port}/v1"
            response = create_chat_completion(messages, t, model, api_key, base_url)
            ans = response.choices[0].message.content
        except Exception as e:
            logger.debug(e)
            ans = 'request failed'
        return ans
