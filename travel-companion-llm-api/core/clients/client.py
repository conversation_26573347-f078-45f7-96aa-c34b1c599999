class Client:
    def __call__(self, messages, t=0.3):
        if isinstance(messages, str):
            messages = [{"role": "user", "content": messages}]
        elif isinstance(messages, list):
            if isinstance(messages[0], str):
                messages = [{"role": "user", "content": m} for m in messages]
            else:
                assert isinstance(messages[0], dict)

            action = self.call(messages, t=t)
            return action, ""

        return self.call(messages, t=t)

    def call(self, *args, **kwargs):
        raise NotImplementedError
