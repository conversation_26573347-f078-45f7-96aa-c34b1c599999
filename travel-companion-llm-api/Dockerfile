FROM python:3.11

WORKDIR /code/app

COPY ./requirements.txt /code/requirements.txt

RUN pip install --no-cache-dir --upgrade -r /code/requirements.txt

COPY ./src /code/app/src

COPY ./core /code/app/core
COPY ./main.py /code/app/main.py
COPY ./start_up.sh /code/app/start_up.sh
COPY ./configuration.py /code/app/configuration.py
COPY ./env.yaml /code/app/env.yaml

RUN chmod +x /code/app/start_up.sh

expose 8080

CMD ["./start_up.sh"]