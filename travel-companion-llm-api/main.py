import asyncio
import json
import logging
import re
import threading
import uuid
from argparse import ArgumentParser
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import Generator, AsyncGenerator
from typing import Union, Optional
import sys

# Python 3.9兼容性：Self在3.11+才可用
if sys.version_info >= (3, 11):
    from typing import Self
else:
    try:
        from typing_extensions import Self
    except ImportError:
        # 如果没有typing_extensions，使用TypeVar作为替代
        from typing import TypeVar
        Self = TypeVar('Self')
import time

import requests
import uvicorn
from fastapi import FastAPI, Header
from fastapi.responses import StreamingResponse
from loguru import logger
from pydantic import BaseModel

from src.stage0_agent_intent import call_intent_agent
from src.stage1_classifier import (
    call_classifier_big,
    call_classifier_mid,
    call_classifier_sub
)
from src.stage1_rewriter import call_rewriter
from src.tool_filter import call_tool_filter
from src.utiles.utiles import *
from src.v8_010.intent_agent import intent_agent, IntentInput
from src.v8_010.poi_assistant import Assistant_010
from src.v8_020.poi_assistant import Assistant_020
from src.version import __version__
import configuration
import ast

from src.utiles.constants import *

global_config = configuration.config

poi_type_str = """
{
  "汽车服务": {
    "汽车服务相关": ["汽车服务相关"],
    "加油站": ["加油站", "中国石化", "中国石油", "壳牌", "美孚", "加德士", "东方", "中石油碧辟", "中石化碧辟", "道达尔", "埃索", "中化道达尔"],
    "其它能源站": ["其它能源站"],
    "加气站": ["加气站"],
    "汽车养护/装饰": ["汽车养护", "加水站"],
    "洗车场": ["洗车场"],
    "汽车俱乐部": ["汽车俱乐部"],
    "汽车救援": ["汽车救援"],
    "汽车配件销售": ["汽车配件销售"],
    "汽车租赁": ["汽车租赁", "汽车租赁还车"],
    "二手车交易": ["二手车交易"],
    "充电站": ["充电站", "换电站", "充换电站", "专用充电站"]
  },
  "餐饮服务": {
    "餐饮相关场所": ["餐饮相关"],
    "中餐厅": ["中餐厅", "综合酒楼", "四川菜(川菜)", "广东菜(粤菜)", "山东菜(鲁菜)", "江苏菜", "浙江菜", "上海菜", "湖南菜(湘菜)", "安徽菜(徽菜)", "福建菜", "北京菜", "湖北菜(鄂菜)", "东北菜", "云贵菜", "西北菜", "老字号", "火锅店", "特色/地方风味餐厅", "海鲜酒楼", "中式素菜馆", "清真菜馆", "台湾菜", "潮州菜"],
    "外国餐厅": ["外国餐厅", "西餐厅(综合风味)", "日本料理", "韩国料理", "法式菜品餐厅", "意式菜品餐厅", "泰国/越南菜品餐厅", "地中海风格菜品", "美式风味", "印度风味", "英国式菜品餐厅", "牛扒店(扒房)", "俄国菜", "葡国菜", "德国菜", "巴西菜", "墨西哥菜", "其它亚洲菜"],
    "快餐厅": ["快餐厅", "肯德基", "麦当劳", "必胜客", "永和豆浆", "茶餐厅", "大家乐", "大快活", "美心", "吉野家", "仙跡岩", "呷哺呷哺"],
    "休闲餐饮场所": ["休闲餐饮场所"],
    "咖啡厅": ["咖啡厅", "星巴克咖啡", "上岛咖啡", "Pacific CoffeeCompany", "巴黎咖啡店"],
    "茶艺馆": ["茶艺馆"],
    "冷饮店": ["冷饮店"],
    "糕饼店": ["糕饼店"],
    "甜品店": ["甜品店"]
  },
  "购物服务": {
    "购物相关场所": ["购物相关场所"],
    "商场": ["商场", "购物中心", "普通商场", "免税品店"],
    "便民商店/便利店": ["便民商店/便利店", "7-ELEVEn便利店", "OK便利店"],
    "家电电子卖场": ["家电电子卖场", "综合家电商场", "国美", "大中", "苏宁", "手机销售", "数码电子", "丰泽", "苏宁镭射"],
    "超级市场": ["超市", "家乐福", "沃尔玛", "华润", "北京华联", "上海华联", "麦德龙", "乐天玛特", "华堂", "卜蜂莲花", "屈臣氏", "惠康超市", "百佳超市", "万宁超市"],
    "花鸟鱼虫市场": ["花鸟鱼虫市场", "花卉市场", "宠物市场"],
    "家居建材市场": ["家居建材市场", "家具建材综合市场", "家具城", "建材五金市场", "厨卫市场", "布艺市场", "灯具瓷器市场"],
    "综合市场": ["综合市场", "小商品市场", "旧货市场", "农副产品市场", "果品市场", "蔬菜市场", "水产海鲜市场"],
    "文化用品店": ["文化用品店"],
    "体育用品店": ["体育用品店", "李宁专卖店", "耐克专卖店", "阿迪达斯专卖店", "锐步专卖店", "彪马专卖店", "高尔夫用品店", "户外用品"],
    "特色商业街": ["特色商业街", "步行街"],
    "服装鞋帽皮具店": ["服装鞋帽皮具店", "品牌服装店", "品牌鞋店", "品牌皮具店", "品牌箱包店"],
    "专卖店": ["专营店", "古玩字画店", "珠宝首饰工艺品", "钟表店", "眼镜店", "书店", "音像店", "儿童用品店", "自行车专卖店", "礼品饰品店", "烟酒专卖店", "宠物用品店", "摄影器材店", "宝马生活方式", "土特产专卖店"],
    "特殊买卖场所": ["特殊买卖场所", "拍卖行", "典当行"],
    "个人用品/化妆品店": ["其它个人用品店", "莎莎"]
  },
  "交通设施服务": {
    "交通服务相关": ["交通服务相关"],
    "机场相关": ["机场相关", "候机室", "摆渡车站", "飞机场", "机场出发/到达", "直升机场", "机场货运处"],
    "火车站": ["火车站", "候车室", "进站口/检票口", "出站口", "站台", "售票", "退票", "改签", "公安制证", "票务相关", "货运火车站"],
    "港口码头": ["港口码头", "客运港", "车渡口", "人渡口", "货运港口码头", "进港", "出港", "候船室"],
    "长途汽车站": ["长途汽车站", "进站", "出站", "候车室"],
    "地铁站": ["地铁站", "出入口"],
    "轻轨站": ["轻轨站"],
    "公交车站": ["公交车站相关", "旅游专线车站", "普通公交站", "机场巴士", "快速公交站", "电车站", "智轨车站"],
    "班车站": ["班车站"],
    "停车场": ["停车场相关", "换乘停车场", "公共停车场", "专用停车场", "路边停车场", "停车场入口", "停车场出口", "停车场出入口"]
  },
  "体育休闲服务": {
    "影剧院": ["电影院"]
  },
  "住宿服务": {
    "住宿服务相关": ["住宿服务相关"],
    "宾馆酒店": ["宾馆酒店", "奢华酒店", "五星级宾馆", "四星级宾馆", "三星级宾馆", "经济型连锁酒店"],
    "旅馆招待所": ["旅馆招待所", "青年旅舍"]
  },
  "风景名胜": {
    "风景名胜相关": ["旅游景点"],
    "公园广场": ["公园广场", "公园", "动物园", "植物园", "水族馆", "城市广场", "公园内部设施"],
    "风景名胜": ["风景名胜", "世界遗产", "国家级景点", "省级景点", "纪念馆", "寺庙道观", "教堂", "回教寺", "海滩", "观景点", "红色景区"]
  }
}
"""

retry_count = 0

first_level_list = ["汽车服务", "餐饮服务", "购物服务", "交通设施服务", "体育休闲服务", "住宿服务", "风景名胜"]
second_level_dict = {
    '汽车服务': ['加油站', '其它能源站', '加气站', '汽车养护/装饰', '洗车场', '汽车俱乐部', '汽车救援', '汽车配件销售',
                 '汽车租赁', '二手车交易', '充电站'],
    '餐饮服务': ['中餐厅', '外国餐厅', '快餐厅', '休闲餐饮场所', '咖啡厅', '茶艺馆', '冷饮店', '糕饼店', '甜品店'],
    '购物服务': ['商场', '便民商店/便利店', '家电电子卖场', '超级市场', '花鸟鱼虫市场', '家居建材市场', '综合市场',
                 '文化用品店', '体育用品店', '特色商业街', '服装鞋帽皮具店', '专卖店', '特殊买卖场所',
                 '个人用品/化妆品店'],
    '交通设施服务': ['机场相关', '火车站', '港口码头', '长途汽车站', '地铁站', '轻轨站', '公交车站', '班车站',
                     '停车场'],
    '体育休闲服务': ['影剧院'],
    '住宿服务': ['宾馆酒店', '旅馆招待所'], '风景名胜': ['公园广场', '风景名胜']
}
third_level_dict = {
    '加油站': ['加油站', '中国石化', '中国石油', '壳牌', '美孚', '加德士', '东方', '中石油碧辟', '中石化碧辟', '道达尔',
               '埃索', '中化道达尔'],
    '其它能源站': ['其它能源站'],
    '加气站': ['加气站'],
    '汽车养护/装饰': ['汽车养护', '加水站'],
    '洗车场': ['洗车场'],
    '汽车俱乐部': ['汽车俱乐部'],
    '汽车救援': ['汽车救援'],
    '汽车配件销售': ['汽车配件销售'],
    '汽车租赁': ['汽车租赁', '汽车租赁还车'],
    '二手车交易': ['二手车交易'],
    '充电站': ['充电站', '换电站', '充换电站', '专用充电站'],
    '中餐厅': ['中餐厅', '综合酒楼', '四川菜(川菜)', '广东菜(粤菜)', '山东菜(鲁菜)', '江苏菜', '浙江菜', '上海菜',
               '湖南菜(湘菜)', '安徽菜(徽菜)', '福建菜', '北京菜', '湖北菜(鄂菜)', '东北菜', '云贵菜', '西北菜',
               '老字号', '火锅店', '特色/地方风味餐厅', '海鲜酒楼', '中式素菜馆', '清真菜馆', '台湾菜', '潮州菜'],
    '外国餐厅': ['外国餐厅', '西餐厅(综合风味)', '日本料理', '韩国料理', '法式菜品餐厅', '意式菜品餐厅',
                 '泰国/越南菜品餐厅', '地中海风格菜品', '美式风味', '印度风味', '英国式菜品餐厅', '牛扒店(扒房)',
                 '俄国菜', '葡国菜', '德国菜', '巴西菜', '墨西哥菜', '其它亚洲菜'],
    '快餐厅': ['快餐厅', '肯德基', '麦当劳', '必胜客', '永和豆浆', '茶餐厅', '大家乐', '大快活', '美心', '吉野家',
               '仙跡岩', '呷哺呷哺'],
    '休闲餐饮场所': ['休闲餐饮场所'],
    '咖啡厅': ['咖啡厅', '星巴克咖啡', '上岛咖啡', 'Pacific CoffeeCompany', '巴黎咖啡店'],
    '茶艺馆': ['茶艺馆'],
    '冷饮店': ['冷饮店'],
    '糕饼店': ['糕饼店'],
    '甜品店': ['甜品店'],
    '商场': ['购物中心', '普通商场', '免税品店'],
    '便民商店/便利店': ['7-ELEVEn便利店', 'OK便利店'],
    '家电电子卖场': ['综合家电商场', '国美', '大中', '苏宁', '手机销售', '数码电子', '丰泽', '苏宁镭射'],
    '超级市场': ['超市', '家乐福', '沃尔玛', '华润', '北京华联', '上海华联', '麦德龙', '乐天玛特', '华堂', '卜蜂莲花',
                 '屈臣氏', '惠康超市', '百佳超市', '万宁超市'],
    '花鸟鱼虫市场': ['花卉市场', '宠物市场'],
    '家居建材市场': ['家具建材综合市场', '家具城', '建材五金市场', '厨卫市场', '布艺市场', '灯具瓷器市场'],
    '综合市场': ['小商品市场', '旧货市场', '农副产品市场', '果品市场', '蔬菜市场', '水产海鲜市场'],
    '文化用品店': ['文化用品店'],
    '体育用品店': ['李宁专卖店', '耐克专卖店', '阿迪达斯专卖店', '锐步专卖店', '彪马专卖店', '高尔夫用品店',
                   '户外用品'],
    '特色商业街': ['步行街'],
    '服装鞋帽皮具店': ['品牌服装店', '品牌鞋店', '品牌皮具店', '品牌箱包店'],
    '专卖店': ['专营店', '古玩字画店', '珠宝首饰工艺品', '钟表店', '眼镜店', '书店', '音像店', '儿童用品店',
               '自行车专卖店', '礼品饰品店', '烟酒专卖店', '宠物用品店', '摄影器材店', '宝马生活方式', '土特产专卖店'],
    '特殊买卖场所': ['拍卖行', '典当行'],
    '个人用品/化妆品店': ['其它个人用品店', '莎莎'],
    '机场相关': ['候机室', '摆渡车站', '飞机场', '机场出发/到达', '直升机场', '机场货运处'],
    '火车站': ['候车室', '进站口/检票口', '出站口', '站台', '售票', '退票', '改签', '公安制证', '票务相关',
               '货运火车站'],
    '港口码头': ['客运港', '车渡口', '人渡口', '货运港口码头', '进港', '出港', '候船室'],
    '长途汽车站': ['进站', '出站', '候车室'],
    '地铁站': ['出入口'],
    '轻轨站': ['轻轨站'],
    '公交车站': ['公交车站相关', '旅游专线车站', '普通公交站', '机场巴士', '快速公交站', '电车站', '智轨车站'],
    '班车站': ['班车站'],
    '停车场': ['停车场相关', '换乘停车场', '公共停车场', '专用停车场', '路边停车场', '停车场入口', '停车场出口',
               '停车场出入口'],
    '影剧院': ['电影院'],
    '宾馆酒店': ['奢华酒店', '五星级宾馆', '四星级宾馆', '三星级宾馆', '经济型连锁酒店'],
    '旅馆招待所': ['青年旅舍'],
    '公园广场': ['公园', '动物园', '植物园', '水族馆', '城市广场', '公园内部设施'],
    '风景名胜': ['世界遗产', '国家级景点', '省级景点', '纪念馆', '寺庙道观', '教堂', '回教寺', '海滩', '观景点',
                 '红色景区']
}

sec_trd_level_dict = {
    '汽车服务': ['汽车服务相关', '加油站', '中国石化', '中国石油', '壳牌', '美孚', '加德士', '东方', '中石油碧辟',
                 '中石化碧辟', '道达尔', '埃索', '中化道达尔', '其它能源站', '加气站', '汽车养护', '加水站', '洗车场',
                 '汽车俱乐部', '汽车救援', '汽车配件销售', '汽车租赁', '汽车租赁还车', '二手车交易', '充电站', '换电站',
                 '充换电站', '专用充电站'],
    '餐饮服务': ['中餐厅', '综合酒楼', '四川菜(川菜)', '广东菜(粤菜)', '山东菜(鲁菜)', '江苏菜', '浙江菜', '上海菜',
                 '湖南菜(湘菜)', '安徽菜(徽菜)', '福建菜', '北京菜', '湖北菜(鄂菜)', '东北菜', '云贵菜', '西北菜',
                 '老字号', '火锅店', '特色/地方风味餐厅', '海鲜酒楼', '中式素菜馆', '清真菜馆', '台湾菜', '潮州菜',
                 '外国餐厅', '西餐厅(综合风味)', '日本料理', '韩国料理', '法式菜品餐厅', '意式菜品餐厅',
                 '泰国/越南菜品餐厅', '地中海风格菜品', '美式风味', '印度风味', '英国式菜品餐厅', '牛扒店(扒房)',
                 '俄国菜', '葡国菜', '德国菜', '巴西菜', '墨西哥菜', '其它亚洲菜', '快餐厅', '肯德基', '麦当劳',
                 '必胜客', '永和豆浆', '茶餐厅', '大家乐', '大快活', '美心', '吉野家', '仙跡岩', '呷哺呷哺',
                 '休闲餐饮场所', '咖啡厅', '星巴克咖啡', '上岛咖啡', 'Pacific CoffeeCompany', '巴黎咖啡店', '茶艺馆',
                 '冷饮店', '糕饼店', '甜品店'],
    '购物服务': ['购物相关场所', '商场', '购物中心', '普通商场', '免税品店', '便民商店/便利店', '7-ELEVEn便利店',
                 'OK便利店', '家电电子卖场', '综合家电商场', '国美', '大中', '苏宁', '手机销售', '数码电子', '丰泽',
                 '苏宁镭射', '超市', '家乐福', '沃尔玛', '华润', '北京华联', '上海华联', '麦德龙', '乐天玛特', '华堂',
                 '卜蜂莲花', '屈臣氏', '惠康超市', '百佳超市', '万宁超市', '花鸟鱼虫市场', '花卉市场', '宠物市场',
                 '家居建材市场', '家具建材综合市场', '家具城', '建材五金市场', '厨卫市场', '布艺市场', '灯具瓷器市场',
                 '综合市场', '小商品市场', '旧货市场', '农副产品市场', '果品市场', '蔬菜市场', '水产海鲜市场',
                 '文化用品店', '体育用品店', '李宁专卖店', '耐克专卖店', '阿迪达斯专卖店', '锐步专卖店', '彪马专卖店',
                 '高尔夫用品店', '户外用品', '特色商业街', '步行街', '服装鞋帽皮具店', '品牌服装店', '品牌鞋店',
                 '品牌皮具店', '品牌箱包店', '专营店', '古玩字画店', '珠宝首饰工艺品', '钟表店', '眼镜店', '书店',
                 '音像店', '儿童用品店', '自行车专卖店', '礼品饰品店', '烟酒专卖店', '宠物用品店', '摄影器材店',
                 '宝马生活方式', '土特产专卖店', '特殊买卖场所', '拍卖行', '典当行', '其它个人用品店', '莎莎'],
    '交通设施服务': ['交通服务相关', '机场相关', '候机室', '摆渡车站', '飞机场', '机场出发/到达', '直升机场',
                     '机场货运处', '火车站', '候车室', '进站口/检票口', '出站口', '站台', '售票', '退票', '改签',
                     '公安制证', '票务相关', '货运火车站', '港口码头', '客运港', '车渡口', '人渡口', '货运港口码头',
                     '进港', '出港', '候船室', '长途汽车站', '进站', '出站', '候车室', '地铁站', '出入口', '轻轨站',
                     '公交车站相关', '旅游专线车站', '普通公交站', '机场巴士', '快速公交站', '电车站', '智轨车站',
                     '班车站', '停车场相关', '换乘停车场', '公共停车场', '专用停车场', '路边停车场', '停车场入口',
                     '停车场出口', '停车场出入口'],
    '体育休闲服务': ['电影院'],
    '住宿服务': ['住宿服务相关', '宾馆酒店', '奢华酒店', '五星级宾馆', '四星级宾馆', '三星级宾馆', '经济型连锁酒店',
                 '旅馆招待所', '青年旅舍'],
    '风景名胜': ['旅游景点', '公园广场', '公园', '动物园', '植物园', '水族馆', '城市广场', '公园内部设施', '风景名胜',
                 '世界遗产', '国家级景点', '省级景点', '纪念馆', '寺庙道观', '教堂', '回教寺', '海滩', '观景点',
                 '红色景区']
}

class UserMemoryReq(BaseModel):
    car_id: str
    user_id: str
    category: list[str] = []

class LocationReq(BaseModel):
    lat: str
    lon: str

    def build_coordinate_text(self)-> Optional[str]:
        try:
            return format(float(self.lon), ".6f") + "," + format(float(self.lat), ".6f")
        except BaseException as e:
            print(e)
            return None



class TravelCompanionMsgIn(BaseModel):
    latitude: str
    longitude: str
    messages: list
    model_name: str = "qwen72b"


class TravelCompanionMsgOut(BaseModel):
    results: list
    thought: Union[str, None]
    success: bool


class TravelCompanionV8MsgIn(BaseModel):
    user_info: Optional[UserMemoryReq] = None
    location: LocationReq
    messages: list
    context: str = ""
    is_stream: bool = False
    model_name: str = "senseauto"
    rewrite_model_name: str = "senseauto"
    location_home: Optional[LocationReq] = None
    location_company: Optional[LocationReq] = None
    location_destination: Optional[LocationReq] = None

    def has_field(self, field_name: str) -> bool:
        # Pydantic v1
        return field_name in self.__fields_set__
        


class TravelCompanionV8VersionOut(BaseModel):
    version: str


class TravelCompanionV8MsgOut(BaseModel):
    poi_list: list
    recommend: str = ""
    context: str = ""
    track_id: str = ""
    tips: str = ""
    total_time: dict = {}
    success: bool
    code: int
    title: str = ""
    follow_ups: list[str] = []


class TravelCompanionV8MappingOut(BaseModel):
    mapping_out: list = []
    context: str = ""
    # poiType: list
    # latitude: str = ""
    # longitude: str = ""
    # poiName: list = []
    # page: int = 0
    # pageSize: int = 0
    # poiTags: list = []
    # filter: dict = {}
    # track_id: str = ""
    success: bool


class TravelCompanionFilterMsgIn(BaseModel):
    filter_keyword: list
    poi_list: list
    model_name: str = "qwen72b"


class TravelCompanionFilterMsgOut(BaseModel):
    filter_list: Union[dict, None]
    success: bool


class HealthMsgOut(BaseModel):
    version: str
    status: str


# # 自定义中间件，用于检测请求是否超时
# class TimeoutMiddleware(BaseHTTPMiddleware):
#     def __init__(self, app, timeout: int):
#         super().__init__(app)
#         self.timeout = timeout

#     async def dispatch(self, request: Request, call_next):
#         try:
#             # 使用 asyncio.wait_for 来限制每个请求的最大执行时间
#             return await asyncio.wait_for(call_next(request), timeout=self.timeout)
#         except asyncio.TimeoutError:
#             # 超时则返回自定义的错误信息
#             return JSONResponse(
#                 status_code=504,
#                 content={"detail": f"Request timed out after {self.timeout} seconds."},
#             )
class HealthCheckEndpointFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        return record.getMessage().find("/gac/travel-companion/health") == -1


# Filter out /endpoint
logging.getLogger("uvicorn.access").addFilter(HealthCheckEndpointFilter())

app = FastAPI(title="Travel Companion")


@app.on_event("startup")
async def startup_event():
    app.state.valid_group_id_list = []
    valid_group_id_config_str = os.environ.get('VALID_GROUP_ID_LIST', "")
    if valid_group_id_config_str is not None and len(valid_group_id_config_str) > 0:
        try:
            for item in valid_group_id_config_str.split(","):
                # print(item)
                app.state.valid_group_id_list.append(int(item))
        except ValueError as e:
            app.state.valid_group_id_list = []


async def get_class(queries, model_name="qwen72b"):
    try:
        class_res = call_classifier_big([queries], first_level_list, model_name)
    except:
        return "", "", ""

    try:
        first_class_type = json.loads(class_res)['Type']
    except:
        return "", "", ""
    print(f"first type: {first_class_type}")

    if first_class_type not in first_level_list:
        return "", "", ""

    # class_res = call_classifier_sub([queries], sec_trd_level_dict[first_class_type], model_name)
    # sec_trd_class_type = json.loads(class_res)['Type']
    # print(f"sec_trd type: {sec_trd_class_type}")

    try:
        class_res = call_classifier_mid([queries], second_level_dict[first_class_type], model_name)
    except:
        return first_class_type, "", ""

    try:
        second_class_type = json.loads(class_res)['Type']
    except:
        return first_class_type, "", ""
    print(f"second type: {second_class_type}")

    if second_class_type not in second_level_dict[first_class_type]:
        return first_class_type, "", ""

    if len(third_level_dict[second_class_type]) == 1:
        if third_level_dict[second_class_type][0] == second_class_type:
            return first_class_type, second_class_type, ""
        else:
            return first_class_type, second_class_type, third_level_dict[second_class_type][0]

    try:
        class_res = call_classifier_sub([queries], third_level_dict[second_class_type], model_name)
    except:
        return first_class_type, second_class_type, ""

    try:
        third_class_type = json.loads(class_res)['Type']
    except:
        return first_class_type, second_class_type, ""
    print(f"third type: {third_class_type}")

    if third_class_type not in third_level_dict[second_class_type]:
        return first_class_type, second_class_type, ""

    return first_class_type, second_class_type, third_class_type


def check_poi_type_validity(poi_type_big, poi_type_big_c, poi_type_mid, poi_type_sub):
    # import pdb;pdb.set_trace()
    if poi_type_big != poi_type_big_c:
        return False
    poi_type_dict = json.loads(poi_type_str)

    if poi_type_big in poi_type_dict:
        if not poi_type_mid and not poi_type_sub:
            return True
        if poi_type_mid in poi_type_dict[poi_type_big]:
            if not poi_type_sub:
                return True
            if poi_type_sub in poi_type_dict[poi_type_big][poi_type_mid]:
                return True
    return False


def get_and_check_json_output(json_data):
    brace_count = 0
    json_start = None

    json_results = []

    for i, char in enumerate(json_data):
        if char == '{':
            if brace_count == 0:
                json_start = i  # first {
            brace_count += 1
        elif char == '}':
            brace_count -= 1
            if brace_count == 0 and json_start is not None:
                json_str = json_data[json_start:i + 1]
                json_results.append(json_str)  # TODO: check format
                # except:
                #     print(f"Error decoding JSON: {e}")
                json_start = None

    return json_results


client_id_cache = {}


def _get_group_id_from_cache(client_id: str) -> Optional[str]:
    return client_id_cache.get(client_id, None)


def _insert_group_id_to_cache(client_id: str, group_id: str) -> None:
    client_id_cache[client_id] = group_id


def get_group_id(client_id):
    cached_group_id = _get_group_id_from_cache(client_id)
    if cached_group_id is not None:
        return cached_group_id

    headers = {'Content-Type': 'application/json', "client-id": client_id}
    post_body = {"client_id": client_id}
    group_id = None
    try:
        cabin_client_id_svc_host_variable = os.environ.get('CABIN_CLIENT_ID_SVC_HOST')
        if cabin_client_id_svc_host_variable is None or len(cabin_client_id_svc_host_variable) <= 0:
            cabin_client_id_svc_host_url = "http://cabin-client-id-java.autocloud-platform.svc:8089"
        else:
            cabin_client_id_svc_host_url = cabin_client_id_svc_host_variable

        #         NOVA_URL = "123"
        #         NOVA_URL = "https://acplatform.sensetime.com"
        #         response = requests.post(NOVA_URL+"/cabin-client-id/findUserGroup",
        response = requests.post(cabin_client_id_svc_host_url + "/cabin-client-id/findUserGroup",
                                 headers=headers,
                                 json=post_body)
        response.raise_for_status()
        res_obj = json.loads(response.text)
        if 200 == res_obj['code'] and res_obj['data']:
            group_id = str(res_obj['data'][0]['id'])
            _insert_group_id_to_cache(client_id, group_id)
        return group_id
    except Exception as e:
        print(f"Encounter error when fetching group id with: {str(e)}, the time is: {str(e)}")
        return None




def validate_client_id(client_id):
    group_id = get_group_id(client_id)
    print(group_id)
    print(app.state.valid_group_id_list)
    if group_id != None and int(group_id) in app.state.valid_group_id_list:
        return True
    
    return False

async def require_user_habit(source: Optional[UserMemoryReq], start_time_perf_counter: float, time_limit: float) -> [Optional[str], Optional[float]]:
    if not source or not source.car_id or not source.user_id or 0 >= len(source.category):
        return None, round(time.perf_counter() - start_time_perf_counter, 4)
    async def require_habit_from_cache(source: UserMemoryReq):
        body_json = {
            "car_id" : source.car_id,
            "face_id" : source.user_id,
            "categories": source.category,
        }
        from src.utiles.user_habit_util import request_habit_from_user_memory_text
        result_str = await request_habit_from_user_memory_text(body=body_json, relation_filter="LIKES")
        if not result_str:
            result_str = ""
        return result_str
    try:
        result = await asyncio.wait_for(require_habit_from_cache(source), timeout=time_limit)
        return result, round(time.perf_counter() - start_time_perf_counter, 4)
    except Exception as e:
        logger.error(f"require_user_habit timeout or other error: {e}")
        # 超时也视为网络错误
        error_result = {"code": error_code_mem_http_error, "response_str": "调用记忆体接口网络错误"}
        return error_result, round(time.perf_counter() - start_time_perf_counter, 4)


import copy
@app.post("/gac/travel-companion/v2/mapping", response_model=TravelCompanionV8MappingOut)
async def travel_companion_v8_010_mapping(item: TravelCompanionV8MsgIn, nova_ak=Header(None), nova_sk=Header(None),
                                          track_id=Header(None), client_id=Header(None)) -> TravelCompanionV8MappingOut:
    if validate_client_id(client_id) == False:
        return TravelCompanionV8MappingOut(success=False)

    nova_ak = "2VNRG84V6WdPX3hjiH70lJtXYYf"
    nova_sk = "UGUMIdOHCy8zsIAmQsWQBnYPmhBibb0i"
    messages = item.messages
    coordinate = {}
    coordinate["latitude"] = item.latitude
    coordinate["longitude"] = item.longitude
    assistant = Assistant_010()
    assistant.history.clear()

    try:
        if item.context:
            history_context = json.loads(item.context)
            for history in history_context:
                assistant.history.history.append(history)
    except:
        logger.info(f"track-id: {track_id} context parse error")

    for msg in messages:
        if msg['role'] == 'user':
            assistant.history.user_record(msg['content'])
        if msg['role'] == 'assistant':
            poi_info = {}
            poi_content_str = msg['content']
            try:
                poi_content = json.loads(poi_content_str)
                poi_info["dataList"] = poi_content['mList']
                assistant.history.save_poi_list(json.dumps(poi_info))
            except:
                info = {}
                info['action'] = "direct_response"
                info['params'] = {}
                info['response'] = poi_content_str
                assistant.history.intent_record(json.dumps(info))
    if track_id == None:
        track_id = str(uuid.uuid4().hex[:8])

    poi_mapping, poiTags, _ = assistant.handle_mapping_agent(coordinate=coordinate, nova_ak=nova_ak, nova_sk=nova_sk,
                                                             track_id=track_id)
    poi_mapping_json = json.loads(poi_mapping)

    poi_mapping_json["pageLimit"] = True
    otherpois_len = len(poi_mapping_json["filter"]["otherPois"])
    if 'poiArea' in poi_mapping_json.keys():
        Area_len = 1
    else:
        Area_len = 0

    if "poiTags" in poi_mapping_json.keys():
        poiTags = ','.join(poi_mapping_json["poiTags"])  # poiTags 用于过滤,多个tag应该至少满足一个。传入为string,','隔开
        poi_mapping_json["poiTags"] = poiTags

    poi_mapping_json["poiType"] = '|'.join(poi_mapping_json["poiType"])
    poi_mapping_json["poiName"] = ' '.join(poi_mapping_json["poiName"])

    ori_loads = [copy.deepcopy(poi_mapping_json)]
    otherpois2poiarea_id = []
    if Area_len==0 and otherpois_len>0:
        for id in range(otherpois_len):
            # print(otherpois_len)
            # print(id)
            oth = copy.deepcopy(poi_mapping_json["filter"]["otherPois"][:])
            # Area = oth[id]["keyword"]
            Area = oth.pop(id)["keyword"]

            payload_copy = copy.deepcopy(poi_mapping_json)

            payload_copy['poiArea'] = Area
            otherpois2poiarea_id.append(id+1)
            payload_copy["filter"]["otherPois"] = oth

            ori_loads.append(payload_copy)

    for ori_load in ori_loads:
        if 'city' not in ori_load and 'poiArea'  not in ori_load:
            if 'searchRadius' not in ori_load:
                ori_load['searchRadius'] = 50000

        #mapping有可能返回带有空字符德消息，我这里做过滤
        if 'poiName' in ori_load:
            ori_load['poiName'] = ori_load['poiName'].replace(" ", "")

    #print(f"track-id: {track_id} mapping_output: {ori_loads}")
    logger.info(f"track-id: {track_id} mapping_output: {ori_loads}")

    final_history = []
    for history in assistant.history.history:
        poi_search_result = history.get('action_type', "")
        # 如何历史上下文带有poi list结果信息，该信息删掉，因为客户端回传该信后，意图识别时用不到。
        if poi_search_result == "poi_search_result":
            continue
        final_history.append(history)
    # print(aaa)
    # poiType: list
    # latitude: str = ""
    # longitude: str = ""
    # poiName: list = ""
    # page: int
    # pageSize: int
    # filter: dict
    # success: bool
    return TravelCompanionV8MappingOut(mapping_out = ori_loads, context = json.dumps(final_history, ensure_ascii=False), 
                                       success=True)


@app.get("/gac/travel-companion/v2/version", response_model=TravelCompanionV8VersionOut)
def getVersion():
    return TravelCompanionV8VersionOut(version=__version__)


@app.post("/gac/travel-companion/v2/chat", response_model=TravelCompanionV8MsgOut)
async def travel_companion_v8_010_intent(item: TravelCompanionV8MsgIn, nova_ak=Header(None), nova_sk=Header(None),
                                   track_id=Header(None), client_id=Header(None)) -> TravelCompanionV8MsgOut:
    
    start_time_perf_counter = time.perf_counter()
    # if validate_client_id(client_id) == False:
    #     return TravelCompanionV8MsgOut(poi_list=[], action_type="", tips="client-id error",
    #                                    track_id="", recommend="", success=False, code=-1)
    if track_id == None:
        track_id = str(uuid.uuid4().hex[:8])

    nova_ak = "2VNRG84V6WdPX3hjiH70lJtXYYf"
    nova_sk = "UGUMIdOHCy8zsIAmQsWQBnYPmhBibb0i"
    messages = item.messages
    coordinate = {}
    coordinate["latitude"] = item.location.lat
    coordinate["longitude"] = item.location.lon
    from src.utiles.location_util import require_text_location
    logger.info("loc start")
    city, location_cost_time = await require_text_location(item.location, start_time_perf_counter, 0.5)
    logger.info(f"loc end-----cost time:{location_cost_time}-----location:{city}")
    logger.info("mem start")
    habit, habit_cost_time = await require_user_habit(item.user_info, start_time_perf_counter, 0.5)

    # 检查 habit 变量是否为一个错误字典
    if isinstance(habit, dict) and "code" in habit:
        error_code = habit.get("code")
        error_msg = habit.get("response_str", "调用记忆体服务时发生未知错误")
        
        # 统一处理流式和非流式返回
        if item.is_stream:
            # 构造一个流式的错误块
            error_chunk = {
                'type': 'error',
                'data': {
                    'track_id': track_id if track_id else "",
                    'tips': error_msg,
                    'code': error_code
                }
            }
            async def error_generator():
                yield "data: {}\n\n".format(json.dumps(error_chunk, ensure_ascii=False))
            return StreamingResponse(error_generator(), media_type="text/event-stream")
        else:
            # 构造一个非流式的错误返回体
            return TravelCompanionV8MsgOut(
                poi_list=[],
                tips=error_msg,
                track_id=track_id if track_id else "",
                success=False,
                code=error_code
            )

    logger.info(f"mem end-----cost time:{round(habit_cost_time-location_cost_time,4)}-----habit:{habit}")
    logger.info(f"city: {city} habit: {habit}")

    logger.info(
        f"track-id: {track_id} {item} nova_ak : {nova_ak} nova_sk : {nova_sk} threading_id: {threading.get_ident()}")
    assistant = Assistant_010(max_save_poi_list_num=2, max_save_history_num=20, poi_list_max_len=10, user_like=habit, city=city)
    assistant.history.clear()
    try:
        if item.context:
            history_context = json.loads(item.context)
            for history in history_context:
                assistant.history.history.append(history)
    except:
        logger.info(f"track-id: {track_id} context parse error")

    for msg in messages:
        if msg['role'] == 'user':
            assistant.history.user_record(msg['content'])
        else:
            print(f"error role in messages: {msg}")
    location_infos = {}
    if item.has_field("location_home"):
        location_infos["家"] = item.location_home
    if item.has_field("location_company"):
        location_infos["公司"] = item.location_company
    if item.has_field("location_destination"):
        location_infos["目的地"] = item.location_destination
    logger.info(f"track-id: {track_id} location_infos:{location_infos}")
    if item.is_stream == False:
        # try:
        newest_user_query, part_history = assistant.history.history_process()
        logger.info('model_name:{} rewrite_model_name:{} user query:{} part_history:{}'.format(item.model_name, item.rewrite_model_name, newest_user_query, part_history))
        response, _, duration_msg = assistant.handle_poi_search(newest_user_query, coordinate, nova_ak=nova_ak,
                                                                         nova_sk=nova_sk, model_name=item.model_name,
                                                                         rewrite_model_name=item.rewrite_model_name,
                                                                         start_time = start_time_perf_counter,
                                                                         is_stream=False, track_id=track_id,
                                                                         location_infos=location_infos)

        if response['code'] != 200:
            return TravelCompanionV8MsgOut(code=response['code'],
                                           poi_list=[],
                                           tips=response["response_str"],
                                           track_id=track_id, total_time=duration_msg, success=True)
        # except Exception as e:
        #     logger.info(f"track-id: {track_id} {str(e)}")
        #     return TravelCompanionV8MsgOut(poi_list=[], tips=str(e),
        #                                    track_id=track_id, recommend="", total_time=[], success=False)
        tips = response.get("tips", "").replace("**", " ")
        recommend = response.get("recommend", "").replace("**", " ")
        title = response.get("title", "").replace("**", " ")

        # 推荐问题
        follow_up_questions = []
        logger.info("follow_ups start")
        try:
            raw_question_response, fup_cost_time = await assistant.get_follow_up_questions(
                user_query=newest_user_query,
                assistant_response=recommend,
                nova_ak=nova_ak,
                nova_sk=nova_sk,
                model_name=item.rewrite_model_name
            )
            logger.info(f"follow_ups end-----cost time:{fup_cost_time}")

            # 检查是否返回了错误字典
            if isinstance(raw_question_response, dict) and "code" in raw_question_response:
                # 将错误码和错误信息一起追加到 tips 字段
                error_code = raw_question_response.get('code')
                error_str = raw_question_response.get('response_str', '未知错误')
                error_msg = f"推荐问题生成失败, 错误码: {error_code}, 错误信息: {error_str}"
                tips += error_msg
                logger.warning(f"track-id: {track_id} Failed to get follow-up questions: {error_msg}")
            elif raw_question_response:
                logger.info(f"follow_ups raw response: {raw_question_response}")
                match = re.search(r'\[.*?\]', raw_question_response)
                if match:
                    questions = ast.literal_eval(match.group())
                    if isinstance(questions, list):
                        follow_up_questions = questions
        except Exception as e:
            logger.error(f"Failed to get or parse follow-up questions: {e}")

        duration_msg["t6-follow-ups"] = fup_cost_time
        duration_msg.update({"total_time": time.perf_counter() - start_time_perf_counter})
        duration_msg["t1-loc"] = location_cost_time
        duration_msg["t2-mem"] = habit_cost_time

        final_history = []
        for history in assistant.history.history:
            poi_search_result = history.get('action_type', "")
            # 如果历史上下文带有poi list结果信息，该信息删掉，因为客户端回传该信后，意图识别时用不到。
            if poi_search_result == "poi_search_result":
                continue
            final_history.append(history)

        return TravelCompanionV8MsgOut(poi_list=response["poi_list"],
                                       context=json.dumps(final_history, ensure_ascii=False),
                                       tips=tips, title=title,
                                       code=response['code'],
                                       track_id=track_id, recommend=recommend, 
                                       total_time=duration_msg, success=True,
                                       follow_ups=follow_up_questions)
    else:
        async def generate_response() -> AsyncGenerator:
            poi_list = []
            response_title = ""
            newest_user_query, part_history = assistant.history.history_process()
            logger.info('model_name:{} rewrite_model_name:{} user query:{} part_history:{}'.format(item.model_name, item.rewrite_model_name, newest_user_query, part_history))
            response, poi_list, duration_msg_list = assistant.handle_poi_search(newest_user_query, coordinate, nova_ak=nova_ak,
                                                                         nova_sk=nova_sk, model_name=item.model_name,
                                                                         rewrite_model_name=item.rewrite_model_name,
                                                                         start_time=start_time_perf_counter,
                                                                         is_stream=True, track_id=track_id,
                                                                         location_infos=location_infos)

            if isinstance(response, dict):
                # 如果返回的是字典，说明是错误码(4x)，直接构造最终的错误信息并终止流
                error_chunk = {
                    'type': 'error',
                    'data': {
                        'track_id': track_id,
                        'tips': response.get("response_str", ""),
                        'code': response.get('code')
                    }
                }
                yield "data: {}\n\n".format(json.dumps(error_chunk, ensure_ascii=False))

                # 如果错误码是102，则模拟异步延迟返回特定文字
                if response.get('code') == error_code_irrelevant:
                    response_data_list = ['这个', '问题', '有点', '超出', '我', '的', '知识', '范畴', '啦']
                    # 遍历文字列表，逐字返回
                    for data in response_data_list:
                        await asyncio.sleep(0.1) # 模拟0.1秒的异步延迟
                        # 构造消息体
                        message_chunk = {
                            'data': {
                                'choices': [{'delta': data}],
                                'track_id': track_id
                            }
                        }
                        yield "data: {}\n\n".format(json.dumps(message_chunk, ensure_ascii=False))
                    
                    # 发送一个表示正常结束的chunk，以确保客户端正确处理
                    stop_chunk = {
                        'data': {
                            'choices': [{'finish_reason': 'stop', 'delta': ''}],
                            'track_id': track_id
                        }
                    }
                    yield "data: {}\n\n".format(json.dumps(stop_chunk, ensure_ascii=False))

                return

            first_word = True
            if isinstance(duration_msg_list, dict):
                duration_msg_list["t1-loc"] = location_cost_time
                duration_msg_list["t2-mem"] = habit_cost_time
            poi_list = json.loads(poi_list)['dataList']
            # yield json.dumps(poi_list)
            is_filter = False
            is_star_filter = True
            integrate_stream_output_content = ""
            save_integrate_stream_output_content = ""

            total_time = {}
            total_time['total_time'] = round(time.perf_counter() - start_time_perf_counter, 4)
            duration_msg_list.update(total_time)
            add_time_start = time.perf_counter()
            
            for chunk in response:
                chunk['data']['track_id'] = track_id
                add_time = time.perf_counter()-add_time_start
                add_time_start = time.perf_counter()
                duration_msg_list["total_time"] = round((duration_msg_list["total_time"] + add_time), 4)
                duration_msg_list["integrate_agent_time"] = round((duration_msg_list["integrate_agent_time"] + add_time), 4)
                chunk['data']['duration'] = duration_msg_list
                integrate_stream_output_content += chunk['data']['choices'][0]['delta']
                save_integrate_stream_output_content += chunk['data']['choices'][0]['delta']
                
                pattern = r'"poi_sorted_id"\s*:\s*\[\s*(\d+(?:\s*,\s*\d+)*)\s*\]'
                # print(integrate_stream_output_content)
                # yield f"{json.dumps(chunk)}\n\n"
                match_result = re.search(pattern, integrate_stream_output_content)
                # 当正则匹配到推荐poi索引并且poi_list 里面有内容时进行poi排序
                if match_result and len(poi_list) > 0:
                    try:
                        title_pattern = r'"Title":\s*"([^"]*)"'
                        title_match = re.search(title_pattern, integrate_stream_output_content)
                        if title_match:
                            response_title = title_match.group(1)
                    except Exception:
                        pass

                    poi_list_sorted_dict = json.loads("{" + f"{match_result.group()}" + "}")['poi_sorted_id']
                    logger.info(poi_list_sorted_dict)
                    chunk['data']['choices'][0]['poi_list'] = []
                    chunk['data']['choices'][0]['delta'] = " "
                    for poi in poi_list_sorted_dict:
                        if poi < 0 or poi > len(poi_list):
                            continue
                        chunk['data']['choices'][0]['poi_list'].append(poi_list[poi])
                        # print(poi_list[poi])
                        # chunk['data']['choices'][0]['delta'] = json.dumps(poi_list[poi])

                    # 清空缓存
                    final_history = []
                    for history in assistant.history.history:
                        final_history.append(history)
                    # print(history)
                    chunk['context'] = json.dumps(final_history, ensure_ascii=False)
                    logger.info("即将 YIELD POI LIST 数据块")
                    yield "data: {}\n\n".format(json.dumps(chunk, ensure_ascii=False))
                    integrate_stream_output_content = ""
                try:
                    # 计算模型首帧时间
                    if first_word and not ("poi_list" in chunk['data']['choices'][0]):
                        ttft_time = round(time.perf_counter() - start_time_perf_counter, 4)
                        duration_msg_list["t5-mft"] = ttft_time
                        first_word = False
                except BaseException as e:
                    print(e)

                if "usage" in chunk['data']:
                    chunk['data']['choices'][0]['finish_reason'] = "stop"
                    chunk['data']['choices'][0]['delta'] = ""
                    yield "data: {}\n\n".format(json.dumps(chunk, ensure_ascii=False))

                if 'Recommendation\": \"' in integrate_stream_output_content:
                    # 检索到第一个response后，过滤掉\":\"字符
                    if is_filter == False:
                        is_filter = True
                        continue

                    if '\"\n' in integrate_stream_output_content:
                        # 过滤'\"\n}'
                        if is_star_filter == True:

                            is_star_filter = False
                            chunk['data']['choices'][0]['delta'] = chunk['data']['choices'][0]['delta'].split('"')[0]
                            yield "data: {}\n\n".format(json.dumps(chunk, ensure_ascii=False))
                        #  yield f"{json.dumps(chunk)}\n\n"
                        continue
                    # logger.info(f"track-id: {track_id} per package timestamp: {datetime.utcnow()} {chunk}")
                    yield "data: {}\n\n".format(json.dumps(chunk, ensure_ascii=False))

                # yield f"{json.dumps(chunk)}\n\n"
            save_integrate_stream_output_content = re.sub(r'\s+', "", save_integrate_stream_output_content)
            logger.info(
                f"track-id: {track_id} integrate_output: {save_integrate_stream_output_content}")

            # 推荐问题
            message_id = str(uuid.uuid4())
            logger.info("follow_ups start")
            
            # 传入当前的用户问题和已生成的助手完整回答
            raw_question_response, fup_cost_time = await assistant.get_follow_up_questions(
                user_query=newest_user_query,
                assistant_response=save_integrate_stream_output_content,
                nova_ak=nova_ak,
                nova_sk=nova_sk,
                model_name=item.rewrite_model_name
            )

            logger.info(f"follow_ups end-----cost time:{fup_cost_time}")
            duration_msg_list["t6-follow-ups"] = fup_cost_time

            # 检查是否返回了错误字典
            if isinstance(raw_question_response, dict) and "code" in raw_question_response:
                error_chunk = {
                    'type': 'error',
                    'data': {
                        'track_id': track_id,
                        'tips': raw_question_response.get("response_str", ""),
                        'code': raw_question_response.get("code")
                    }
                }
                yield "data: {}\n\n".format(json.dumps(error_chunk, ensure_ascii=False))
            
            # 解析逻辑
            elif raw_question_response:
                logger.info(f"follow_ups raw response: {raw_question_response}")
                try:
                    match = re.search(r'\[.*?\]', raw_question_response)
                    if match:
                        questions = ast.literal_eval(match.group())
                        
                        if isinstance(questions, list):
                            conclusion_data = {
                                "title": response_title,
                                "follow_ups": questions
                            }
                            follow_up_chunk = {
                                "data": conclusion_data,
                                "type": "follow_ups",
                                "messageId": message_id
                            }
                            yield "data: {}\n\n".format(json.dumps(follow_up_chunk, ensure_ascii=False))
                except Exception as e:
                    logger.error(f"Failed to parse follow-up questions: {raw_question_response}, error: {e}")

            duration_msg_list["total_time"] = round(time.perf_counter() - start_time_perf_counter, 4)

            # 用messageEnd数据块输出完整耗时
            message_end_chunk = {
                "type": "messageEnd",
                "messageId": str(uuid.uuid4()),
                "duration": duration_msg_list
            }
            yield "data: {}\n\n".format(json.dumps(message_end_chunk, ensure_ascii=False))
        # yield f"[DONE]\n\n"
        headers = {"Transfer-Encoding": "chunked", "Connection": "keep-alive"}
        # add request_id
        request_id = uuid.uuid4()
        logger.info(f"travel-companion/v2/chat request_id: {str(request_id)}")
        headers["request_id"] = str(request_id)
        response = StreamingResponse(generate_response(), media_type="text/event-stream", headers=headers)
        response.status_code = 200
        return response



@app.get("/gac/travel-companion/health", response_model=HealthMsgOut)
async def get_health() -> HealthMsgOut:
    return HealthMsgOut(version=__version__, status="up")


def parse_args():
    parser = ArgumentParser()

    parser.add_argument("--root", default="data/workspace/temp")
    # parser.add_argument("--client", default="openai")
    parser.add_argument("--port", type=int, default=0)
    # parser.add_argument("--save", action="store_true")
    # parser.add_argument("--names", nargs="*", default=[
    #     "cabinagent"
    # ])

    args = parser.parse_args()
    return args


if __name__ == '__main__':
    args = parse_args()

    root = args.root
    # names = args.names
    # client = args.client
    port = args.port
    # port = 8080
    # args.port
    # save = args.save

    uvicorn.run(app, host="0.0.0.0", port=port, access_log=True)


