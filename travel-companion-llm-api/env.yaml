
redis:
  host: $REDIS_HOST|auto-cloud-redis-cluster-sit.9uzhp7.clustercfg.cnw1.cache.amazonaws.com.cn
  port: $REDIS_PORT|6379
  password: $REDIS_PASSWORD|""
  username: $REDIS_USERNAME|""



# gaode查询相关内容，主要用在坐标转地址的功能上
gaode:
  api_key: $GAODE_API_KEY|""
  regeo:
    url: $GAODE_URL_REGEO|https://restapi.amap.com/v3/geocode/regeo

# 记忆体查询相关内容
user_memory:
  url: $MEMORY_URL|https://sit-platform.senseauto.com/gac-agent/v1/memory/search

get_poi:
  url: $POI_AMAP_URL|"https://platform.senseauto.com/poi/v1/search/amap"
  client_id: $POI_AMAP_CLIENT_ID|69254417dcc384081916c7296dd0a537

# 大模型
llm_models:
  SenseAutoChat-30B:
    api_key: $SENSEAUTOCHAT_30B_API_KEY|"1s3963nw8802M4O55yMuU6x37tOYQ682"
    base_url: $SENSEAUTOCHAT_30B_BASE_URL|"http://************:8099/v1"
    model_name: "SenseAuto-Chat"
  POI-rewrite-7B:
    api_key: $POIREWRITE_7B_API_KEY|"d941dcfdaaeb637267fc06bda9ae6770"
    base_url: $POIREWRITE_7B_BASE_URL|"https://*************0:8091/v1"
    model_name: "POI/release_models/v0.1.0"
  qwen:
    api_key: $QWEN_API_KEY|"EMPTY"
    base_url: $QWEN_BASE_URL|"http://101.230.144.204:18055/v1"
    model_name: "/mnt/afs/gaomengya/model/senseauto-chat-v0.1.0-release/"
  senseauto:
    api_key: $SENSEAUTO_API_KEY|"UGUMIdOHCy8zsIAmQsWQBnYPmhBibb0i"
    base_url: $SENSEAUTO_BASE_URL|"https://acplatform.sensetime.com/v1"
    model_name: "/mnt/afs/gaomengya/model/senseauto-chat-v0.1.0-release/"
  mapping:
    api_key: $MAPPING_API_KEY|"UGUMIdOHCy8zsIAmQsWQBnYPmhBibb0i"
    base_url: $MAPPING_BASE_URL|"http://103.177.28.206:23334/v1"
    model_name: "/mnt/afs/xuyilun/xtuner_multi_node/hz_filter/exp1/hf_model/final_iter"
