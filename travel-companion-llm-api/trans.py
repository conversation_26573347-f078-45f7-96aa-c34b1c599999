from src.utiles.utiles import *
from src.stage0_agent_intent import call_intent_agent
from src.stage1_classifier import call_classifier
import json
import re
import requests
import time

# 设置URL
url = "https://sit-platform.senseauto.com/poi/v1/search/conditions"

# 设置headers
headers = {
    "client-id": "C4M79714h4t637t3eRRbd0P8ffq473o4",
    "Content-Type": "application/json"
}

poi_type_dict = {
  "汽车服务": {
    "汽车服务相关": ["汽车服务相关"],
    "加油站": ["加油站", "中国石化", "中国石油", "壳牌", "美孚", "加德士", "东方", "中石油碧辟", "中石化碧辟", "道达尔", "埃索", "中化道达尔"],
    "其它能源站": ["其它能源站"],
    "加气站": ["加气站"],
    "汽车养护/装饰": ["汽车养护", "加水站"],
    "洗车场": ["洗车场"],
    "汽车俱乐部": ["汽车俱乐部"],
    "汽车救援": ["汽车救援"],
    "汽车配件销售": ["汽车配件销售"],
    "汽车租赁": ["汽车租赁", "汽车租赁还车"],
    "二手车交易": ["二手车交易"],
    "充电站": ["充电站", "换电站", "充换电站", "专用充电站"]
  },
  "餐饮服务": {
    "餐饮相关场所": ["餐饮相关"],
    "中餐厅": ["中餐厅", "综合酒楼", "四川菜(川菜)", "广东菜(粤菜)", "山东菜(鲁菜)", "江苏菜", "浙江菜", "上海菜", "湖南菜(湘菜)", "安徽菜(徽菜)", "福建菜", "北京菜", "湖北菜(鄂菜)", "东北菜", "云贵菜", "西北菜", "老字号", "火锅店", "特色/地方风味餐厅", "海鲜酒楼", "中式素菜馆", "清真菜馆", "台湾菜", "潮州菜"],
    "外国餐厅": ["外国餐厅", "西餐厅(综合风味)", "日本料理", "韩国料理", "法式菜品餐厅", "意式菜品餐厅", "泰国/越南菜品餐厅", "地中海风格菜品", "美式风味", "印度风味", "英国式菜品餐厅", "牛扒店(扒房)", "俄国菜", "葡国菜", "德国菜", "巴西菜", "墨西哥菜", "其它亚洲菜"],
    "快餐厅": ["快餐厅", "肯德基", "麦当劳", "必胜客", "永和豆浆", "茶餐厅", "大家乐", "大快活", "美心", "吉野家", "仙跡岩", "呷哺呷哺"],
    "休闲餐饮场所": ["休闲餐饮场所"],
    "咖啡厅": ["咖啡厅", "星巴克咖啡", "上岛咖啡", "Pacific CoffeeCompany", "巴黎咖啡店"],
    "茶艺馆": ["茶艺馆"],
    "冷饮店": ["冷饮店"],
    "糕饼店": ["糕饼店"],
    "甜品店": ["甜品店"]
  },
  "购物服务": {
    "购物相关场所": ["购物相关场所"],
    "商场": ["商场", "购物中心", "普通商场", "免税品店"],
    "便民商店/便利店": ["便民商店/便利店", "7-ELEVEn便利店", "OK便利店"],
    "家电电子卖场": ["家电电子卖场", "综合家电商场", "国美", "大中", "苏宁", "手机销售", "数码电子", "丰泽", "苏宁镭射"],
    "超级市场": ["超市", "家乐福", "沃尔玛", "华润", "北京华联", "上海华联", "麦德龙", "乐天玛特", "华堂", "卜蜂莲花", "屈臣氏", "惠康超市", "百佳超市", "万宁超市"],
    "花鸟鱼虫市场": ["花鸟鱼虫市场", "花卉市场", "宠物市场"],
    "家居建材市场": ["家居建材市场", "家具建材综合市场", "家具城", "建材五金市场", "厨卫市场", "布艺市场", "灯具瓷器市场"],
    "综合市场": ["综合市场", "小商品市场", "旧货市场", "农副产品市场", "果品市场", "蔬菜市场", "水产海鲜市场"],
    "文化用品店": ["文化用品店"],
    "体育用品店": ["体育用品店", "李宁专卖店", "耐克专卖店", "阿迪达斯专卖店", "锐步专卖店", "彪马专卖店", "高尔夫用品店", "户外用品"],
    "特色商业街": ["特色商业街", "步行街"],
    "服装鞋帽皮具店": ["服装鞋帽皮具店", "品牌服装店", "品牌鞋店", "品牌皮具店", "品牌箱包店"],
    "专卖店": ["专营店", "古玩字画店", "珠宝首饰工艺品", "钟表店", "眼镜店", "书店", "音像店", "儿童用品店", "自行车专卖店", "礼品饰品店", "烟酒专卖店", "宠物用品店", "摄影器材店", "宝马生活方式", "土特产专卖店"],
    "特殊买卖场所": ["特殊买卖场所", "拍卖行", "典当行"],
    "个人用品/化妆品店": ["其它个人用品店", "莎莎"]
  },
  "交通设施服务": {
    "交通服务相关": ["交通服务相关"],
    "机场相关": ["机场相关", "候机室", "摆渡车站", "飞机场", "机场出发/到达", "直升机场", "机场货运处"],
    "火车站": ["火车站", "候车室", "进站口/检票口", "出站口", "站台", "售票", "退票", "改签", "公安制证", "票务相关", "货运火车站"],
    "港口码头": ["港口码头", "客运港", "车渡口", "人渡口", "货运港口码头", "进港", "出港", "候船室"],
    "长途汽车站": ["长途汽车站", "进站", "出站", "候车室"],
    "地铁站": ["地铁站", "出入口"],
    "轻轨站": ["轻轨站"],
    "公交车站": ["公交车站相关", "旅游专线车站", "普通公交站", "机场巴士", "快速公交站", "电车站", "智轨车站"],
    "班车站": ["班车站"],
    "停车场": ["停车场相关", "换乘停车场", "公共停车场", "专用停车场", "路边停车场", "停车场入口", "停车场出口", "停车场出入口"]
  },
  "体育休闲服务": {
    "影剧院": ["电影院"]
  },
  "住宿服务": {
    "住宿服务相关": ["住宿服务相关"],
    "宾馆酒店": ["宾馆酒店", "奢华酒店", "五星级宾馆", "四星级宾馆", "三星级宾馆", "经济型连锁酒店"],
    "旅馆招待所": ["旅馆招待所", "青年旅舍"]
  },
  "风景名胜": {
    "风景名胜相关": ["旅游景点"],
    "公园广场": ["公园广场", "公园", "动物园", "植物园", "水族馆", "城市广场", "公园内部设施"],
    "风景名胜": ["风景名胜", "世界遗产", "国家级景点", "省级景点", "纪念馆", "寺庙道观", "教堂", "回教寺", "海滩", "观景点", "红色景区"]
  }
}

first_level_list = list(poi_type_dict.keys())
second_level_dict = {}
third_level_dict = {}
sec_trd_level_dict = {}

for key, value in poi_type_dict.items():
    second_level_dict[key] = list(poi_type_dict[key].keys())
    sec_trd_level_dict[key] = []
    for sec_type in second_level_dict[key]:
        third_level_dict[sec_type] = poi_type_dict[key][sec_type]
        sec_trd_level_dict[key].extend(poi_type_dict[key][sec_type])

print(first_level_list)
print(second_level_dict)
print(third_level_dict)
print(sec_trd_level_dict)
