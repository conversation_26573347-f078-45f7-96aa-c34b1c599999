from src.utiles.agent import Agent

#### this is stage 0, intent rewrite

def rerank(queries,model_name="gpt-4o"):
    prompts_file_name = ["router_system_prompt", "rerank_user_prompt"]
    # prompts_file_name = ["router_system_prompt",  "router_user_prompt"]
    replace_strs = ["user_input"]
    intent_agent = Agent(queries,prompts_file_name,replace_strs)


    # phrase_result = intent_agent.call_apis(model_name)
    phrase_result = intent_agent.convert2json(model_name)

    return phrase_result


if __name__ == "__main__":
    queries= [" "]
    phrase_result = call_intent_agent(queries,"gpt-4o")
    print(phrase_result)


