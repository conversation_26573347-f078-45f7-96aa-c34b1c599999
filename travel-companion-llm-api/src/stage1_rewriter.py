from src.utiles.agent import Agent
import time
#### this is stage 0, intent rewrite

def call_rewriter(queries, model_name="gpt-4o", task="rewriter"):
    prompts_file_name = ["rewriter_system_prompt",  "rewriter_user_prompt"]
    replace_strs = ["user_input"]
    intent_agent = Agent(queries, prompts_file_name, replace_strs)

    start = time.time()
    phrase_result = intent_agent.call_apis(model_name, task)
    print(f"rewriter time: {time.time() - start}")
    return phrase_result


if __name__ == "__main__":
    queries= [" "]
    phrase_result = call_rewriter(queries,"gpt-4o")
    print(phrase_result)


