serper_api_key: "fa3ae685e31afb390b0bfc45d6a8575522bb4d71"
openai_api_key: "sk-rmDkmN50yF1fnlY6Df19609dDbC24710A6C2146060371955"
serpapi_key: "59d46fe69eecdeb90cb6b40146166af47d3bdf219e2cea0d1c76c41f93f16f4c"
openai_base_url: "http://cubic.sensetime.com:8457/v1"
embedding_url: "http://10.198.6.242:7008/v1/qd"

# model_name: "gpt-3.5-turbo-16k"
model_name: "gpt-4o-mini"
template: |
  Web search result:
  {context_str}
  
  Instructions: You are a/an {profile}. Using the provided web search results, write a comprehensive and detailed reply to the given query. 
  Make sure to cite results using [number] notation after the reference.
  At the end of the answer, list the corresponding references with indexes, each reference contains the urls and quoted sentences from the web search results by the order you marked in the answer above and these sentences should be exactly the same as in the web search results.
  Here is an example of a reference:
      [1] URL: https://www.pocketgamer.biz/news/81670/tencent-and-netease-dominated-among-chinas-top-developers-in-q1/
          Quoted sentence: Tencent accounted for roughly 50% of domestic market revenue for the quarter, compared to 40% in Q1 2022.
  Respond language: Chinese
  Query: {query}
  Output Format: {format}
  Please organize your output according to the Output Format. If the Output Format is empty, you can ignore it.