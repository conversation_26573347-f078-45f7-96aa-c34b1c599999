from src.utiles.agent import Agent

#### this is stage 0, intent rewrite

def call_tool_filter(queries, snippets, model_name="gpt-4o", task="filter"):
    prompts_file_name = ["filter_system_prompt",  "filter_user_prompt"]
    replace_strs = ["poilist", "keyword"]
    intent_agent = Agent(queries, prompts_file_name, replace_strs, snippets=snippets)

    import time
    start = time.time()
    phrase_result = intent_agent.call_apis(model_name, task)
    print(f"call intent time: {time.time() - start}")
    return phrase_result


if __name__ == "__main__":
    queries= [" "]
    phrase_result = call_intent_agent(queries,"gpt-4o")
    print(phrase_result)


