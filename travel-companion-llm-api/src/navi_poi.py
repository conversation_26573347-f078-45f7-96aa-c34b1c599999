import requests
import json

class NaviPOIPlugin:
    def __init__(self):
        self.function_description = {
            "name": "navi_poi",
            "description": "查找兴趣点",
            "input_parameters": {
                "type": "object",
                "description": "输入参数",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "起点位置的名称，例如：北京市天安门",
                    },
                    "radius": {
                        "type": "int",
                        "description": "检索半径，例如1公里：1000",
                    },
                    "types": {
                        "type": "string",
                        "description": "兴趣点类型，例如：餐馆、酒店",
                    }
                },
                "required": ["location", "destination", "types"],
            },
            "output_parameters": {
                "type": "object",
                "description": "输出参数",
                "properties": {
                    "message": {
                        "type": "string",
                        "description": "返回输出结果",
                    }
                },
                "required": ["message"],
            },
        }

    def geocode(self,api_key, address):  # 将地址转换为经纬度坐标
        url = 'https://restapi.amap.com/v3/geocode/geo'
        params = {
            'key': api_key,
            'address': address,
            'output': 'JSON'
        }
        response = requests.get(url, params=params)
        return response.json()
    
    
    def get_pois_nearby(self, api_key, location, radius, types):    # 兴趣点获取
        url = 'https://restapi.amap.com/v3/place/around'
        params = {
            'key': api_key,
            'location': location,  # 经度,纬度 (格式: 经度,纬度)
            'radius': radius,      # 检索半径 (单位: 米)
            'types': types,        # POI 类型 (可选)
            'output': 'JSON'       # 返回格式
        }
        response = requests.get(url, params=params)
        return response.json()
    
    
    def run(self, location, radius, types):
        api_key = 'ee7026026a68f681887b22e0502063da'    # 使用的高德API

        location_address = self.geocode(api_key, location)
        
        if location_address.get('status') == '1':
            location_geo = location_address.get('geocodes', [])[0].get('location')

            if location_geo:
                pois = self.get_pois_nearby(api_key, location_geo, radius, types)
                if pois.get('status') == '1':
                    result = {"pois": []}
                    for poi in pois.get('pois', []):
                        result["pois"].append({"name": poi.get('name'), "address": poi.get('address')})
                    return json.dumps(result, ensure_ascii=False, indent=4)
                else:
                    return json.dumps({"message": f"Error: {pois.get('info')}"}, ensure_ascii=False, indent=4)

            else:
                return json.dumps({"message": "地址解析失败"}, ensure_ascii=False, indent=4)

        else:
            return json.dumps({"message": "地址解析错误"}, ensure_ascii=False, indent=4)
       


if __name__ == "__main__":
    tool = NaviPOIPlugin()
    location = '深圳南山金海岸大厦'  # 起点位置
    radius = 1000  # 检索半径
    types = '游泳'  # POI 类型
    response = tool.run(location, radius, types)
    print(f'ending')
    response_json = json.loads(response)
    print(response_json)
    function_description = tool.function_description
    print(function_description)