import os

import requests
import json
import copy
import time
import numpy as np
import threading
import time
import queue

def merge(responseAC: list,responseB: list) -> dict:  # [dict]
    datalistA = []
    datalistB = []
    datalistC = []

    dataset = set()
    tot = []
    totPages = []
    page = []
    pagesize = []


    for content in responseAC:
        tot.append(content['data']['total'])
        if int(content['data']['total']) > 0:
            totPages.append(content['data']['totalPages'])
            page.append(content['data']['page']-1)
            pagesize.append(content['data']['pageSize'])
            for data in content['data']['dataList']:
                ishash = "{}-{:.2f}-{:.2f}".format(data['name'],float(data['longitude']),float(data['latitude']))
                if ishash not in dataset:
                    dataset.add(ishash)
                    if 'otherPois' in str(data):
                        datalistA.append(data)
                    else:
                        datalistC.append(data)
    
    for content in responseB:
        tot.append(content['data']['total'])
        if int(content['data']['total']) > 0:
            totPages.append(content['data']['totalPages'])
            page.append(content['data']['page'])
            pagesize.append(content['data']['pageSize'])
            for data in content['data']['dataList']:
                ishash = "{}-{:.2f}-{:.2f}".format(data['name'],float(data['longitude']),float(data['latitude']))
                if ishash not in dataset:
                    dataset.add(ishash)
                    datalistB.append(data)

    datalistA = sorted(datalistA, key=lambda x: int(x['distance']))
    datalistB = sorted(datalistB, key=lambda x: int(x['distance']))
    datalistC = sorted(datalistC, key=lambda x: int(x['distance']))
    # print('-'*50,'datalistA','-'*50)
    # print(datalistA)
    # print('-'*100)
    # print('-'*50,'datalistB','-'*50)
    # print(datalistB)
    # print('-'*100)
    # print('-'*50,'datalistC','-'*50)
    # print(datalistC)
    # print('-'*100)

    merge_res = {}
    merge_res['status'] = content['status']
    merge_res['data'] = {}
    merge_res['data']['total'] = str(np.min(tot))
    merge_res['data']['totalPages'] = str(np.min(totPages)) if totPages else ""
    merge_res['data']['page'] = str(np.min(page)) if page else ""
    merge_res['data']['pageSize'] = str(np.min(pagesize)) if pagesize else ""
    merge_res['data']['dataList'] = datalistA+datalistB+datalistC

    return merge_res




def poi_search_base(ids,payload:str, result_queue):
    start_time = time.time()
    # print("开始搜索")
    url = "http://avatr-poi-search-service.autocloud-platform.svc:8080/avatr/poi/v1/search/conditions"
    headers = {
        'client-id': '69254417dcc384081916c7296dd0a537',
        'Content-Type': 'application/json'
    }
    response = requests.request("POST", url, headers=headers, data=payload).text
    end_time = time.time()
    # print("搜索结束耗时", end_time-start_time)
    result_queue.put((ids,response))

def poi_search_api(payload: str) -> str:

    payload = json.loads(payload)
    if 'poiType' in payload.keys():
        if '' not in payload["poiType"]:
            payload["poiType"].append('')
    else:
        payload["poiType"] = ['']
    type_len = len(payload["poiType"])
    Name_len = len(payload["poiName"])               ##poiName不会为空
    Area_len = len(payload["filter"]["otherPois"])+1   ##添加other pois

    if "poiTags" in payload.keys():
        poiTags = ','.join(payload["poiTags"])  # poiTags 用于过滤,多个tag应该至少满足一个。传入为string,','隔开

    ori_load = [copy.deepcopy(payload) for _ in range(type_len * Name_len*Area_len)]
    
    for idx in range(type_len):
        for idy in range(Name_len):
            for idz in range(Area_len):
                if idz == Area_len -1:    
                    pass
                else:
                    oth = payload["filter"]["otherPois"][:]                    
                    Area = oth.pop(idz)["keyword"]
                    ori_load[idx*Name_len*Area_len + idy*Area_len+idz]['poiArea'] = Area
                    ori_load[idx*Name_len*Area_len + idy*Area_len+idz]["filter"]["otherPois"] = oth                    
                ori_load[idx*Name_len*Area_len + idy*Area_len+idz]['poiType'] = payload["poiType"][idx]
                ori_load[idx*Name_len*Area_len + idy*Area_len+idz]['poiName'] = payload["poiName"][idy]
                if "poiTags" in payload.keys():
                    ori_load[idx*Name_len*Area_len + idy*Area_len+idz]['poiTags'] = poiTags


    # 初始化线程队列
    result_queue = queue.Queue()
    # 创建线程列表
    threads = []
    start = time.time()
    for ids,ori in enumerate(ori_load):
        ori_str = json.dumps(ori)  # 将 ori 转换为字符串
        # 创建一个线程执行 poi_search_base 任务
        t = threading.Thread(target=poi_search_base, args=(ids,ori_str, result_queue))
        threads.append(t)

    # 启动所有线程
    for thread in threads:
        thread.start()

    # 等待所有线程执行完毕
    for thread in threads:
        thread.join()

    # 从队列中收集结果
    responseAC = []
    responseB = []
    while not result_queue.empty():
        content = result_queue.get()
        idx = content[0]
        if not (idx+1)%(Name_len*Area_len):
            responseAC.append(json.loads(content[1]))
        else:
            responseB.append(json.loads(content[1]))
        
    # print('tot_time:{:.2f}'.format(time.time()-start))
    megedres = merge(responseAC,responseB)

    return json.dumps(megedres, ensure_ascii=False)


if __name__ == "__main__":


    # 现在不会出现没有poiName只有PoiType的情况。

    results = {"poiType": ["咖啡馆",], "latitude": "40.081839", "longitude": "116.586733",
               "poiName": ["咖啡馆" ], "poiTags": [""], "page": 1, "pageSize": 5,
               "searchRadius": 25000, "filter": {"otherPois": [{'keyword':"颐和园"}]}}
    # results = {"poiType": ["体育休闲服务"], "latitude": "40.081839", "longitude": "116.586733",
    #              "poiName": ["体育休闲"], "poiTags": [""], "page": 1, "pageSize": 5,
    #              "searchRadius": 15000, "filter": {"otherPois": []}}
    # results = {
    #   "keywords": "火锅店,中餐厅,餐饮服务",
    #   "poiType": "火锅店",
    #   "searchRadius": 50000,
    #   "latitude": "40.088988",
    #   "longitude": "116.541239",
    #   "poiTags": "火锅",
    #   "filter": {
    #     "otherPois": []
    #   },
    #   "page": 0,
    #   "pageSize": 10
    # }
    # results = {"poiType": ["餐饮相关"], "latitude": "40.081839", "longitude": "116.586733", "poiName": ["肯德基"], "page": 1,
    #    "pageSize": 5, "searchRadius": 15000, "filter": {"otherPois": []}}
    payload = json.dumps(results, ensure_ascii=False, indent=4)
    start = time.time()
    response = poi_search_api(payload)
    # response = poi_search_api2(payload)
    print(type(response))
    print(json.loads(response))
    print(f'{time.time() - start} sec')
    # poi_list_info = json.loads(response)
