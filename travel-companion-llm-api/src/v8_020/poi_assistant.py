import json

from .history_management import History
from .intent_agent import intent_agent,IntentInput,IntentOutput
from .mapping_agent import mapping_agent,MappingInput,MappingOutput
from .integrate_agent import integrate_agent,IntegrateInput,IntegrateOutput
from .poi_search_api import poi_search_api
from .utiles import get_coordinate, get_timestamp, get_uuid, save_interaction_results



class Assistant_020():
    def __init__(self, id=-1, max_save_poi_list_num=2):
        self.id = get_uuid() if id == -1 else str(id)
        self.history = History(self.id, max_save_poi_list_num)

    def process_query(self, query):
        self.history.user_record(query)

        intent_history_prompt = self.history.intent_get_history_prompt()
        intent_output = intent_agent(IntentInput(history=intent_history_prompt, id=self.id)).results
        # print("【intent_output】")
        # print(intent_output)
        self.history.intent_record(intent_output)

        intent_output = json.loads(intent_output)

        return self.handle_action(intent_output)
    
    def handle_action(self, intent_output):
        action = intent_output["action"]
        params = intent_output["params"]

        if action == 'direct_response':
            response = self.handle_direct_response(params)
        
        elif action == 'navigation':
            response = self.handle_navigation()

        elif action == 'POI_recommend':
            response = self.handle_poi_recommend()

        elif action == 'poi_search':
            response = self.handle_poi_search(params)

        else:
            response = "action内容错误，intent输出内容如下：" + intent_output
        
        return response
    
    def handle_direct_response(self, params):
        response = "【助手回复】：" + params["response"]
        return response

    def handle_navigation(self):
        poi = self.history.get_newest_navigation_poi()
        response = "【导航目的地】：\n" + poi
        return response

    def handle_poi_search(self, params):
        tips = params["tips"]
        response = "【助手提示】：" + tips

        mapping_history_prompt = self.history.mapping_get_history_prompt()
        poi_search = json.dumps(params, ensure_ascii=False)
        coordinate = get_coordinate()
        mapping_output = mapping_agent(MappingInput(history=mapping_history_prompt, id=self.id, poi_search=poi_search, latitude=coordinate["latitude"], longitude=coordinate["longitude"])).results
        # print("【mapping_output】")
        # print(mapping_output)

        self.history.mapping_record(mapping_output)

        poi_list = poi_search_api(mapping_output)
        self.history.poi_search_record(poi_list)

        poi_list2 = json.loads(poi_list)

        integrate_history_prompt, poi_list = self.history.integrate_get_history_prompt()
        integrate_output = integrate_agent(IntegrateInput(history=integrate_history_prompt, id=self.id, poi_list=poi_list)).results
        self.history.integrate_record(integrate_output)

        integrate_output = json.loads(integrate_output)
        recommend = integrate_output["recommend"]
        poi_list = json.dumps(integrate_output["poi_list"]["dataList"], ensure_ascii=False, indent=4)

        response = response + "\n【助手推荐】：" + recommend + "\n【推荐poi list展示】：\n" + poi_list
        return response

    def handle_poi_recommend(self):
        integrate_history_prompt, poi_list = self.history.integrate_get_history_prompt()
        integrate_output = integrate_agent(IntegrateInput(history=integrate_history_prompt, id=self.id, poi_list=poi_list)).results
        self.history.integrate_record(integrate_output)

        integrate_output = json.loads(integrate_output)
        recommend = integrate_output["recommend"]
        poi_list = json.dumps(integrate_output["poi_list"]["dataList"], ensure_ascii=False, indent=4)

        response = "【助手推荐】：" + recommend + "\n【推荐poi list展示】：\n" + poi_list
        return response
