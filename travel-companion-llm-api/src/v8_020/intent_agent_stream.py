from .client import client
from pydantic import BaseModel
import json
import time
from .history_management import History
from typing import Generator

class IntentInput(BaseModel):
    # model_name: str = "qwen72b"
    model_name: str = "senseauto"
    history: str
    id: str = "-1"
    is_stream: bool = True
    nova_ak:str=""
    nova_sk:str=""

class IntentOutput(BaseModel):
    results: str
    success: bool

def intent_agent_call(item:IntentInput, start_time = -1) ->Generator:
    model_name = item.model_name
    history = item.history
    id = item.id
    system_prompt_path = "src/v8_020/prompts/intent_system_prompt.txt"
    with open(system_prompt_path, 'r', encoding='utf-8') as file:
        system_prompt = file.read()

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": history},
    ]

    response = client(messages, id, model_name=model_name, is_stream=item.is_stream, nova_ak=item.nova_sk, nova_sk=item.nova_sk)
    return response


def intent_agent_process(response:Generator, start_time = -1) ->IntentOutput:
    content = ""
    for chunk in response:
        content += chunk
    # print(content)
    results = content.split("```json")[-1].split("```")[0]
    results = json.loads(results)
    if results["action"] == "poi_search":
        if "tips" not in results["params"]:
            results["params"]["tips"] = "正在搜索，请您耐心等候。"
    results = json.dumps(results,ensure_ascii=False)

    return IntentOutput(results = results, success = True)

if __name__ == "__main__":
    history = History()
    query = "导航到第二个"
    history.user_record(query)
    history_prompt = history.intent_get_history_prompt()
    intent_output = intent_agent_call(IntentInput(history=history_prompt))
    content = ""
    for chunk in intent_output :
        content += chunk
        print(chunk,end="")
