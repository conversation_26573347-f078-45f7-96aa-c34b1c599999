import time
import pandas as pd
import json
from openai import OpenAI
import sensenova
import os
import re

from .utiles import get_timestamp, get_uuid

def client_openai(messages:list, model_name="qwen", is_stream=False, temperature=0):

    model_exist = True
    if "qwen" in model_name.lower():
        openai_api_key = "EMPTY"
        # openai_api_base = "http://10.4.196.19:8000/v1"
        # openai_api_base = "http://10.198.34.126:8000/v1"
        # openai_api_base = "http://10.4.196.19:8000/v1"
        openai_api_base = "http://10.198.34.142:8000/v1"
        # openai_api_base = "http://103.237.29.217:2222/v1"
        model_name = "/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct"
    elif "senseauto" in model_name.lower():

        # openai_api_key = "EMPTY"
        # openai_api_base = "http://10.198.34.142:8000/v1"
        # model_name = "/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct"

        # openai_api_key = "EMPTY"
        # openai_api_base = "http://101.230.144.204:16888/v1"
        # model_name = "/mnt/afs/gaomengya/model/senseauto-chat-v0.1.0-release/"

        openai_api_key = "EMPTY"
        openai_api_base = "http://101.230.144.204:16908/v1"
        model_name = "/mnt/afs/gaomengya/model/senseauto-chat-v0.2.0"

        # openai_api_key = "EMPTY"
        # openai_api_base = "http://101.230.144.204:18055/v1"
        # model_name = "/mnt/afs/gaomengya/model/senseauto-chat-v0.2.1"

    else:
        print("没有相关的模型名字")
        model_exist = False

    if model_exist:
        client = OpenAI(
            api_key=openai_api_key,
            base_url=openai_api_base,
        )
        response = client.chat.completions.create(
            model=model_name,
            messages=messages,
            logprobs=True,
            stream=is_stream,
            top_p=0.3,
            temperature=0.001
            # temperature=temperature
        )

        if is_stream:
            for chunk in response:
                chunk_str = chunk.choices[0].delta.content
                if isinstance(chunk_str,str):
                    yield chunk_str
        else:
            content = response.choices[0].message.content
            yield content
    else:
        yield None

def client_sensenova(messages:list, model_name="senseauto", is_stream=False, temperature=0.001):
    if "senseauto" in model_name.lower():
        model_name = "SenseAuto-Chat"
    # sensenova.access_key_id = "2V3uatcedTJPhW9srmCt5TsCfmp"
    # sensenova.secret_access_key = "XzoVqkGWjVONZx5G8jWfVPluXuRkuGlI"
    # sensenova.api_base='https://api.sensenova.cn/v1/llm/chat-completions'

    sensenova.access_key_id = "2afg7AXRlVilFjbcPkfBCTM6wfy"
    sensenova.secret_access_key = "XXTisIffRTbWiTLF7ql8RFtV1SEX1K6Q"

    response = sensenova.ChatCompletion.create(
        model=model_name,
        max_new_tokens=1024,
        messages=messages,
        # repetition_penalty=1.05,
        top_p=0.3,
        temperature=0.001,
        # temperature=temperature
        # top_k=3, #3或5
        stream=is_stream,
    )

    if is_stream:
        for chunk in response:
            chunk_str = chunk['data']['choices'][0]['delta']
            if isinstance(chunk_str,str):
                yield chunk_str
    else:
        content = response['data']['choices'][0]['message']
        yield content

def client(messages:list, id:str, model_name="qwen", is_stream=False, temperature=0.001):
    # history_dir = "client_log"
    # os.makedirs(history_dir, exist_ok=True)
    # max_number = 0
    # for filename in os.listdir(history_dir):
    #     filename = filename.replace("_in","")
    #     filename = filename.replace("_out", "")
    #     match = re.fullmatch(r'^\d+\.txt$', filename)
    #     if match:
    #         # 将匹配到的数字转换为整数
    #         number = int(match.group().replace(".txt", ""))
    #         # 更新最大编号
    #         if number > max_number:
    #             max_number = number
    # max_number += 1
    # save_path = history_dir + "\\" + f"{id}_{get_timestamp()}"

    if "sense" in model_name.lower():
        # client_type = "sensenova"
        client_type = "openai"
    elif "qwen" in model_name.lower() or "gpt" in model_name.lower():
        client_type = "openai"
    else:
        client_type = ""

    save_content = ""

    if client_type == "sensenova":
        response = client_sensenova(messages,model_name,is_stream,temperature)
        for chunk in response:
            save_content += chunk
            yield chunk

    elif client_type == "openai":
        response = client_openai(messages,model_name,is_stream,temperature)
        for chunk in response:
            save_content += chunk
            yield chunk

    else:
        yield "无匹配模型名称"

    # with open(save_path + "_in.txt", 'w', encoding='utf-8') as file:
    #     json.dump(messages,file,ensure_ascii=False,indent=4)
    # with open(save_path + "_out.txt", 'w', encoding='utf-8') as file:
    #     file.write(save_content)


if __name__ == "__main__":
    system_prompt = "你是一个助手"
    history = "请讲一个笑话"

    # with open("D:\data_for_work\\baoma_POI\\travelcompanion-poi-v8_display\client_log\\74_in.txt","r",encoding="utf-8") as file:
    #     messages = json.load(file)

    messages = [
        {"role": "system", "content": "你是一个助手"},
        {"role": "user", "content": "你好"},
    ]

    # response = client(messages,model_name="qwen",is_stream=False)
    # content = response.choices[0].message.content

    # content = client(messages, model_name="senseauto", is_stream=False)
    # print(content)

    response = client(messages, model_name="senseauto", is_stream=False)
    for chunk_str in response:
        # chunk_str = chunk['data']['choices'][0]['delta']
        print(chunk_str,end="")

    # response = client(messages,model_name="gpt4o", is_stream=True)
    #
    # for chunk in response:
    #     chunk_str = chunk.choices[0].delta.content
    #     print(chunk_str)
