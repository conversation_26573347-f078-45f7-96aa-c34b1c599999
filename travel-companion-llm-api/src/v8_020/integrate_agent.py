from .client import client
from pydantic import BaseModel
import json
import time
from .history_management import History
from .integrate_agent_stream import (
    integrate_agent_call,
    integrate_agent_process, 
    IntegrateInput, 
    IntegrateOutput,
)

def integrate_agent(item:IntegrateInput, start_time = -1) ->IntegrateOutput:
    
    item.is_stream = False
    response =integrate_agent_call(item,start_time)
    
    output = integrate_agent_process(item,response,start_time)

    return output

if __name__ == "__main__":
    history = History(max_save_poi_list_num=2, max_save_history_num=20)

    user_info = "你好，今天天气怎么样？"
    history.user_record(user_info)

    integrate_info = {
        "recommend": "推荐您去咖啡厅B，营业时间最晚。",
        "poi_list":{
            'total': 198, 'totalPages': 40, 'page': 1, 'pageSize': 5,
            'dataList': [
                {'name': '抽象烤牛肉(泓晟国际中心店)', 'type': '餐饮服务;外国餐厅;外国餐厅',
                 'address': '朝阳门北大街泓晟国际中心一层101', 'longitude': '116.433186', 'latitude': '39.930641',
                 'area': '东四', 'distance': 12110,'tel': '19910327789',
                 'rating': '4.6', 'closed': False, 'openTime': '11:00-02:00',
                 'tags': ['牛肉', '料理', '豪华生鱼拼', '赤贝刺身', '刺身拼盘', '烤黑虎虾', '烤肉', '极上和牛四种',
                          '寿喜锅', '活海胆', '和牛七拼', '西冷牛排', '和牛五种', '烧烤', '日本料理', '鲜鱼刺身',
                          '野生蓝鳍金枪鱼', '壶渍牛肋条', '刺身', '虎虾天妇罗', '手握寿司']},

                {'name': '寿司郎', 'type': '餐饮服务;外国餐厅;日本料理', 'address': '长富宫办公楼2号楼4楼4003',
                 'longitude': '116.438431', 'latitude': '39.907517', 'area': '建外大街', 'distance': 13921,
                 'tel': '13503051344', 'rating': '4.0', 'closed': False, 'tags': ['寿司']},

                {'name': '歌志轩名古屋拉面(北京东方新天地店)', 'type': '餐饮服务;外国餐厅;日本料理',
                 'address': '王府井东方广场1号东方新天地LG层', 'longitude': '116.414908', 'latitude': '39.909051',
                 'area': '东单', 'distance': 12283, 'rating': '3.5', 'closed': False, 'tags': ['拉面']}]
        },
    }
    integrate_info = json.dumps(integrate_info)
    history.integrate_record(integrate_info)

    mapping_info ={
      "poiType": ["电影院"],
      "tags": ["评分高"],
      "position": "海淀区",
      "otherPois": [{"keyword": "麦当劳"}]
    }
    mapping_info = json.dumps(mapping_info)
    history.mapping_record(mapping_info)

    intent_response_info = {
        "action": "direct_response",
        "params": {
            "response":"一年有12个月"
        }
    }
    intent_response_info = json.dumps(intent_response_info,ensure_ascii=False)
    history.intent_record(intent_response_info)

    intent_search_info = {
        "action": "poi_search",
        "params": {
            "recommendPois": ["北京大学"],
            "Constraints": [],
            "pageIndex": 0,
            "tips":"正在导航到北京大学，请稍等。"
        }
    }
    intent_search_info = json.dumps(intent_search_info, ensure_ascii=False)
    history.intent_record(intent_search_info)

    intent_navigation_info = {
      "action": "navigation",
      "params": {
        "POI_list_index":0,
        "POI_index":0,
      }
    }
    intent_navigation_info = json.dumps(intent_navigation_info, ensure_ascii=False)
    history.intent_record(intent_navigation_info)

    user_info = "你好，我要去距离最远的餐厅"
    history.user_record(user_info)

    intent_recommend_info = {
        "action": "POI_recommend",
        "params": {
            "POI_list_index":1
        }
    }
    intent_recommend_info = json.dumps(intent_recommend_info, ensure_ascii=False)
    history.intent_record(intent_recommend_info)
    poi_search_info = {
            "state": 200,
            "data":{
                'total': 198, 'totalPages': 40, 'page': 1, 'pageSize': 5,
                'dataList': [
                    {'name': '御·本泽和牛烧肉(泓晟国际中心店)', 'type': '餐饮服务;外国餐厅;外国餐厅',
                     'address': '朝阳门北大街泓晟国际中心一层101', 'longitude': '116.433186', 'latitude': '39.930641',
                     'area': '东四', 'distance': 12110,'tel': '19910327789',
                     'rating': '4.6', 'closed': False, 'openTime': '11:00-02:00',
                     'tags': ['牛肉', '料理', '豪华生鱼拼', '赤贝刺身', '刺身拼盘', '烤黑虎虾', '烤肉', '极上和牛四种',
                              '寿喜锅', '活海胆', '和牛七拼', '西冷牛排', '和牛五种', '烧烤', '日本料理', '鲜鱼刺身',
                              '野生蓝鳍金枪鱼', '壶渍牛肋条', '刺身', '虎虾天妇罗', '手握寿司']},

                    {'name': '寿司郎', 'type': '餐饮服务;外国餐厅;日本料理', 'address': '长富宫办公楼2号楼4楼4003',
                     'longitude': '116.438431', 'latitude': '39.907517', 'area': '建外大街', 'distance': 13921,
                     'tel': '13503051344', 'rating': '4.0', 'closed': False, 'tags': ['寿司']},

                    {'name': '歌志轩名古屋拉面(北京东方新天地店)', 'type': '餐饮服务;外国餐厅;日本料理',
                     'address': '王府井东方广场1号东方新天地LG层', 'longitude': '116.414908', 'latitude': '39.909051',
                     'area': '东单', 'distance': 12283, 'rating': '3.5', 'closed': False, 'tags': ['拉面']}]
            },
        }
    poi_search_info = json.dumps(poi_search_info,ensure_ascii=False)
    history.poi_search_record(poi_search_info)

    history_prompt, poi_list = history.integrate_get_history_prompt()
    integrate_output = integrate_agent(IntegrateInput(history=history_prompt,poi_list=poi_list)).results
    print(integrate_output)


