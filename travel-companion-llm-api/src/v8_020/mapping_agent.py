from .client import client
from pydantic import BaseModel
import json
import time
from .history_management import History
from .mapping_agent_stream import (
    mapping_agent_call,
    mapping_agent_process,
    MappingInput,
    MappingOutput,
)

tag_kv = {'0': '大团体','1': '小团体','2': 'DIY','3': '包厢','4': '包场','5': '吧台','6': '卡座','7': '宴会厅','8': 'wifi','9': '宝宝椅','10': '咖啡','11': '主题餐厅','12': '卡通主题餐厅','13': '亲子主题餐厅','14': '充电','15': '宠物禁入','16': '宠物友好','17': '地铁','18': '等位区','19': '自助餐','20': '儿童游乐区','21': '好停车','22': '高空城景','23': '工业风格','24': '旋转餐厅','25': '别墅餐厅','26': '音乐餐厅','27': '海景餐厅','28': '山景餐厅','29': '湖景餐厅','30': '花园餐厅','31': '江景餐厅','32': '庭院餐厅','33': '环境好','34': '评分高','35': '服务好','36': '味道好','37': '表演','38': '景观位','39': '预订','40': '手机支付','41': '老洋房','42': '露天','43': '猫咖','44': '赛事直播','45': '商场','46': '沿街','47': '无烟','48': '残疾人友好','49': '小清新风格','50': '复古风格','51': '日式','52': '异国风情'}
type_kv = {'0': '汽车服务','0.0': '汽车服务相关','0.0.0': '汽车服务相关','0.1': '加油站','0.1.0': '加油站','0.1.1': '中国石化','0.1.2': '中国石油','0.1.3': '壳牌','0.1.4': '美孚','0.1.5': '加德士','0.1.6': '东方','0.1.7': '中石油碧辟','0.1.8': '中石化碧辟','0.1.9': '道达尔','0.1.10': '埃索','0.1.11': '中化道达尔','0.2': '其它能源站','0.2.0': '其它能源站','0.3': '加气站','0.3.0': '加气站','0.4': '汽车养护/装饰','0.4.0': '汽车养护','0.4.1': '加水站','0.5': '洗车场','0.5.0': '洗车场','0.6': '汽车俱乐部','0.6.0': '汽车俱乐部','0.7': '汽车救援','0.7.0': '汽车救援','0.8': '汽车配件销售','0.8.0': '汽车配件销售','0.9': '汽车租赁','0.9.0': '汽车租赁','0.9.1': '汽车租赁还车','0.10': '二手车交易','0.10.0': '二手车交易','0.11': '充电站','0.11.0': '充电站','0.11.1': '换电站','0.11.2': '充换电站','0.11.3': '专用充电站','1': '餐饮服务','1.0': '餐饮相关场所','1.0.0': '餐饮相关','1.1': '中餐厅','1.1.0': '中餐厅','1.1.1': '综合酒楼','1.1.2': '四川菜(川菜)','1.1.3': '广东菜(粤菜)','1.1.4': '山东菜(鲁菜)','1.1.5': '江苏菜','1.1.6': '浙江菜','1.1.7': '上海菜','1.1.8': '湖南菜(湘菜)','1.1.9': '安徽菜(徽菜)','1.1.10': '福建菜','1.1.11': '北京菜','1.1.12': '湖北菜(鄂菜)','1.1.13': '东北菜','1.1.14': '云贵菜','1.1.15': '西北菜','1.1.16': '老字号','1.1.17': '火锅店','1.1.18': '特色/地方风味餐厅','1.1.19': '海鲜酒楼','1.1.20': '中式素菜馆','1.1.21': '清真菜馆','1.1.22': '台湾菜','1.1.23': '潮州菜','1.2': '外国餐厅','1.2.0': ' 外国餐厅','1.2.1': '西餐厅(综合风味)','1.2.2': '日本料理','1.2.3': '韩国料理','1.2.4': '法式菜品餐厅','1.2.5': '意式菜品餐厅','1.2.6': '泰国/越南菜品餐厅','1.2.7': '地中海风格菜品','1.2.8': '美式风味','1.2.9': '印度风味','1.2.10': '英国式菜品餐厅','1.2.11': '牛扒店(扒房)','1.2.12': '俄国菜','1.2.13': '葡国菜','1.2.14': '德国菜','1.2.15': '巴西菜','1.2.16': '墨西哥菜','1.2.17': '其它亚洲菜','1.3': '快餐厅','1.3.0': '快餐厅','1.3.1': '肯德基','1.3.2': '麦当劳','1.3.3': '必胜客','1.3.4': '永和豆浆','1.3.5': '茶餐厅','1.3.6': '大家乐','1.3.7': '大快活','1.3.8': '美心','1.3.9': '吉野家','1.3.10': '仙跡岩','1.3.11': '呷哺呷哺','1.4': '休闲餐饮场所','1.4.0': '休闲餐饮场所','1.5': '咖啡厅','1.5.0': '咖啡厅','1.5.1': '星巴克咖啡','1.5.2': '上岛咖啡','1.5.3': 'Pacific CoffeeCompany','1.5.4': '巴黎咖啡店','1.6': '茶艺馆','1.6.0': '茶艺馆','1.7': '冷饮店','1.7.0': '冷饮店','1.8': '糕饼店','1.8.0': '糕饼店','1.9': '甜品店','1.9.0': '甜品店','2': '购物服务','2.0': '购物相关场所','2.0.0': '购物相关场所','2.1': '商场','2.1.0': '商场','2.1.1': '购物中心','2.1.2': '普通商场','2.1.3': '免税品店','2.2': '便民商店/便利店','2.2.0': '便民商店/便利店','2.2.1': '7-ELEVEn便利店','2.2.2': 'OK便利店','2.3': '家电电子卖场','2.3.0': '家电电子卖场','2.3.1': '综合家电商场','2.3.2': '国美','2.3.3': '大中','2.3.4': '苏宁','2.3.5': '手机销售','2.3.6': '数码电子','2.3.7': '丰泽','2.3.8': '苏宁镭射','2.4': '超级市场','2.4.0': '超市','2.4.1': '家乐福','2.4.2': '沃尔玛','2.4.3': '华润','2.4.4': '北京华联','2.4.5': '上海华联','2.4.6': '麦德龙','2.4.7': '乐天玛特','2.4.8': '华堂','2.4.9': '卜蜂莲花','2.4.10': '屈臣氏','2.4.11': '惠康超市','2.4.12': '百佳超市','2.4.13': '万宁超市','2.5': '花鸟鱼虫市场','2.5.0': '花鸟鱼虫市场','2.5.1': '花卉市场','2.5.2': '宠物市场','2.6': '家居建材市场','2.6.0': '家居建材市场','2.6.1': '家具建材综合市场','2.6.2': '家具城','2.6.3': '建材五金市场','2.6.4': '厨卫市场','2.6.5': '布艺市场','2.6.6': '灯具瓷器市场','2.7': '综合市场','2.7.0': '综合市场','2.7.1': '小商品市场','2.7.2': '旧货市场','2.7.3': '农副产品市场','2.7.4': '果品市场','2.7.5': '蔬菜市场','2.7.6': '水产海鲜市场','2.8': '文化用品店','2.8.0': '文化用品店','2.9': '体育用品店','2.9.0': '体育用品店','2.9.1': '李宁专卖店','2.9.2': '耐克专卖店','2.9.3': '阿迪达斯专卖店','2.9.4': '锐步专卖店','2.9.5': '彪马专卖店','2.9.6': '高尔夫用品店','2.9.7': '户外用品','2.10': '特色商业街','2.10.0': '特色商业街','2.10.1': '步行街','2.11': '服装鞋帽皮具店','2.11.0': '服装鞋帽皮具店','2.11.1': '品牌服装店','2.11.2': '品牌鞋店','2.11.3': '品牌皮具店','2.11.4': '品牌箱包店','2.12': '专卖店','2.12.0': '专营店','2.12.1': '古玩字画店','2.12.2': '珠宝首饰工艺品','2.12.3': '钟表店','2.12.4': '眼镜店','2.12.5': '书店','2.12.6': '音像店','2.12.7': '儿童用品店','2.12.8': '自行车专卖店','2.12.9': '礼品饰品店','2.12.10': '烟酒专卖店','2.12.11': '宠物用品店','2.12.12': '摄影器材店','2.12.13': '宝马生活方式','2.12.14': '土特产专卖店','2.13': '特殊买卖场所','2.13.0': '特殊买卖场所','2.13.1': '拍卖行','2.13.2': '典当行','2.14': '个人用品/化妆品店','2.14.0': '其它个人用品店','2.14.1': '莎莎','3': '交通设施服务','3.0': '交通服务相关','3.0.0': '交通服务相关','3.1': '机场相关','3.1.0': '机场相关','3.1.1': '候机室','3.1.2': '摆渡车站','3.1.3': '飞机场','3.1.4': '机场出发/到达','3.1.5': '直升机场','3.1.6': '机场货运处','3.2': '火车站','3.2.0': '火车站','3.2.1': '候车室','3.2.2': '进站口/检票口','3.2.3': '出站口','3.2.4': '站台','3.2.5': '售票','3.2.6': '退票','3.2.7': '改签','3.2.8': '公安制证','3.2.9': '票务相关','3.2.10': '货运火车站','3.3': '港口码头','3.3.0': '港口码 头','3.3.1': '客运港','3.3.2': '车渡口','3.3.3': '人渡口','3.3.4': '货运港口码头','3.3.5': '进港','3.3.6': '出港','3.3.7': '候船室','3.4': '长途汽车站','3.4.0': '长途汽车站','3.4.1': '进站','3.4.2': '出站','3.4.3': '候车室','3.5': '地铁站','3.5.0': '地铁站','3.5.1': '出入口','3.6': '轻轨站','3.6.0': '轻轨站','3.7': '公交车站','3.7.0': '公交车站相关','3.7.1': '旅游专线车站','3.7.2': '普通公交站','3.7.3': '机场巴士','3.7.4': '快速公交站','3.7.5': '电车站','3.7.6': '智轨车站','3.8': '班车站','3.8.0': '班车站','3.9': '停车场','3.9.0': '停车场相关','3.9.1': '换乘停车场','3.9.2': '公共停车场','3.9.3': '专用停车场','3.9.4': '路边停车场','3.9.5': '停车场入口','3.9.6': '停车场出口','3.9.7': '停车场出入口','4': '体育休闲服务','4.0': '影剧院','4.0.0': '电影院','5': '住宿服务','5.0': '住宿服务相关','5.0.0': '住宿服务相关','5.1': '宾馆酒店','5.1.0': '宾馆酒店','5.1.1': '奢华酒店','5.1.2': '五星级宾馆','5.1.3': '四星级宾馆','5.1.4': '三星级宾馆','5.1.5': '经济型连锁酒店','5.2': '旅馆招待所','5.2.0': '旅馆招待所','5.2.1': '青年旅舍','6': '风景名胜','6.0': ' 风景名胜相关','6.0.0': '旅游景点','6.1': '公园广场','6.1.0': '公园广场','6.1.1': '公园','6.1.2': '动物园','6.1.3': '植物园','6.1.4': '水族馆','6.1.5': '城市广场','6.1.6': '公园内部设施','6.2': '风景名胜','6.2.0': '风景名胜','6.2.1': '世界遗产','6.2.2': '国家级景点','6.2.3': '省级景点','6.2.4': '纪念馆','6.2.5': '寺庙道观','6.2.6': '教堂','6.2.7': '回教寺','6.2.8': '海滩','6.2.9': '观景点','6.2.10': '红色景区','7': ''}


def mapping_agent(item: MappingInput) -> MappingOutput:
    
    item.is_stream =False
    response = mapping_agent_call(item)
    output = mapping_agent_process(item,response)
    return output

if __name__ == "__main__":
    history = History(max_save_poi_list_num=2, max_save_history_num=20)
    user_info = "我想吃青团？"
    history.user_record(user_info)

    # print(history.mapping_get_history_prompt())

    # intent_search_info = {
    #         "recommendPois": ["面包店","中式点心店"],
    #         "Constraints": ["青团"],
    #         "pageIndex": 0,
    #         "tips": "正在查找，请稍等。"
    #     }

    intent_search_info = {
            "recommendPois": ["中式餐馆"],
            "Constraints": ["麻花"],
            "pageIndex": 1,
            "tips": "正在查找，请稍等。"
    }

    intent_search_info = json.dumps(intent_search_info, ensure_ascii=False)
    history = history.mapping_get_history_prompt()
    test_input = MappingInput(history=history, poi_search=intent_search_info)
    mapping_output = mapping_agent(test_input)
    print(mapping_output)
    # print(history.integrate_get_history_prompt())
    # print(history.mapping_get_history_prompt())
