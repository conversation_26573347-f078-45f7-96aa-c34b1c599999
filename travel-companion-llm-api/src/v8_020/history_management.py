import json
import os
import time
from .utiles import get_timestamp, get_uuid
from datetime import datetime
from pathlib import Path
import re


class History():
    def __init__(self, id=-1, max_save_poi_list_num:int = 2, max_save_history_num:int = 20, poi_list_max_len:int = 5):
        self.id = id
        self.history = []
        self.max_save_history_num = max_save_history_num if max_save_history_num > 0 else 0

        # 使用字典替代列表存储 POI 列表，键为 POI 列表的哈希值，值为实际的 POI 列表
        self.POI_list_container = {}
        self.POI_list_save_num = max_save_poi_list_num if max_save_poi_list_num > 0 else 0

        self.poi_list_max_len = poi_list_max_len

    def clear(self):
        self.history = []
        self.POI_list_container = {}

    def get_current_time(self):
        """获取当前时间的格式化字符串"""
        formatted_time = datetime.now().strftime("%Y年%m月%d日 %H:%M:%S:%f")
        return formatted_time

    def clean_useless_history(self):
        """清理超过最大保存数的历史记录"""
        if self.max_save_history_num < len(self.history):
            self.history = self.history[-self.max_save_history_num:]

    def user_record(self, info: str):
        """记录用户的输入信息"""
        history_item = {
            "role": "user",
            "message": info,
            "timestamp": self.get_current_time(),
        }
        self.history.append(history_item)
        self.clean_useless_history()

    def history_process(self):
        """找到最新的用户query，并返回不包含最新query + poi search结果的新列表"""
        query = ""
        query_index = -1
        for index in range(len(self.history)):
            history_item = self.history[index]
            if history_item["role"] == "user":
                query = history_item["message"]
                query_index = index

        new_history = []
        if query_index == -1:
            new_history = self.history
        else:
            for index in range(len(self.history)):
                if index == query_index:
                    continue
                elif "action_type" in self.history[index] and self.history[index]["action_type"] == "poi_search_result":
                    continue
                else:
                    new_history.append(self.history[index])

        return query, new_history

    def mapping_record(self, info: str):
        """记录POI映射的操作"""
        try:
            info = json.loads(info)
        except json.JSONDecodeError:
            print("错误：无法解析提供的JSON数据")
            return

        history_item = {
            "role": "system_action",
            "action_type": "poi_search_mapping",
            "details": json.dumps(info, ensure_ascii=False),
            "timestamp": self.get_current_time(),
        }
        self.history.append(history_item)
        self.clean_useless_history()

    def clean_useless_poi_list(self):
        """清理多余的POI列表，保持在指定数量以内"""
        if len(self.POI_list_container) > self.POI_list_save_num:
            keys = list(self.POI_list_container.keys())
            for key in keys[:-self.POI_list_save_num]:
                del self.POI_list_container[key]

    # def update_history_poi_list_index(self):
    #     for index in range(len(self.history)):
    #         history_item = self.history[index]
    #         if "details" in history_item:
    #             if "poi_list_index" in history_item["details"]:
    #                 self.history[index]["details"]["poi_list_index"] += 1

    def process_poi_list(self, poi_list:str):
        poi_list = json.loads(poi_list)
        # print("poi_list",poi_list)
        if "page" in poi_list:
            poi_list["currentPage"] = poi_list.pop("page")
        for index in range(len(poi_list["dataList"])):
            if 'closed' in poi_list["dataList"][index]:
                del poi_list["dataList"][index]['closed']
        new_poi_list = json.dumps(poi_list,ensure_ascii=False,indent=4)
        return new_poi_list

    def save_poi_list(self, poi_list: str):
        """保存POI列表，并返回POI列表的倒序索引值"""

        # 格式处理
        poi_list = self.process_poi_list(poi_list)

        # 计算 POI 列表的哈希值用于快速查找
        poi_list_hash = hash(poi_list)

        if poi_list_hash in self.POI_list_container:
            # 如果 POI 列表已经存在，返回其倒序索引
            total_poi_count = len(self.POI_list_container)
            # 找到该 POI 列表的顺序索引
            regular_index = list(self.POI_list_container.keys()).index(poi_list_hash)
            # 倒序索引 = 总数 - 顺序索引 - 1
            return total_poi_count - regular_index - 1

        # 保存新的 POI 列表
        self.POI_list_container[poi_list_hash] = poi_list
        self.clean_useless_poi_list()
        # self.update_history_poi_list_index()

        # 倒序返回最新添加的 POI 列表索引，即 0
        return len(self.POI_list_container)-1

    def poi_search_record(self, info: str):
        """记录POI搜索操作"""
        info = re.sub(r'(\"photos\"\s*:\s*)(\[.*?\]|\".*?\")', r'\1"省略"', info)
        try:
            info = json.loads(info)
            poi_list = info["data"]
        except (json.JSONDecodeError, KeyError):
            print("错误：无法解析POI搜索数据")
            return

        if len(poi_list["dataList"])>self.poi_list_max_len:
            poi_list["dataList"] = poi_list["dataList"][:self.poi_list_max_len]

        poi_list = json.dumps(poi_list, ensure_ascii=False, indent=4)
        # index = self.save_poi_list(poi_list_json)

        poi_list = self.process_poi_list(poi_list)

        history_item = {
            "role": "system_action",
            "action_type": "poi_search_result",
            "details": {
                "poi_list_content": poi_list,
            },
            "timestamp": self.get_current_time(),
        }
        self.history.append(history_item)
        self.clean_useless_history()

    def integrate_record(self, info: str):
        """记录推荐的POI列表和推荐内容"""
        try:
            info = json.loads(info)
        except json.JSONDecodeError:
            print("错误：无法解析提供的JSON数据")
            return

        # 保存推荐信息
        history_item = {
            "role": "assistant",
            "message": info["recommend"],
            "timestamp": self.get_current_time(),
        }
        self.history.append(history_item)
        self.clean_useless_history()

        # 保存POI列表
        poi_list = json.dumps(info["poi_list"], ensure_ascii=False)
        index = self.save_poi_list(poi_list)

        history_item = {
            "role": "system_action",
            "action_type": "POI_recommend_sorted_filtered",
            "details":{
                "poi_list_index": index,
            },
            "timestamp": self.get_current_time(),
        }
        self.history.append(history_item)
        self.clean_useless_history()

    def intent_record(self, info: str):
        """根据用户意图进行记录"""
        try:
            info = json.loads(info)
        except json.JSONDecodeError:
            print("错误：无法解析提供的JSON数据")
            return

        action = info.get("action")
        params = info.get("params", {})

        if action == "direct_response":
            history_item = {
                "role": "assistant",
                "message": params.get("response", ""),
                "timestamp": self.get_current_time(),
            }
        elif action == "poi_search":
            del params["tips"]
            history_item = {
                "role": "system_action",
                "action_type": "poi_search",
                "details": json.dumps(params, ensure_ascii=False),
                "timestamp": self.get_current_time(),
            }
        elif action == "navigation":
            POI_list_index = params.get("POI_list_index", 0)
            POI_index = params.get("POI_index", 0)
            # POI_list = self.POI_list_container.get(list(self.POI_list_container.keys())[-(POI_list_index + 1)])
            POI_list = self.POI_list_container.get(list(self.POI_list_container.keys())[POI_list_index])
            POI = json.loads(POI_list)["dataList"][POI_index] if POI_list else {}

            history_item = {
                "role": "system_action",
                "action_type": "navigation",
                "details": json.dumps(POI, ensure_ascii=False),
                "timestamp": self.get_current_time(),
            }
        elif action == "POI_recommend":
            POI_list_index = params.get("POI_list_index", 0)
            history_item = {
                "role": "system_action",
                "action_type": "POI_recommend_selected",
                "details": {
                    "poi_list_index": POI_list_index,
                },
                "timestamp": self.get_current_time(),
            }
        else:
            print("未知的action类型：", action)
            print(info)
            return

        self.history.append(history_item)
        self.clean_useless_history()

    def get_newest_navigation_poi(self):
        poi = ""
        for history_item in reversed(self.history):
            if history_item["role"] == "system_action":
                if history_item["action_type"] == "navigation":
                    poi = history_item["details"]
                    break
        return poi

    def get_no_action_history(self,input_history=[]):

        if len(input_history)>0:
            history = input_history
        else:
            history = self.history

        no_action_history = []
        for history_item in history:
            if history_item["role"] != "system_action":
                no_action_history.append(history_item)

        return no_action_history

    def get_poi_list_by_index(self, poi_list_index: int):
        """通过 POI 列表的倒序索引获取 POI 列表"""
        # 确保 POI_list_index 不超出容器范围
        if 0 <= poi_list_index < len(self.POI_list_container):
            # 获取按倒序排列的 POI_list key
            # POI_list_key = list(self.POI_list_container.keys())[-(poi_list_index + 1)]
            POI_list_key = list(self.POI_list_container.keys())[poi_list_index]
            # 根据 key 获取 POI 列表
            poi_list = self.POI_list_container.get(POI_list_key)
            return poi_list
        else:
            # 索引无效，返回 None 或提示
            print(f"错误：POI_list_index {poi_list_index} 超出范围")
            return ""

    def get_to_recommend_poi_list(self):
        history_item = self.history[-1]
        if history_item["role"] == "system_action":
            if history_item["action_type"] == "POI_recommend_selected":
                poi_list_index = history_item["details"]["poi_list_index"]
                poi_list = self.get_poi_list_by_index(poi_list_index)
            elif history_item["action_type"] == "poi_search_result":
                poi_list = history_item["details"]["poi_list_content"]
            else:
                print("history_item:", history_item)
                poi_list = ""
        else:
            poi_list = ""
            print("history_item:", history_item)

        return poi_list

    def intent_get_history_prompt(self):
        """生成历史记录的提示信息，包括POI列表"""
        history_description = '''
## 当前对话的上下文说明：
1. 历史交互记录由三部分组成：POI列表索引和内容、历史交互、以及当前用户的话。
2. 历史交互记录中的 POI 列表索引，可在 POI列表对照表中找到对应的完整 POI 列表。
3. 历史记录中的 "role" 字段含义：
    - "user"：用户的输入；
    - "assistant"：系统对用户的回复；
    - "system_action"：系统调用工具时的动作记录。
4. 各种 action_type 对应的系统工具输出类型：
    - `poi_search`：POI 初步查询指令；
    - `navigation`：导航目的地的 POI 信息；
    - `POI_recommend_selected`：待推荐工具处理的 POI 列表；
    - `poi_search_mapping`：POI 初步查询指令 经过映射后的 POI 精确查询指令；
    - `POI_recommend_sorted_filtered`：POI 推荐工具返回的处理后列表。
5. POI列表索引和内容中关于页码的字段含义
    - "total"：POI 的总数量；
    - "totalPages"：总页数；
    - "currentPage"：当前页码；
    - "pageSize"：每页显示的POI数量；
6. POI list 的索引序号含义: 序号越大的POI list代表越新的查询。例如当有 3 个 POI 列表时，POI list 2 代表与用户对话中最近一次查询到的POI list，POI list 0代表与用户对话中最早一次查询到的POI list。。
7. 当前用户的话通常是用户最近表达的主要意图。\n
'''

        newest_user_query, part_history = self.history_process()

        part_history_prompt = "## 历史交互记录：\n"
        part_history_prompt += json.dumps(part_history, ensure_ascii=False, indent=4) + "\n\n"

        # POI_list_description = "##POI_list 索引与内容对照表：\n"
        # POI_list_container_display = {}
        # for index, poi_list_hash in enumerate(reversed(self.POI_list_container.keys())):
        #     POI_list_container_display[str(index)] = json.loads(self.POI_list_container[poi_list_hash])
        # POI_list_description += json.dumps(POI_list_container_display,ensure_ascii=False,indent=4)
        # POI_list_description += "...\n\n"

        POI_list_description = "## POI_list 索引与内容对照表：\n"
        # for index, poi_list_hash in enumerate(reversed(self.POI_list_container.keys())):
        for index, poi_list_hash in enumerate(self.POI_list_container.keys()):
            POI_list_description += f"### POI list {index} ：\n"
            POI_list_description += json.dumps(json.loads(self.POI_list_container[poi_list_hash]),ensure_ascii=False,indent=4)
            POI_list_description += "\n\n"
        POI_list_description += "...\n\n"

        query = f"##当前用户说的话：\n{newest_user_query}\n"

        history_prompt = history_description + POI_list_description + part_history_prompt + query

        # history_prompt += "\n 你必须要给出你调用工具和参数选取的解释 \n"
        history_prompt += "\n 开始! \n"

        return history_prompt

    def integrate_get_history_prompt(self):
        """生成历史记录的提示信息，包括POI列表"""

        history_description = '''
\n
当前历史对话的上下文信息内容说明：
1. 包含历史交互记录、当前用户说的话, 当前待推荐的POI_list三部分。
2. 历史交互记录中role为user的条目记录了用户说的话，role为assistant的条目记录了车舱助手对用户说的话，role为system_action的条目记录了车舱助手调用工具的信息。
3. 当前用户说的话代表了用户在历史对话中，最新表达的内容，往往代表了用户的主要意图。\n\n
'''
        newest_user_query, part_history = self.history_process()


        part_history_prompt = "##历史交互记录：\n"
        no_action_history = self.get_no_action_history(part_history)
        part_history_prompt += json.dumps(no_action_history, ensure_ascii=False, indent=4) + "\n\n"

        query = f"##当前用户说的话：\n{newest_user_query}\n\n"

        POI_list_description = "##当前待推荐的POI_list：\n"

        poi_list = json.loads(self.get_to_recommend_poi_list())
        poi_list_datalist = json.dumps(poi_list["dataList"],ensure_ascii=False,indent=4)
        POI_list_description += poi_list_datalist + "\n\n"

        history_prompt = history_description + part_history_prompt + query + POI_list_description
        history_prompt += "\n开始!\n"

        poi_list = json.dumps(poi_list, ensure_ascii=False, indent=4)
        return history_prompt, poi_list

    def get_poi_search_mapping_history(self):
        poi_search_mapping_history = []
        history = self.history
        for history_item in history:
            if history_item["role"] == "system_action":
                if history_item["action_type"] == "poi_search_mapping":
                    poi_search_mapping_history.append(history_item)
        return poi_search_mapping_history

    def mapping_get_history_prompt(self):
        """生成历史记录的提示信息，包括POI列表"""
        history_prompt = "#以下为历史输出信息：\n"
        src_poi_search_mapping_history = self.get_poi_search_mapping_history()
        display_num = 1
        if len(src_poi_search_mapping_history)>display_num:
            select_poi_search_mapping_history = src_poi_search_mapping_history[-(display_num)]
        else:
            select_poi_search_mapping_history = src_poi_search_mapping_history
        history_prompt += json.dumps(select_poi_search_mapping_history, ensure_ascii=False, indent=4) + "\n\n"
        history_prompt += "...\n"
        history_prompt += "\n开始!\n"
        return history_prompt

    def time_difference(self,time_str_start, time_str_end):
        # 定义时间格式
        time_format = "%Y年%m月%d日 %H:%M:%S:%f"

        # 将时间字符串转换为datetime对象
        time_obj_start = datetime.strptime(time_str_start, time_format)
        time_obj_end = datetime.strptime(time_str_end, time_format)

        # 计算时间差
        time_diff = time_obj_end - time_obj_start

        # 返回时间差的总秒数
        return abs(time_diff.total_seconds())

    def save_content(self):
        history_dir = "historic_record"
        os.makedirs(history_dir, exist_ok=True)
        # max_number = 0
        # for filename in os.listdir(history_dir):
        #     match = re.fullmatch(r'^\d+\.txt$', filename)
        #     if match:
        #         # 将匹配到的数字转换为整数
        #         number = int(match.group().replace(".txt",""))
        #         # 更新最大编号
        #         if number > max_number:
        #             max_number = number
        # max_number += 1
        save_path = history_dir + "\\" + f"{self.id}_{get_timestamp()}_history.txt"

        save_POI_list_container = {}
        id = -1
        for key in self.POI_list_container:
            id += 1
            POI_list = self.POI_list_container[key]
            POI_list = json.loads(POI_list)

            save_POI_list_container[str(id)] = POI_list
        prev_timestamp = ""
        save_history = self.history.copy()
        for index in range(len(save_history)):
            if "details" in save_history[index]:
                if isinstance(save_history[index]["details"],str):
                    save_history[index]["details"] = json.loads(save_history[index]["details"])
            if index == 0:
                prev_timestamp = save_history[index]["timestamp"]
            else:
                current_timestamp = save_history[index]["timestamp"]
                cost_time = self.time_difference(prev_timestamp,current_timestamp)
                prev_timestamp = current_timestamp
                save_history[index]["cost_time"] = cost_time

        save_POI_list_container = json.dumps(save_POI_list_container,ensure_ascii=False,indent=4)
        save_history = json.dumps(save_history, ensure_ascii=False, indent=4)

        save_content = ""
        save_content += "【以下为history的内容】:\n"
        save_content += save_history
        save_content += "【以下为POI_list_container的内容】:\n"
        save_content += save_POI_list_container + "\n\n"

        with open(save_path, 'w', encoding='utf-8') as file:
            file.write(save_content)
            
        return save_path



if __name__ == "__main__":
    history = History(max_save_poi_list_num=2, max_save_history_num=20)

    user_info = "你好，今天天气怎么样？"
    history.user_record(user_info)

    poi_search_info = {
            "state": 200,
            "data":{
                'total': 198, 'totalPages': 40, 'page': 1, 'pageSize': 5,
                'dataList': [
                    {'name': '【】超级和牛烧肉(泓晟国际中心店)', 'type': '餐饮服务;外国餐厅;外国餐厅',
                     'address': '朝阳门北大街泓晟国际中心一层101', 'longitude': '116.433186', 'latitude': '39.930641',
                     'area': '东四', 'distance': 12110,'tel': '19910327789',
                     'rating': '4.6', 'closed': False, 'openTime': '11:00-02:00',
                     'tags': ['牛肉', '料理', '豪华生鱼拼', '赤贝刺身', '刺身拼盘', '烤黑虎虾', '烤肉', '极上和牛四种',
                              '寿喜锅', '活海胆', '和牛七拼', '西冷牛排', '和牛五种', '烧烤', '日本料理', '鲜鱼刺身',
                              '野生蓝鳍金枪鱼', '壶渍牛肋条', '刺身', '虎虾天妇罗', '手握寿司']},

                    {'name': '寿司郎', 'type': '餐饮服务;外国餐厅;日本料理', 'address': '长富宫办公楼2号楼4楼4003',
                     'longitude': '116.438431', 'latitude': '39.907517', 'area': '建外大街', 'distance': 13921,
                     'tel': '13503051344', 'rating': '4.0', 'closed': False, 'tags': ['寿司']},

                    {'name': '歌志轩名古屋拉面(北京东方新天地店)', 'type': '餐饮服务;外国餐厅;日本料理',
                     'address': '王府井东方广场1号东方新天地LG层', 'longitude': '116.414908', 'latitude': '39.909051',
                     'area': '东单', 'distance': 12283, 'rating': '3.5', 'closed': False, 'tags': ['拉面']}]
            },
        }
    poi_search_info = json.dumps(poi_search_info,ensure_ascii=False)
    history.poi_search_record(poi_search_info)


    poi_search_info = {
            "state": 200,
            "data":{
                'total': 198, 'totalPages': 40, 'page': 1, 'pageSize': 5,
                'dataList': [
                    {'name': '【】御·本泽和牛烧肉(泓晟国际中心店)', 'type': '餐饮服务;外国餐厅;外国餐厅',
                     'address': '朝阳门北大街泓晟国际中心一层101', 'longitude': '116.433186', 'latitude': '39.930641',
                     'area': '东四', 'distance': 12110,'tel': '19910327789',
                     'rating': '4.6', 'closed': False, 'openTime': '11:00-02:00',
                     'tags': ['牛肉', '料理', '豪华生鱼拼', '赤贝刺身', '刺身拼盘', '烤黑虎虾', '烤肉', '极上和牛四种',
                              '寿喜锅', '活海胆', '和牛七拼', '西冷牛排', '和牛五种', '烧烤', '日本料理', '鲜鱼刺身',
                              '野生蓝鳍金枪鱼', '壶渍牛肋条', '刺身', '虎虾天妇罗', '手握寿司']},

                    {'name': '寿司郎', 'type': '餐饮服务;外国餐厅;日本料理', 'address': '长富宫办公楼2号楼4楼4003',
                     'longitude': '116.438431', 'latitude': '39.907517', 'area': '建外大街', 'distance': 13921,
                     'tel': '13503051344', 'rating': '4.0', 'closed': False, 'tags': ['寿司']},

                    {'name': '歌志轩名古屋拉面(北京东方新天地店)', 'type': '餐饮服务;外国餐厅;日本料理',
                     'address': '王府井东方广场1号东方新天地LG层', 'longitude': '116.414908', 'latitude': '39.909051',
                     'area': '东单', 'distance': 12283, 'rating': '3.5', 'closed': False, 'tags': ['拉面']}]
            },
        }
    poi_search_info = json.dumps(poi_search_info,ensure_ascii=False)
    history.poi_search_record(poi_search_info)

    integrate_info = {
        "recommend": "推荐您去咖啡厅B，营业时间最晚。",
        "poi_list":{
            'total': 198, 'totalPages': 40, 'page': 1, 'pageSize': 5,
            'dataList': [
                {'name': '抽象烤牛肉(泓晟国际中心店)', 'type': '餐饮服务;外国餐厅;外国餐厅',
                 'address': '朝阳门北大街泓晟国际中心一层101', 'longitude': '116.433186', 'latitude': '39.930641',
                 'area': '东四', 'distance': 12110,'tel': '19910327789',
                 'rating': '4.6', 'closed': False, 'openTime': '11:00-02:00',
                 'tags': ['牛肉', '料理', '豪华生鱼拼', '赤贝刺身', '刺身拼盘', '烤黑虎虾', '烤肉', '极上和牛四种',
                          '寿喜锅', '活海胆', '和牛七拼', '西冷牛排', '和牛五种', '烧烤', '日本料理', '鲜鱼刺身',
                          '野生蓝鳍金枪鱼', '壶渍牛肋条', '刺身', '虎虾天妇罗', '手握寿司']},

                {'name': '寿司郎', 'type': '餐饮服务;外国餐厅;日本料理', 'address': '长富宫办公楼2号楼4楼4003',
                 'longitude': '116.438431', 'latitude': '39.907517', 'area': '建外大街', 'distance': 13921,
                 'tel': '13503051344', 'rating': '4.0', 'closed': False, 'tags': ['寿司']},

                {'name': '歌志轩名古屋拉面(北京东方新天地店)', 'type': '餐饮服务;外国餐厅;日本料理',
                 'address': '王府井东方广场1号东方新天地LG层', 'longitude': '116.414908', 'latitude': '39.909051',
                 'area': '东单', 'distance': 12283, 'rating': '3.5', 'closed': False, 'tags': ['拉面']}]
        },
    }
    integrate_info = json.dumps(integrate_info)
    history.integrate_record(integrate_info)

    integrate_info = {
        "recommend": "推荐您去烤肉店A，营业时间最晚。",
        "poi_list":{
            'total': 198, 'totalPages': 40, 'page': 1, 'pageSize': 5,
            'dataList': [
                {'name': '超级碗(泓晟国际中心店)', 'type': '餐饮服务;外国餐厅;外国餐厅',
                 'address': '朝阳门北大街泓晟国际中心一层101', 'longitude': '116.433186', 'latitude': '39.930641',
                 'area': '东四', 'distance': 12110,'tel': '19910327789',
                 'rating': '4.6', 'closed': False, 'openTime': '11:00-02:00',
                 'tags': ['牛肉', '料理', '豪华生鱼拼', '赤贝刺身', '刺身拼盘', '烤黑虎虾', '烤肉', '极上和牛四种',
                          '寿喜锅', '活海胆', '和牛七拼', '西冷牛排', '和牛五种', '烧烤', '日本料理', '鲜鱼刺身',
                          '野生蓝鳍金枪鱼', '壶渍牛肋条', '刺身', '虎虾天妇罗', '手握寿司']},

                {'name': '寿司郎', 'type': '餐饮服务;外国餐厅;日本料理', 'address': '长富宫办公楼2号楼4楼4003',
                 'longitude': '116.438431', 'latitude': '39.907517', 'area': '建外大街', 'distance': 13921,
                 'tel': '13503051344', 'rating': '4.0', 'closed': False, 'tags': ['寿司']},

                {'name': '歌志轩名古屋拉面(北京东方新天地店)', 'type': '餐饮服务;外国餐厅;日本料理',
                 'address': '王府井东方广场1号东方新天地LG层', 'longitude': '116.414908', 'latitude': '39.909051',
                 'area': '东单', 'distance': 12283, 'rating': '3.5', 'closed': False, 'tags': ['拉面']}]
        },
    }
    integrate_info = json.dumps(integrate_info)
    history.integrate_record(integrate_info)

    integrate_info = {
        "recommend": "推荐您去烤肉店C，营业时间最晚。",
        "poi_list":{
            'total': 198, 'totalPages': 40, 'page': 1, 'pageSize': 5,
            'dataList': [
                {'name': '有很多烤肉(泓晟国际中心店)', 'type': '餐饮服务;外国餐厅;外国餐厅',
                 'address': '朝阳门北大街泓晟国际中心一层101', 'longitude': '116.433186', 'latitude': '39.930641',
                 'area': '东四', 'distance': 12110,'tel': '19910327789',
                 'rating': '4.6', 'closed': False, 'openTime': '11:00-02:00',
                 'tags': ['牛肉', '料理', '豪华生鱼拼', '赤贝刺身', '刺身拼盘', '烤黑虎虾', '烤肉', '极上和牛四种',
                          '寿喜锅', '活海胆', '和牛七拼', '西冷牛排', '和牛五种', '烧烤', '日本料理', '鲜鱼刺身',
                          '野生蓝鳍金枪鱼', '壶渍牛肋条', '刺身', '虎虾天妇罗', '手握寿司']},

                {'name': '寿司郎', 'type': '餐饮服务;外国餐厅;日本料理', 'address': '长富宫办公楼2号楼4楼4003',
                 'longitude': '116.438431', 'latitude': '39.907517', 'area': '建外大街', 'distance': 13921,
                 'tel': '13503051344', 'rating': '4.0', 'closed': False, 'tags': ['寿司']},

                {'name': '歌志轩名古屋拉面(北京东方新天地店)', 'type': '餐饮服务;外国餐厅;日本料理',
                 'address': '王府井东方广场1号东方新天地LG层', 'longitude': '116.414908', 'latitude': '39.909051',
                 'area': '东单', 'distance': 12283, 'rating': '3.5', 'closed': False, 'tags': ['拉面']}]
        },
    }
    integrate_info = json.dumps(integrate_info)
    history.integrate_record(integrate_info)

    mapping_info ={
      "poiType": ["电影院"],
      "tags": ["评分高"],
      "position": "海淀区",
      "otherPois": [{"keyword": "麦当劳"}]
    }
    mapping_info = json.dumps(mapping_info)
    history.mapping_record(mapping_info)

    intent_response_info = {
        "action": "direct_response",
        "params": {
            "response":"一年有12个月"
        }
    }
    intent_response_info = json.dumps(intent_response_info,ensure_ascii=False)
    history.intent_record(intent_response_info)

    intent_search_info = {
        "action": "poi_search",
        "params": {
            "recommendPois": ["北京大学"],
            "Constraints": [],
            "pageIndex": 0,
            "tips":"正在导航到北京大学，请稍等。"
        }
    }
    intent_search_info = json.dumps(intent_search_info, ensure_ascii=False)
    history.intent_record(intent_search_info)

    intent_navigation_info = {
      "action": "navigation",
      "params": {
        "POI_list_index":0,
        "POI_index":0,
      }
    }
    intent_navigation_info = json.dumps(intent_navigation_info, ensure_ascii=False)
    history.intent_record(intent_navigation_info)

    user_info = "你好，我有一些冷"
    history.user_record(user_info)

    intent_recommend_info = {
        "action": "POI_recommend",
        "params": {
            "POI_list_index":1
        }
    }
    intent_recommend_info = json.dumps(intent_recommend_info, ensure_ascii=False)
    history.intent_record(intent_recommend_info)

    # print(history.intent_get_history_prompt())
    print(history.integrate_get_history_prompt()[0])
    # print(history.mapping_get_history_prompt())

    # print(history.save_content())