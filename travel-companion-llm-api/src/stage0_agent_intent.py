from src.utiles.agent import Agent

#### this is stage 0, intent rewrite

def call_intent_agent(queries, model_name="gpt-4o", task="intent"):
    prompts_file_name = ["router_system_prompt_tb",  "router_user_prompt"]
    replace_strs = ["user_input"]
    intent_agent = Agent(queries,prompts_file_name,replace_strs)

    import time
    start = time.time()
    phrase_result = intent_agent.call_apis(model_name, task)
    print(f"call intent time: {time.time() - start}")
    return phrase_result


if __name__ == "__main__":
    queries= [" "]
    phrase_result = call_intent_agent(queries,"gpt-4o")
    print(phrase_result)


