from src.utiles.agent import Agent
import time
#### this is stage 0, intent rewrite
import signal

# 定义超时异常
class TimeoutException(Exception):
    pass

# 信号处理函数
def handler(signum, frame):
    raise TimeoutException("函数执行超时！")

# 使用装饰器实现超时控制
def timeout_decorator(seconds):
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 设置信号处理器
            signal.signal(signal.SIGALRM, handler)
            # 设置超时
            signal.alarm(seconds)
            try:
                result = func(*args, **kwargs)
            finally:
                # 关闭闹钟
                signal.alarm(0)
            return result
        return wrapper
    return decorator

def call_classifier(queries, snippets, model_name="gpt-4o", task="class"):
    prompts_file_name = ["classifier_system_prompt",  "classifier_user_prompt"]
    replace_strs = ["user_input", "snippets"]
    intent_agent = Agent(queries, prompts_file_name, replace_strs, snippets=snippets)

    start = time.time()
    phrase_result = intent_agent.call_apis(model_name, task)
    print(f"classifier time: {time.time() - start}")
    return phrase_result

@timeout_decorator(3)
def call_classifier_big(queries, snippets, model_name="gpt-4o", task="class"):
    prompts_file_name = ["classifier_big_system_prompt",  "classifier_big_user_prompt"]
    replace_strs = ["user_input", "snippets"]
    intent_agent = Agent(queries, prompts_file_name, replace_strs, snippets=snippets)

    start = time.time()
    phrase_result = intent_agent.call_apis(model_name, task)
    print(f"classifier time: {time.time() - start}")
    return phrase_result

@timeout_decorator(3)
def call_classifier_mid(queries, snippets, model_name="gpt-4o", task="class"):
    prompts_file_name = ["classifier_mid_system_prompt",  "classifier_mid_user_prompt"]
    replace_strs = ["user_input", "snippets"]
    intent_agent = Agent(queries, prompts_file_name, replace_strs, snippets=snippets)

    start = time.time()
    phrase_result = intent_agent.call_apis(model_name, task)
    print(f"classifier time: {time.time() - start}")
    return phrase_result

@timeout_decorator(3)
def call_classifier_sub(queries, snippets, model_name="gpt-4o", task="class"):
    prompts_file_name = ["classifier_sub_system_prompt",  "classifier_sub_user_prompt"]
    replace_strs = ["user_input", "snippets"]
    intent_agent = Agent(queries, prompts_file_name, replace_strs, snippets=snippets)

    start = time.time()
    phrase_result = intent_agent.call_apis(model_name, task)
    print(f"classifier time: {time.time() - start}")
    return phrase_result

if __name__ == "__main__":
    queries= [" "]
    phrase_result = call_intent_agent(queries,"gpt-4o")
    print(phrase_result)


