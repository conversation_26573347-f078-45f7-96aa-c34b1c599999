import requests
import json

class NaviPlanningPlugin:
    def __init__(self):
        self.function_description = {
            "name": "navi_planning",
            "description": "设置路径规划",
            "input_parameters": {
                "type": "object",
                "description": "输入参数",
                "properties": {
                    "origin": {
                        "type": "string",
                        "description": "起点位置的名称，例如：北京市天安门",
                    },
                    "destination": {
                        "type": "string",
                        "description": "终点位置的名称，例如：北京市朝阳公园",
                    }
                },
                "required": ["origin", "destination"],
            },
            "output_parameters": {
                "type": "object",
                "description": "输出参数",
                "properties": {
                    "message": {
                        "type": "string",
                        "description": "返回输出结果",
                    }
                },
                "required": ["message"],
            },
        }

    def geocode(self,api_key, address):  # 将地址转换为经纬度坐标
        url = 'https://restapi.amap.com/v3/geocode/geo'
        params = {
            'key': api_key,
            'address': address,
            'output': 'JSON'
        }
        response = requests.get(url, params=params)
        return response.json()
    
    
    def get_route(self,api_key, origin, destination):    # 路径规划
        url = 'https://restapi.amap.com/v3/direction/driving'
        params = {
            'key': api_key,
            'origin': origin,
            'destination': destination,
        }
        response = requests.get(url, params=params)
        return response.json()
    
    
    def run(self, origin, destination):
        api_key = 'ee7026026a68f681887b22e0502063da'    # 使用的高德API
        
        origin_address = self.geocode(api_key, origin)
        destination_address = self.geocode(api_key, destination)
        
        if origin_address.get('status') == '1' and destination_address.get('status') == '1':
            start_geo = origin_address.get('geocodes', [])[0].get('location')
            end_geo = destination_address.get('geocodes', [])[0].get('location')
            
            if start_geo and end_geo:
                route = self.get_route(api_key, start_geo, end_geo)  # 路径规划

                if route.get('status') == '1':  # 打印路径规划结果
                    route_info = route.get('route', {})
                    paths = route_info.get('paths', [])
                    if paths:
                        path = paths[0]
                        #distance = path.get('distance')  # 路程距离
                        #duration = path.get('duration')  # 预计时间（秒）
                        steps = path.get('steps', [])  # 路线详情

                        result = {"plannings": []}
                        for index,step in enumerate(steps):
                            result["plannings"].append({"index": index, "planning": step.get('instruction')})
                        return json.dumps(result, ensure_ascii=False, indent=4)

                    else:
                        return json.dumps({"message": "没有找到路径"}, ensure_ascii=False, indent=4)
                else:
                    return json.dumps({"message": f"路径规划错误: {route.get('info')}"}, ensure_ascii=False, indent=4)

            else:
                return json.dumps({"message": "地址解析失败"}, ensure_ascii=False, indent=4)
        else:
            return json.dumps({"message": "地址解析错误"}, ensure_ascii=False, indent=4)


if __name__ == "__main__":
    tool = NaviPlanningPlugin()
    origin = '深圳南山金海岸大厦'  # 起点位置
    destination = '南山来福士'  # 终点位置
    response = tool.run(origin, destination)
    print(f'ending')
    response_json = json.loads(response)
    print(response_json)
    function_description = tool.function_description
    print(function_description)