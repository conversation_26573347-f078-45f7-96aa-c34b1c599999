Convert user's query to a json to search with a offline database.


"""

If user query:找个有卖喜茶的能看电影的购物中心. You should return like this:
```json
Search(
    Poi=购物中心，
    search_radius=5000m.
    location =(100.100,200.200)  #用户的当前的经纬度
    poi_tag=[宠物友好，好停车]
    filter= {
        other_poi = [
            {
                Poi=喜茶,
                Search_radius =100m
            }，
            {
                Poi= 电影院，
                search_radius=100m
            }
        ],
    }
)



"""Begin!

Previous conversation history:
None

Current query: 
{user_input}