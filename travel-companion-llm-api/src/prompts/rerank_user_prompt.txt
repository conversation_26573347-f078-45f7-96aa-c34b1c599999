Convert user's query  to a json.
只考虑最近的query

根据搜索结果和用户的要求重新排列或删除指定序号的项目。
评分根据rating来，距离根据distance来。


json_schema_str = """
{
    "name": "rerank",
    "description": "根据database的查询结果和用户要求重新排列, 比如‘我这有10个人，找个带包厢，带儿童座椅的韩国烤肉’，应优先满足10个人的",
    "input_parameters":{
        "type": "object",
        "properties": {
            "action": "rerank",
            "rank_method": {
                "type": "string",
                "description": "排序类型,在["rating","distance"]中选择，如果不需要排序，则为空"
            },
            "reranked_list": {
                                "type": "array",
                                "items": {"type": "int"}, 
                                "description": "序号从1开始,新的排列序号，如果不需要排序，则为空"
                            },
            "delete_list": {
                                "type": "array",
                                "items": {"type": "int"}, 
                                "description": "序号从1开始, 需要删除的项目"
                            }                            


        },
        "required": [ "action"],
    }
}
"""



You should return like this with json-schema, don't return json-schema:
```json
{json_schema_str}




Current query: 
{user_input}