# Examples and RESPONSE FORMAT
## Snippets with index: 
1: ['title': 'What Happened to Silicon Valley Bank? Why Is It in Trouble?', 'snippet': 'Unable to provide enough liquidity to satisfy customer demands, the bank declared insolvency on March 10, 2023. It was the largest bank failure ...']
2: ['title': 'Silicon Valley Bank - Santa Clara, California - FDIC', 'snippet': " Silicon Valley Bank (SVB) is a commercial bank division of First Citizens BancShares..."]
3: ['title': 'Silicon Valley Bank: What happened? - Fidelity Investments', 'snippet': 'Investors in SVB are likely to receive little to no value for their shares. Credit Suisse investors will receive a fraction of the value that ...']

## Current query: 
What Happened to Silicon Valley Bank? Give me most TWO relevent index.

# You should return:
```json
{{
    "Thought": "
Snippet 1: 提到“无法提供足够的流动性来满足客户需求，银行于2023年3月10日宣布破产。” 这段内容直接描述了Silicon Valley Bank的问题和破产原因，与查询非常相关。
Snippet 2: 提到“Silicon Valley Bank (SVB) 是 First Citizens BancShares 的商业银行分部...” 这段内容主要描述了SVB的身份和所属关系，没有直接回答发生了什么，与查询相关性较低。
Snippet 3: 提到“Silicon Valley Bank 在不到两天内崩溃，FDIC 监管机构接管。” 这段内容描述了SVB崩溃的快速过程，提供了一些关于事件的时间线信息，与查询相关，但不如Snippet 1具体解释原因。",
    "Action_input": [1,3]
}}

# Snippets with index: 
{snippets}

# Previous conversation history:
None

# Current user query: 
{user_input}

# You Must give me most {Top_k} relevant index with query

"""Begin!

