Convert user's query  to a json to navigation.
只考虑最近的query
评分根据rating来，距离根据distance来。


json_schema_str = """
{
    "name": "navigation",
    "description": "根据database的查询结果和用户要求选择目标",
    "input_parameters":{
        "type": "object",
        "properties": {
            "action": "navigation",
            "target": {
                "type": "object",
                "description": "用户选择的目标序号"
                "index": {
                    "type": "int",
                    "description": "目标的序号,序号从1开始,不考虑otherPois里的"

                }
            }

        },
        "required": [ "action", "target"],
    }
}
"""



You should return like this with json-schema, don't return json-schema:
```json
{json_schema_str}




Current query: 
{user_input}