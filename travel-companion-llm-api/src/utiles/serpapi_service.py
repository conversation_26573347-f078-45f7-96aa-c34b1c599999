import requests
import re
import json
import yaml
import os
import serpapi

class SerpapiClient:
    def __init__(self):
        # Load configuration from config.yaml file
        self.query = None
        # config_path = os.path.join(os.path.dirname(__file__), 'config', 'config.yaml')
        config_path = os.path.join('config', 'config.yaml')
        with open(config_path, 'r') as file:
            config = yaml.safe_load(file)

        self.client = serpapi.Client(api_key=config["serpapi_key"])

    def serper(self, query: str,engine:str):
        self.query = query
        results = self.client.search({
            'engine': engine,
            'q': query,
        })
        return results


# Usage example
if __name__ == "__main__":    
    client = SerpapiClient()
    query = "What happened to Silicon Valley Bank"
    response = client.serper(query,"baidu")
    print(response)
    components = client.extract_components(response)
    print(components)
