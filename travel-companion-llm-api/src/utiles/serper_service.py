import requests
import re
import json
import yaml
import os



class SerperClient:
    def __init__(self):
        # Load configuration from config.yaml file
        # config_path = "config\config.yaml"
        current_dir = os.path.dirname(__file__)
        config_path = os.path.join(current_dir, '..', 'config', 'config.yaml')
        with open(config_path, 'r') as file:
            self.config = yaml.safe_load(file)

        # Set up the URL and headers for the Serper API
        self.url = "https://google.serper.dev/search"
        self.headers = {
            "X-API-KEY": self.config["serper_api_key"],  # API key from config file
            "Content-Type": "application/json"
        }

    def serper(self, query: str):
        # Configure the query parameters for Serper API
        serper_settings = {"q": query, "page": 1}

        payload = json.dumps(serper_settings)

        # Perform the POST request to the Serper API and return the JSON response
        response = requests.request("POST", self.url, headers=self.headers, data=payload)
        return response.json()


# Usage example
if __name__ == "__main__":
    client = SerperClient()
    query = "What happened to Silicon Valley Bank"
    response = client.serper(query)
    components = client.extract_components(response)
    print(components)
