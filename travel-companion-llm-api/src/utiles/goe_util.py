import requests
from typing_extensions import Optional
from configuration import config


api_key = config["gaode"]["api_key"]
url_regeo = config["gaode"]["regeo"]["url"]


def _request_re_geo(lat: str, lon: str) -> Optional[dict]:
    try:
        location = format(float(lon), ".6f") + "," + format(float(lat), ".6f")
    except BaseException as e:
        print(e)
        return None
    param = {"key": api_key, "location": location, "radius":"100", "output": "JSON"}
    response = requests.post(url=url_regeo, params=param)
    print(f"!!!!!!!!!!!!!!!!!!!!    {response.status_code}")
    if 200 == response.status_code:
        return response.json()
    else:
        return None
    

async def request_re_geo_rough(lat: str, lon: str)-> Optional[str]:
    position_json = _request_re_geo(lat=lat, lon=lon)
    print("!!!!!!!!!!!!!!!!!!!!    ")
    print(position_json)
    if not position_json:
        return None
    if isinstance(position_json, dict):
        if "1" == position_json["status"] and position_json["regeocode"]:
            regeocode = position_json["regeocode"]
            position = ""
            addressComponent = regeocode["addressComponent"]
            position += addressComponent.get("province", "")
            city_list = addressComponent.get("city")
            if isinstance(city_list, str):
                position = city_list
            elif isinstance(city_list, list) and 0 < len(city_list) and isinstance(city_list[0], str):
                position = city_list[0]
            return position
        else:
            return None
    else: 
        return None

async def request_re_geo_detail(lat: str, lon: str)-> Optional[str]:
    position_json = _request_re_geo(lat=lat, lon=lon)
    if not position_json:
        return None
    if isinstance(position_json, dict):
        if "1" == position_json["status"] and position_json["regeocode"]:
            regeocode = position_json["regeocode"]
            position = ""
            addressComponent = regeocode["addressComponent"]
            position += addressComponent.get("province", "")
            city_list = addressComponent.get("city")
            if isinstance(city_list, str):
                position += city_list
            elif isinstance(city_list, list) and 0 < len(city_list) and isinstance(city_list[0], str):
                position += city_list[0]

            position += addressComponent.get("district", "")
            position += addressComponent.get("township", "")
            neighborhood = addressComponent["neighborhood"]
            building = addressComponent["building"]
            streetNumber = addressComponent["streetNumber"]
            if streetNumber: 
                position += streetNumber.get("street", "")
                position += streetNumber.get("number", "")
            elif neighborhood:
                position += neighborhood.get("name", "")
            elif building:
                position += building.get("name", "")
            return position
        else:
            return None
    else: 
        return None