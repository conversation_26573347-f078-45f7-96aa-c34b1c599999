import requests
import json
import os


def reranker(query: str, positive_lst: list, doc_details: list=None) -> list:
    if doc_details is None:
        doc_details = [""]*len(positive_lst)
    url = "http://10.198.6.242:7100/v1/rank"
    response = requests.post(
        url=url,
        json={
            "inputs": [
                {
                    'query': query,
                    'positive_lst': positive_lst,
                    'doc_details': doc_details
                }
            ]
        },
        headers={
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    )
    if response.status_code == 200:
        #print(response.json()['outputs'])
        data = response.json()['outputs'][0]["resp"]
    else:
        print(f"Error: {response.status_code}, {response.json()}")
        data = f"Error: {response.status_code}, {response.json()}"
    return data
