from src.utiles.serper_service import SerperClient
from src.utiles.serpapi_service import SerpapiClient


#### this is stage 1, web search

class WebSearch:
    def __init__(self):
        # Initialize the fetcher with a search query
        pass

    def extract_info_with_idx(self, data):
        with_link = {}
        without_link = {}
        idx = 1

        def extract_from_dict(d):
            nonlocal idx
            if 'title' in d and 'snippet' in d and 'link' in d:
                with_link[idx] = {'title': d['title'], 'snippet': d['snippet'], 'link': d['link']}
                without_link[idx] = {'title': d['title'], 'snippet': d['snippet']}
                idx += 1

            # import pdb;pdb.set_trace()
            
            # for key, value in d.items():
            #     if isinstance(value, dict):
            #         extract_from_dict(value)
            #     elif isinstance(value, list):
            #         for item in value:
            #             if isinstance(item, dict):
            #                 extract_from_dict(item)

        for item in data:
            extract_from_dict(item)
        return with_link

    def _web_launcher(self, query, engine=None):
        # Function to launch the Serper client and get search results
        # import pdb;pdb.set_trace()
        if engine:
            # serper_client = SerpapiClient()["organic_results"]
            serper_client = SerpapiClient()
            search_results = serper_client.serper(query, engine)
            return self.extract_info_with_idx(search_results)
        else:
            serper_client = SerperClient()
            search_results = serper_client.serper(query)["organic"]
            return self.extract_info_with_idx(search_results)
