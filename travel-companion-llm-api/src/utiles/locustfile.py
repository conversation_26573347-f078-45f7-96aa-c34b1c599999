from locust import HttpUser, task, between

class MyUser(HttpUser):
    wait_time = between(1, 5)

    @task(1)
    def post_query(self):
        request_json = {
            "messages": [
                {
                    "role": "user",
                    "content": "帮我找一家餐厅"
                }
            ]
        }
        self.client.post("/poc/avatr/travel-companion-llm-wrapper/v6/travel_companion/intent", json=request_json)

    @task(1)
    def post_query(self):
        request_json = {
            "messages": [
                {
                    "role": "user",
                    "content": "我感觉有点饿"
                },
                {
                    "role": "assistant",
                    "content": "你想找家餐厅吗?"
                },
                {
                    "role": "user",
                    "content": "帮我找一家附近能停车的麦当劳"
                }
            ]
        }
        self.client.post("/poc/avatr/travel-companion-llm-wrapper/v6/travel_companion/intent", json=request_json)
