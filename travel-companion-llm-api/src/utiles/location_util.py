from loguru import logger

from main import LocationReq
from typing_extensions import Union, Optional
DEFAULT_LOCATION = "北京"
import asyncio
import time

async def require_text_location(source: Optional[LocationReq], start_time_perf_counter: float, time_limit: float) -> [Optional[str], Optional[float]]:
    if not source:
        return DEFAULT_LOCATION, round(time.perf_counter() - start_time_perf_counter, 4)
    async def require_location(source: LocationReq):
        text_location = None
        from src.utiles.cache import rd
        # 只有城市名，如上海为上海市，广东省广州市为广州市
        key = f"text_location_city:{source.build_coordinate_text()}"
        try:
            text_location = await rd.get(key)
            if text_location:
                text_location = text_location.decode('utf-8')
        except BaseException as be:
            logger.error(f"require_location from cache failed, e: {be}")
        if not text_location:
            from src.utiles.goe_util import request_re_geo_rough
            text_location = await request_re_geo_rough(lat=source.lat, lon=source.lon)
            if text_location:
                logger.info(f"query from regeo location: {text_location}")
                try:
                    await asyncio.wait_for(rd.setex(key, 3600 * 3, text_location), timeout=time_limit)
                except BaseException as be:
                    logger.error(f"rd.set error, e: {be}")
            else:
                logger.error(f"query from regeo fail")
        return text_location
    try:
        result = await asyncio.wait_for(require_location(source), timeout=time_limit)
        if result:
            logger.info(f"location: {result}" )
        if not result:
            result = DEFAULT_LOCATION
        return result, round(time.perf_counter() - start_time_perf_counter, 4)
    except Exception as e:
        logger.error("require_text_location timeout")

        return DEFAULT_LOCATION, round(time.perf_counter() - start_time_perf_counter, 4)

