import os
import yaml
import glob
import concurrent.futures
from openai import OpenAI
import re
import json
#### this is stage 2, using agent to choose topk

# import sensenova

class Agent:
    def __init__(self, queries: list, prompt_file_name: list, replace_strs: list, snippets=None,k="3"):
        """
        Agent主要接收要使用的system prompt和user prompt文件,将user prompt和所有输入的queries concat在一起,
        得到queries长度的system prompt和user prompt
        e.g.
        queries = ["我想去一个中餐厅"]
        snippets = ["汽车服务", "餐饮服务", "购物服务", "交通设施服务", "体育休闲服务", "住宿服务", "风景名胜"]
        prompts_file_name = ["classifier_big_system_prompt",  "classifier_big_user_prompt"]
        replace_strs = ["user_input", "snippets"]
        intent_agent = Agent(queries, prompts_file_name, replace_strs, snippets=snippets)
        """
        # Load configuration from config.yaml file
        current_dir = os.path.dirname(__file__)
        config_path = os.path.join('src/config/config.yaml')
        # config_path = "config\config.yaml"
        with open(config_path, 'r') as file:
            self.config = yaml.safe_load(file)
        self.queries = queries
        self.prompts = self.load_prompts("src/prompts")


        # self.prompts={'router_system_prompt':'回答用户问题', 'router_user_prompt':'回答用户问题'}

        # Initialize system prompts
        self.agent_system_prompts = [self.prompts[prompt_file_name[0]]] * len(queries)
        # self.agent_system_prompts = []
        self.agent_user_prompts = []


        # If snippets is provided, ensure its length matches queries length
        if snippets is not None :
            for q in queries:
                self.agent_user_prompts.append(
                    self.insert_str2prompt(
                        self.prompts[prompt_file_name[1]],
                        {replace_strs[0]: q, replace_strs[1]: snippets}
                    )
                )
        else:
            for q in queries:
                self.agent_user_prompts.append(
                    self.insert_str2prompt(
                        self.prompts[prompt_file_name[1]],
                        {replace_strs[0]: q}
                    )
                )

        # print(self.agent_user_prompts)
    def insert_str2prompt(self,prompt, *args):
        combined_dict = {}

        for dic in args:
            combined_dict.update(dic)

        # import pdb;pdb.set_trace()

        # if 'offline_search' in prompt or 'navigation' in prompt:
        #     prompt=prompt.replace('{user_input}', dic['user_input'])
        #     return prompt

        # print(f"dic: {dic}")
        # if '{user_input}' in prompt  :
        #     prompt=prompt.replace('{user_input}', dic['user_input'])
        #     return prompt

        # if '{snippets}' in prompt:
        #     prompt=prompt.replace('{user_input}', dic['user_input'])


        return prompt.format(**combined_dict)


    def load_prompts(self,folder_path):
        file_names = glob.glob(os.path.join(folder_path, '*.txt'))
        file_contents = {}

        for file_name in file_names:
            with open(file_name, 'r', encoding='utf-8') as file:
                file_contents[os.path.basename(file_name).split(".")[0]] = file.read()
        return file_contents


    def gpt_call(self,client, model, tempture, topp, system_prompt, user_prompt):
        completion = client.chat.completions.create(model=model, temperature=tempture, top_p=topp
                                                    , messages=[
                {"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}
            ])
        return completion.choices[0].message.content



    def nova_chat(self, client,  system_prompt, user_prompt, tempture, topp):

        NOVA_AK = "E206DF16D0BC4B18B608659A882CF5C7"
        NOVA_SK = "92037E83B9DE49F8A421553A1E143064"

        sensenova.access_key_id = NOVA_AK
        sensenova.secret_access_key = NOVA_SK
        #resp = sensenova.Model.list()

        model_id = "SenseChat-5"
        stop=""

        response = sensenova.ChatCompletion.create(
            messages=[
                {"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}
            ],
            model=model_id,
            stop = stop,
            temperature=tempture,
            stream = True,
        )

        # import pdb;pdb.set_trace()

        ans = ''
        for chunk in response:

            chunk_str = chunk.data.choices[0].delta
            # print(chunk_str,end='')
            ans = ans + chunk_str


        return ans




    def qwen_call(self, client, model, tempture, topp, system_prompt, user_prompt):

        # fp16
        # model_name = "/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct"


        # model_name ="/mnt/afs/projects/bmw_pk_qwen2_72b_int8/weights/"

        # int8
        model_name = model#"/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct-GPTQ-Int8"

        #zhongchao model
        # model_name = "/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct-Longcontext"
        # model_name = "/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct-GPTQ-Int8/"




        # import time
        # start_time = time.time()

        # print(f"qwen_call user: {user_prompt}")
        # print(f"qwen_call system: {system_prompt}")
        # with open("user_prompt.txt", "a+") as f:
        #     f.write(f"{user_prompt}\n")
        # with open("system_prompt.txt", "a+") as f:
            # f.write(f"{system_prompt}\n")
        completion = client.chat.completions.create(model=model_name, temperature=tempture, top_p=topp
                                                    , messages=[
                {"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}
            ])

        # end_time = time.time()
        # elapsed_time = end_time - start_time

        # print(f"++++++++++++++++++++++++client.chat.completions.create函数调用持续时间: {elapsed_time:.2f} 秒")



        return completion.choices[0].message.content



    def call_gpt_concurrently_repeatly(self,client, call_func, model_name, temperature, topp, prompts, num_calls=1):
        system_prompts, user_prompts = prompts
        results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_calls) as executor:
            if "gpt" in model_name or "deepseek" in model_name:
                futures = [executor.submit(call_func, client, model_name, temperature, topp, system_prompts, user_prompts) for _ in
                           range(num_calls)]
            elif model_name == "SenseChat-5":
                futures = [executor.submit(call_func, system_prompts, user_prompts, temperature, topp) for _ in range(num_calls)]
            elif "qwen" in model_name:
                futures = [executor.submit(call_func, model_name, temperature, topp, system_prompts, user_prompts) for _ in range(num_calls)]
            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    results.append(f"Error: {e}")
        return results



    def call_gpt_concurrently_individually(self, client, call_func, model, temperature, topp, prompts):
        results = [None] * len(prompts[1])  # Initialize a list with the same length as user_prompts
        system_prompts, user_prompts = prompts

        # import pdb;pdb.set_trace()

        with concurrent.futures.ThreadPoolExecutor(max_workers=len(user_prompts)) as executor:
            if "gpt" in model or "deepseek" in model:
                # import pdb;pdb.set_trace()
                futures = {
                    executor.submit(call_func, client, model, temperature, topp, system_prompts[i], user_prompts[i]): i
                    for i in range(len(user_prompts))
                }
            elif model == "SenseChat-5":
                futures = {
                    executor.submit(call_func, client, system_prompts[i], user_prompts[i], temperature, topp): i
                    for i in range(len(user_prompts))
                }
            # 按照user_prompt数量来并发提交多个prompt任务,最后收集其回答
            elif "qwen" in model or "Qwen" in model:
                futures = {
                    executor.submit(call_func, client, model, temperature, topp, system_prompts[i], user_prompts[i]): i
                    for i in range(len(user_prompts))
                }

            for future in concurrent.futures.as_completed(futures):
                index = futures[future]
                try:
                    result = future.result()
                    results[index] = result
                except Exception as e:
                    results[index] = f"Error: {e}"

        return results

    def parse_llm_json_outputs(self,queries,text):

        # pattern = r'{{{{(.+?)}}}}'
        # match = re.search(pattern, text, re.DOTALL)

        # if match:
        #     json_str = match.group(1)
        #     # 替换掉多余的换行和空格
        #     json_str = json_str.strip().replace('\n', '').replace(' ', '')

        # import pdb;pdb.set_trace()
        text = text.strip('###LLM:')

        try:
            data = json.loads(text)
        except json.JSONDecodeError as e:
            print('e==', e)
            print('text==', text)
            data = json.loads('{"Thought": "None.", "Action": "response", "Action_input":"' +text+ ', 请稍后再试"}')

        return data

    def init_clients(self,model_name,task):
        # print(f"init_clients: {model_name} {task}")
        client = None
        if model_name == "SenseChat-5" or model_name == "SenseChat":
            call_func = self.nova_chat

        elif "gpt" in model_name:
            client = OpenAI(
                api_key=self.config["openai_api_key"],
                base_url="http://cubic.sensetime.com:8457/v1")
            call_func = self.gpt_call

        elif model_name == "deepseek-chat":
            client = OpenAI(api_key="sk-e05cd2a70e3841eeaa89ad88fa499f97", base_url="https://api.deepseek.com/")
            call_func = self.gpt_call

        elif "qwen" in model_name:
            # openai_api_key = "EMPTY"
            openai_api_key = "Good@SenseAuto"#os.getenv("LLM_API_KEY")

            # fp16
            # openai_api_base = "http://101.230.144.204:17888/v1"

             # int8
            general_llm_url = "http://103.237.28.240:8080/v1"
            if task == "intent":
                # openai_api_base = "http://101.230.144.204:18090/v1"
                # openai_api_base = "http://103.237.29.217:8000/v1"
                # openai_api_base = "http://10.4.196.19:8000/v1"
                openai_api_base = general_llm_url
            elif task == "class":
                # openai_api_base = "http://101.230.144.204:16908/v1"
                # openai_api_base = "http://103.237.29.217:2223/v1"
                # openai_api_base = "http://10.4.196.19:8000/v1"
                openai_api_base = general_llm_url
            elif task == "filter":
                # openai_api_base = "http://101.230.144.204:16908/v1"
                # openai_api_base = "http://103.237.29.217:2223/v1"
                # openai_api_base = "http://10.4.196.19:8000/v1"
                openai_api_base = general_llm_url
            elif task == "rewriter":
                # openai_api_base = "http://101.230.144.204:16908/v1"
                # openai_api_base = "http://10.4.196.19:8000/v1"
                openai_api_base = general_llm_url

            # int8
            # if task == "intent":
            #     openai_api_base = "http://101.230.144.204:16908/v1"
            # elif task == "class":
            #     openai_api_base = "http://101.230.144.204:16908/v1"

            # openai_api_base = "http://101.230.144.204:16908/v1"
            # zhongchao model
            # openai_api_base = "http://103.237.29.217:12346/v1"
            # openai_api_base = "http://101.230.144.204:10015/v1"
            # openai_api_base = "http://10.4.196.19:8000/v1"
            # openai_api_base = "http://103.237.29.217:2223/v1"

            # openai_api_base = "http://103.237.28.240:8080/v1"



            client = OpenAI( api_key=openai_api_key,  base_url=openai_api_base,)

            call_func = self.qwen_call
        else:
            print("Model not supported")
            return
        return client, call_func



    def call_apis(self, model_name, task, temp=0.001, topp=0.2):
        # print(f"call apis: {model_name} {task}")
        # if task == "filter":
        #     import pdb;pdb.set_trace()
        client, call_func = self.init_clients(model_name, task)
        # if task == "intent": model_name = "/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct"
        # elif task == "filter": model_name = "/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct"#"/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct-GPTQ-Int8/"
        # elif task == "class": model_name = "/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct"#"/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct-GPTQ-Int8/"
        # elif task == "rewriter": model_name = "/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct"
        general_model_name = "/mnt/afs/projects/bmw_pk_qwen2_72b_int8/weights/"
        if task == "intent": model_name = general_model_name
        elif task == "filter": model_name = general_model_name#"/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct-GPTQ-Int8/"
        elif task == "class": model_name = general_model_name#"/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct-GPTQ-Int8/"
        elif task == "rewriter": model_name = general_model_name
        # if task == "intent": model_name = "/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct-GPTQ-Int8"
        # elif task == "class": model_name = "/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct"
        # model_name = "/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct"
        # model_name = "/mnt/afs/share/model/Qwen/Qwen2-72B-Instruct-GPTQ-Int8"
        results = self.call_gpt_concurrently_individually(client, call_func, model_name, temp, topp,
                                                 [self.agent_system_prompts, self.agent_user_prompts],
                                                 )

        return results[0]

        # phrase_result = self.parse_llm_json_outputs(self.queries,results[0])
        # return phrase_result




    def convert2json(self,model_name, temp=0.0001, topp=0.7):
        client, call_func = self.init_clients(model_name)
        results = self.call_gpt_concurrently_individually(client, call_func, model_name, temp, topp,
                                                 [self.agent_system_prompts, self.agent_user_prompts],
                                                 )
        # print(results[0])


        return results




if __name__ == "__main__":

    prompts_file_name = ["router_system_prompt","router_user_prompt"]
    queries= ["拜登退选是真的吗","你好"]
    replace_strs = ["user_input"]
    llm_agent = Agent(queries,prompts_file_name,replace_strs)
    results = llm_agent.call_apis("qwen","intent",0.7)
    print(results)

