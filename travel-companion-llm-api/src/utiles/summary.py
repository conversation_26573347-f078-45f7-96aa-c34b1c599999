import time
import os
import yaml
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler

class GPTAnswer:
    TOP_K = 3  # Top K documents to retrieve

    def __init__(self):
        # Load configuration from a YAML file
        current_dir = os.path.dirname(__file__)
        config_path = os.path.join(current_dir, '../config/config.yaml')
        config_path = os.path.abspath(config_path)

        with open(config_path, 'r') as file:
            self.config = yaml.safe_load(file)
        self.model_name = self.config["model_name"]
        self.api_key = self.config["openai_api_key"]
        self.api_url = self.config["openai_base_url"]

    def _format_reference(self, relevant_docs_str):
        # Parse the relevant_docs_str and format it
        relevant_docs_list = relevant_docs_str.strip().split("\n")
        reference_list = []

        for doc in relevant_docs_list[:self.TOP_K]:
            index, content = doc.split(":", 1)
            reference_list.append(f"Webpage[{index}]:\n{content.strip()}\n\n\n")

        formatted_reference = "\n".join(reference_list)
        return formatted_reference

    def _format_reference(self, relevant_docs_dict):
        # Parse the relevant_docs_dict and format it
        formatted_reference = "\n"
        
        for category, docs in relevant_docs_dict.items():
            formatted_reference += f"Category: {category}\n"
            for i, doc in enumerate(docs[:self.TOP_K], 1):
                content = doc["content"]
                url = doc["url"]
                formatted_reference += f"Webpage[{i}], url: {url}:\n{content}\n\n\n"
                
        return formatted_reference

    def get_answer(self, query, relevant_docs, output_format, profile, search=True):
        # Create an instance of ChatOpenAI and generate an answer
        llm = ChatOpenAI(model=self.model_name, base_url=self.api_url, api_key=self.api_key, temperature=0.0,
                         streaming=True, callbacks=[StreamingStdOutCallbackHandler()])
        if search:
            template = self.config["template"]
            prompt_template = PromptTemplate(
                input_variables=["profile", "context_str", "query", "format"],
                template=template)
        else:
            prompt_template = query

        profile = "conscientious researcher" if not profile else profile
        #relevant_docs = relevant_docs.replace("\n","")
        summary_prompt = prompt_template.format(context_str=relevant_docs, query=query, format=output_format, profile=profile)
        print("\n\nThe message sent to LLM:\n", summary_prompt)
        print("\n\n", "="*30, "GPT's Answer: ", "="*30, "\n")

        # import pdb;pdb.set_trace()

        gpt_answer = llm.invoke(summary_prompt)
        # gpt_answer = llm([HumanMessage(content=summary_prompt)])

        return gpt_answer

# Example usage
if __name__ == "__main__":

    web_contents = {
    "奥运 消息": [
        {
            "content": "这是奥运消息的第一篇文章内容。",
            "url": "http://example.com/olympic1"
        },
        {
            "content": "这是奥运消息的第二篇文章内容。",
            "url": "http://example.com/olympic2"
        },
        {
            "content": "这是奥运消息的第三篇文章内容。",
            "url": "http://example.com/olympic3"
        }
    ],
    "奥运 开幕时间": [
        {
            "content": "这是开幕时间的第一篇文章内容。",
            "url": "http://example.com/tech1"
        },
        {
            "content": "这是开幕时间的第二篇文章内容。",
            "url": "http://example.com/tech2"
        },
        {
            "content": "这是开幕时间的第三篇文章内容。",
            "url": "http://example.com/tech3"
        }
    ]
}

    content_processor = GPTAnswer()
    query = "What are the latest Olympic news and the time of the Olympic opening ceremony?"

    output_format = "" # User can specify output format
    profile = "" # User can define the role for LLM
    start = time.time()
    ai_message_obj = content_processor.get_answer(query, web_contents, output_format, profile)
    answer = ai_message_obj.content + '\n'
    end = time.time()

    print("\n\nGPT Answer:\n", answer)
    print("\nGPT Answer time:", end - start, "s")

