import requests
from typing_extensions import Optional
from configuration import config

from src.utiles.constants import *

url=config["user_memory"]["url"]

def _request_habit_from_user_memory_raw(body: dict) -> Optional[dict]:
    header={'Content-Type': 'application/json'}
    try:
        with requests.post(url=url, headers=header, json=body) as response:
            if response.status_code == 200:
                return response.json()
            else:
                # HTTP状态码非200，返回网络错误
                return {"code": error_code_mem_http_error, "response_str": "调用记忆体接口网络错误"}
    except requests.RequestException as e:
        # 捕获requests相关的网络异常，返回网络错误
        print(e)
        return {"code": error_code_mem_http_error, "response_str": "调用记忆体接口网络错误"}
    
# realtion_filter 表示需要筛选的关系类型，如 LIKES 、 INTRERSTED_IN 等
# async def request_habit_from_user_memory_raw(body: dict, relation_filter: Optional[str] = None) -> Optional[list]:
#     all_info = _request_habit_from_user_memory_raw(body=body)
#     if not all_info:
#         return None
#     status = all_info.get("status", {})
#     if "200" != status.get("code", ""):
#         return None
#     data = all_info.get("data", {})
#     data_list = data.get("dataList", [])
#     if not relation_filter:
#         return data_list
#     else:
#         filtered_data_list = [temp for temp in data_list if isinstance(temp, str) and relation_filter in temp]
#         return filtered_data_list



# realtion_filter 表示需要筛选的关系类型，如 LIKES 、 INTRERSTED_IN 等
async def request_habit_from_user_memory_text(body: dict,  relation_filter: str) -> Optional[str]:
    all_info = _request_habit_from_user_memory_raw(body=body)
    print(all_info)
    # 1. 检查并透传底层函数返回的网络错误
    if all_info and isinstance(all_info, dict) and all_info.get("code") == error_code_mem_http_error:
        return all_info

    # 2. 检查记忆体服务返回的业务状态码或数据格式，如果不正确，则返回内部错误
    if not all_info or "200" != all_info.get("status", {}).get("code", ""):
        return {"code": error_code_mem_return_error, "response_str": "调用记忆体接口内部错误"}
    data = all_info.get("data", {})
    data_list = data.get("dataList", [])
    # relation_filter 不再使用，返回完整的用户画像
    filtered_data_list = data_list
    # filtered_data_list = [temp for temp in data_list if isinstance(temp, str) and relation_filter in temp]
    if 0 >= len(filtered_data_list):
        return None
    else:
        try:
            print(filtered_data_list)
            temp_care = "User "
            cares = ", ".join(filtered_data_list)
            print(cares)
            return temp_care + cares + "."
        except BaseException as e:
            print(e)
            return None
        