import glob
import os
import time


def load_prompts(folder_path):
    file_names = glob.glob(os.path.join(folder_path, '*.txt'))
    file_contents = {}

    for file_name in file_names:
        with open(file_name, 'r', encoding='utf-8') as file:
            file_contents[os.path.basename(file_name).split(".")[0]] = file.read()
    return file_contents


def format_agent_topk(nested_dict):
    result = []
    for key in nested_dict:
        formatted_str = []
        for sub_key, sub_value in nested_dict[key].items():
            title = sub_value.get('title', '')
            snippet = sub_value.get('snippet', '')
            formatted_str.append(f'{sub_key}: {{title: {title}, snippet: {snippet}}}')
        result.append('\n'.join(formatted_str))
    return result

def format_reranker_topk(data):
    result = []
    for key, value in data.items():
        titles_snippets = []
        for v in value.values():
            title = v['title']
            snippet = v['snippet']
            titles_snippets.append(f"'title': '{title}', 'snippet': '{snippet}'")
        result.append(titles_snippets)
    return result


def get_idx_link(query_with_link,query_topk_indx):
    """
    query_with_link: format
    {
        '今日 奥运会 消息': 
        {
        1: {'title': '巴黎奥运会羽毛球比赛今日开赛！   - Paris 2024 Olympic Games',
        'snippet': '巴黎奥运会羽毛球比赛在拉夏贝尔门体育馆正式拉开帷幕。顶尖选手们齐聚一堂，精彩对决一触即发，让我们共同见证羽毛球场上的激情与荣耀！',
        'link': 'https://olympics.com/zh/paris-2024/live-updates/f66209be-4d16-4b6f-87ff-1ab46f90aac4'},
        2: {'title': '冲击首金！巴黎奥运会今日比赛看点→',
        'snippet': '今天是巴黎奥运会开幕后的首个比赛日，将决出14枚金牌。首金花落谁家，大家也十分期待。 本届奥运会的首金，有可能在射击混合团体10米气步枪和跳水女子 ...',
        'link': 'https://content-static.cctvnews.cctv.com/snow-book/index.html?item_id=9712519362118668996'},
        3: {'title': '巴黎2024实时博客- 奥运会实时动态和劲爆新闻',
        'snippet': '在今日晚些将举办的开幕式环节中，也会有大家都很熟悉的运动员入场式。你对所有参赛的国家和地区奥委会知多少？我们为你整理了一份列表。',
        'link': 'https://olympics.com/zh/paris-2024/live-updates/9611097c-0b15-4b18-b5c4-749442c56006'}
        }
        '今日 xxxx 消息': 
        {
            1: {'title': 'xxxxxx',
        'snippet': 'xxxxxxxxx',
        'link': 'xxxx'},
        }
        
    }
    query_topk_indx: {'今日 奥运 消息': ['1', '2']}
    return:
        {
            '今日 奥运消息': ['https://www.163.com/dy/article/J88STU360516BU8U.html','https://olympics.com/zh/paris-2024/live-updates/bbc4a1b6-d802-46ba-9efd-e6a22f68d0c9']
            '今日 xxxx 消息':['xxxx']
        }
    """
    query_links_to_open = {}
    for q,values in query_topk_indx.items():
        temp_links = []
        for v in values:
            temp_links.append(query_with_link[q][int(v)]["link"])
        query_links_to_open[q] = temp_links
    return query_links_to_open