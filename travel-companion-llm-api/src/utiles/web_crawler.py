import requests
import re
from bs4 import BeautifulSoup


class WebCrawler:
    def __init__(self, user_agent='macOS'):
        self.headers = self._get_headers(user_agent)

    def _get_headers(self, user_agent):
        if user_agent == 'macOS':
            return {
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
                'sec-ch-ua': '"Not/A)Brand";v="99", "Google Chrome";v="115", "Chromium";v="115"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
            }
        else:
            return {
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
                'sec-ch-ua': '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'Referer': 'https://www.google.com/',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'}

    def get_webpage_html(self, url):
        # Fetch the HTML content of a webpage from a given URL
        response = requests.Response()  # Create an empty Response object
        if url.endswith(".pdf"):
            # Skip PDF files which are time consuming
            return response
        try:
            response = requests.get(url, headers=self.headers)
            if response.status_code == 200:
                response.encoding = response.apparent_encoding
                # print(response)
        except requests.exceptions.Timeout:
            # Add timeout exception handling here
            return "Timeout"
        return response

    def convert_html_to_soup(self, html):
        # Convert the HTML string to a BeautifulSoup object for parsing

        return BeautifulSoup(html.text, 'html.parser')

    def extract_main_content(self, html_soup):
        main_content = []
        content_tags = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'div', 'article', 'section']
        for tag in html_soup.find_all(content_tags):
            tag_text = tag.get_text().strip()
            if tag_text and len(tag_text.split()) > 15:
                main_content.append(tag_text)
        return "\n".join(main_content).strip()

    def clean_text(self, text):
        text = re.sub(r'\s+', ' ', text).strip()
        short_word_pattern = r'\b\w{1,14}\b'
        text = re.sub(fr'(\b{short_word_pattern}(?:\s+{short_word_pattern})+\b)', '', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text

    def scrape_url(self, url):
        webpage_html = self.get_webpage_html(url)
        soup = self.convert_html_to_soup(webpage_html)
        main_content = self.extract_main_content(soup)
        cleaned_text = self.clean_text(main_content)
        return cleaned_text


if __name__ == "__main__":
    crawler = WebCrawler(user_agent='windows')
    test_url = "https://sputniknews.cn/person_telangpu/"
    main_content = crawler.scrape_url(test_url)
    print(main_content)
