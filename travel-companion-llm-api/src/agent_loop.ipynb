{"cells": [{"cell_type": "code", "source": ["prompt_tpl = '''\n", "You are ChatGPT, a large language model trained by OpenAI, based on the GPT-4 architecture.  \n", "Knowledge cutoff: 2023-10  \n", "Current date: 2024-07-28\n", "respond language: Chinese\n", "\n", "# Tools\n", "\n", "## browser\n", "\n", "You have the tool `browser`. Use `browser` in the following circumstances:\n", "- User is asking about current events or something that requires real-time information (weather, sports scores, etc.)\n", "- User is asking about some term you are totally unfamiliar with (it might be new)\n", "- User explicitly asks you to browse or provide links to reference\n", "\n", "Given a query that requires retrieval, your turn will consist of steps:\n", "1. Call the `web_browser(str)` function to get a list of results. `search(str)` Issues a query to a search engine and displays the results.\n", "2. Call the `mclick(ids: list[str])` function to retrieve a diverse and high-quality subset of these results (in parallel). Remember to SELECT AT LEAST 3 sources when using `mclick`. `mclick(ids: list[str])`. Retrieves the contents of the webpages with provided IDs (indices).Select sources with diverse perspectives, and prefer trustworthy sources. Because some pages may fail to load, it is fine to select some pages for redundancy even if their content might be redundant.\n", "3. Write a response to the user based on these results. In your response, cite sources using the citation format below.\n", "\n", "\n", "In some cases, you should repeat step 1 twice, if the initial results are unsatisfactory, and you believe that you can refine the query to get better results.\n", "\n", "For citing quotes from the 'browser' tool: please render in this format: [message idx].\n", "\n", "\"\"Begin!\n", "\n", "# ALWAYS use following format:\n", "Question: 用户输入的query问题\n", "Thought: 你应该思考如何解决问题的过程,是否需要分解问题\n", "Action: 执行的动作，应该是Tool name 'browser' 的其中一个，如果没有action，那么意味着你可以直接给出Final answer\n", "Action Input: action 的输入，'web_browser(str)', 'mclick(list[str])' 的其中一个，这取决于你执行到哪一步了, 永远只保证list中只有一个action input\n", "Observation: action执行的结果\n", "... (this Thought/Action/Action Input/Observation can be repeated N times, therefore you can to decompose questions to sub-parts if the query is too complex, until you got answer)\n", "Thought: 根据上面循环的推理，我知道了最终答案！\n", "Final Answer: 原始的输入的query的final answer\n", "\n", "Past chat:\n", "{past_chat}\n", "\n", "Current query: \n", "{user_query}\n", "\n", "Past loop:\n", "{past_loop}\n", "'''\n", "\n", "prompt_tpl = '''\n", "You are ChatGPT, a large language model trained by OpenAI, based on the GPT-4 architecture.  \n", "Knowledge cutoff: 2023-10  \n", "Current date: 2024-07-28\n", "respond language: Chinese\n", "\n", "# Tools\n", "\n", "## browser\n", "\n", "You have the tool `browser`. Use `browser` in the following circumstances:\n", "- User is asking about current events or something that requires real-time information (weather, sports scores, etc.)\n", "- User is asking about some term you are totally unfamiliar with (it might be new)\n", "- User explicitly asks you to browse or provide links to reference\n", "\n", "Given a query that requires retrieval, your turn will consist of steps:\n", "1. Call the `web_browser(str)` function to get a list of results. `web_browser(str)` Issues a query to a search engine and displays the results.\n", "2. Write a comprehensive response to the user based on search results, not just the plain answer. Format should be like IEEE reference citation. please render in this format: [message idx].\n", "\n", "example of response:\n", "answer1[1] means answer1 is cited from idx 1, answer2[2] means answer1 is cited from idx 2, answer3[3][4] means answer3 is cited both from idx 3 and 4.\n", "you can rerank the reference order to make the response fluent.\n", "\n", "\"\"Begin!\n", "\n", "## Output Format\n", "To answer the question, please use the following format.\n", "\n", "Thought: I need to use a tool to help me answer the question.\n", "Action: tool name (one of 'browser') if using a tool.\n", "Action_Input: the input to the tool (one of web_browser(\"rewrite query\")),  e.g. web_browser(\"川普 最新消息\")\n", "\n", "Please ALWAYS start with a Thought.\n", "\n", "If this format is used, the user will respond Observation in the following format:\n", "\n", "Observation: tool response\n", "\n", "You should keep repeating the above format until you have enough information to answer the query without using any more tools, you need rethink the reason why fail and you believe you can refine your method to get a good result. At that point, you MUST respond in following formats:\n", "\n", "Thought: I can answer without using any more tools.\n", "Final_Answer: your answer here\n", "\n", "Past chat:\n", "{past_chat}\n", "\n", "Current query: \n", "{user_query}\n", "\n", "Past loop:\n", "{past_loop}\n", "'''\n", "import os\n", "import json\n", "from langchain_community.tools.tavily_search import TavilySearchResults\n", "import datetime\n", "import os\n", "import re\n", "from openai import OpenAI\n", "client = OpenAI(\n", "    api_key=\"sk-62cf36a0acfb45d8aba4333afed64f73\",\n", "    base_url=\"https://api.deepseek.com\"\n", "\n", ")\n", "\n", "\n", "def llm(query,history=[],user_stop_words=[]):    # 调用api_server\n", "    try:\n", "        messages=[{'role':'system','content':'You are a helpful assistant.'}]\n", "        for hist in history:\n", "            messages.append({'role':'user','content':hist[0]})\n", "            messages.append({'role':'assistant','content':hist[1]})\n", "        messages.append({'role':'user','content':query})\n", "        completion = client.chat.completions.create(model=\"deepseek-chat\", temperature=0.1, top_p=0.7\n", "                                                    , messages=messages,stop=user_stop_words)\n", "        return completion.choices[0].message.content\n", "    except Exception as e:\n", "        return str(e)\n", "\n", "\n", "\n", "def web_browser(query):\n", "    print(f\"Search web_browser with query: {query}\")\n", "    os.environ['TAVILY_API_KEY']='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'\n", "    retriever = TavilySearchResults(max_results=10)\n", "    \n", "    search_res = retriever.invoke(query)\n", "    #print(search_res)\n", "    try:\n", "        output_str = \"\"\n", "        for i, item in enumerate(search_res, 1):\n", "            content = item['content']\n", "            output_str += f\"{i}: {{'content': '{content}'}}\\n\"\n", "    except Exception as e:\n", "        output_str = search_res\n", "    return  output_str\n", "\n", "\n", "def match_call_str_tools(action_input):\n", "\n", "    match = re.match(r\"(\\w+)\\(\\\"([^\\\"]*)\\\"\\)\", action_input.strip(\"`\"))\n", "    if match:\n", "        func_name = match.group(1)\n", "        query = match.group(2)\n", "        if func_name in [\"web_browser\"]:\n", "            result = web_browser(query)\n", "        else:\n", "            result = f\"Function {func_name} is not defined.\"\n", "    else:\n", "        result = \"Invalid input string.\"\n", "    return result\n", "\n", "\n", "\n", "def extract_info(s):\n", "    thought_pattern = r'Thought:\\s*(.*)'\n", "    action_pattern = r'Action:\\s*(.*)'\n", "    action_input_pattern = r'Action_Input:\\s*(.*)'\n", "    final_answer_pattern = r'Final_Answer:\\s*(.*)'\n", "    \n", "    thought_match = re.search(thought_pattern, s)\n", "    action_match = re.search(action_pattern, s)\n", "    action_input_match = re.search(action_input_pattern, s)\n", "    final_answer_match = re.search(final_answer_pattern, s)\n", "    \n", "    thought = thought_match.group(1).strip() if thought_match else None\n", "    action = action_match.group(1).strip() if action_match else None\n", "    action_input = action_input_match.group(1).strip() if action_input_match else None\n", "    final_answer = final_answer_match.group(1).strip() if final_answer_match else None\n", "    \n", "    return thought, action, action_input, final_answer"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-07-29T00:44:52.588212Z", "start_time": "2024-07-29T00:44:52.119471Z"}}, "id": "da692713cc59f75e", "outputs": [], "execution_count": 2}, {"cell_type": "code", "source": "", "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-07-29T00:44:53.351571Z", "start_time": "2024-07-29T00:44:53.332582Z"}}, "id": "f0bf1bbc82fbe440", "outputs": [], "execution_count": 2}, {"cell_type": "code", "source": ["past_chat,past_loop = \"\",\"\"\n", "query=input(\"What do you want ask to my beast LLM?\")\n", "print(\"user query is: \",query)\n", "for i in range(5):\n", "    prompt=prompt_tpl.format(past_chat=past_chat,user_query=query,past_loop=past_loop)\n", "    response=llm(prompt,user_stop_words=[\"Observation\"])\n", "    thought, action, action_input, final_answer = extract_info(response)\n", "    print(response)\n", "    if not final_answer:\n", "        search_res = match_call_str_tools(action_input)\n", "        observation = \"Observation:\\n\"+ \"search_result\\n\"+search_res\n", "        past_loop += response+observation\n", "    else:\n", "        break"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-07-29T01:22:21.148922Z", "start_time": "2024-07-29T01:21:46.055597Z"}}, "id": "d4cef70b789094a", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["user query is:  商汤科技是什么时候上市的？当时股票多少钱？现状怎么样了\n", "Thought: I need to use a tool to help me answer the question about the listing details and current status of SenseTime.\n", "Action: browser\n", "Action_Input: web_browser(\"商汤科技 上市时间 股票价格 现状\")\n", "Search web_browser with query: 商汤科技 上市时间 股票价格 现状\n", "Thought: I can answer without using any more tools.\n", "Final_Answer: 商汤科技于2021年12月30日正式在香港交易所上市。上市首日的开盘价为3.91港元，较发行价3.85港元上涨约1.56%。截至收盘，商汤科技股价上涨7.27%至4.13港元，市值达到1374.56亿港元[1][2][3][4][5][6][7][8][9][10]。目前，商汤科技的股价和市值表现良好，显示出市场对其AI技术的认可和期待。\n"]}], "execution_count": 13}, {"cell_type": "code", "source": "print(search_res)", "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-07-29T01:23:13.421582Z", "start_time": "2024-07-29T01:23:13.406564Z"}}, "id": "fa80a0845a3a0895", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1: {'content': '12月30日，商汤科技正式登陆港交所。开盘后，商汤股价直线拉升，其后有所回落，截至发稿，商汤股价4.37港元，涨13.51%，总市值1454亿港元。 上市 ...'}\n", "2: {'content': '12月30日上午9点30分，商汤集团股份有限公司（以下简称\"商汤科技\"；股份代码：0020.hk）正式登录香港联合交易所。首日股价最高涨幅超过20%，市值超过1500亿港元。1月3日，商汤迎来上市之后的第三个交易日，股价延续前两个交易日的涨势。'}\n", "3: {'content': '截至收盘，商汤科技股价上涨7.27%至4.13港元，市值达1374.56亿港元。. 在经历波折后，商汤登陆港股为等待上市的AI企业们带来了积极信号。. 估值涨近 ...'}\n", "4: {'content': '查看最新行情. 新浪科技讯 北京时间12月30日上午消息，AI独角兽商汤科技今日在香港上市，上市首日开盘价3.91港元每股，较发行价3.85港元上涨约1.56% ...'}\n", "5: {'content': '商汤科技上市首日：股价冲高回落，收涨7.27%，市值超1300亿. \"AI四小龙\"，谁能率先登陆资本市场备受关注. 12月30日，商汤（00020.HK）在港交所挂牌 ...'}\n", "6: {'content': '历经二次招股，商汤科技成功挂牌 市值逾1400亿港元. \"AI第一股\"，花落商汤。. 21世纪经济报道记者白杨 北京报道. 12月30日，商汤科技正式在港交所挂牌上市，开盘价3. 91 港元较发行价3. 85 港元上涨约1.56%，总市值约1301亿港元。. 随后，商汤科技的股价一路上涨 ...'}\n", "7: {'content': '一旦此次IPO顺利，商汤科技可能创下全球人工智能领域规模最大IPO。. 今天，12月30号，商汤科技终于在港股上市了。. 据悉，商汤科技最终发售价定 ...'}\n", "8: {'content': '商汤科技IPO：明日起公开发售15亿股，募资约56亿港元. 12月6日，商汤集团股份有限公司（0020.HK）公布其拟于香港联合交易所有限公司主板上市，明日起公开发售15亿股，中间价3.92港元，将募资约56亿港元。. 商汤集团此次全球发售合共1,500,000,000股B类股份，其中90% ...'}\n", "9: {'content': '商汤科技的上市之路一波三折。公司原计划于12月17日挂牌上市，但12月10日，美国财政部称商汤加入\"中国军工复合体企业\"清单，商汤科技发布声明反对对这一决定与相关指控；12月13日，商汤科技发表声明称决定延迟上市；12月20日，商汤科技宣布重新启动招股。'}\n", "10: {'content': '商汤集团终于上市了!. 智东西12月30日报道，今日，商汤集团（股份代号：0020.HK）以3.95港元的发行价正式登陆香港联合交易所。. 商汤集团上市首日 ...'}\n", "\n"]}], "execution_count": 14}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "24fd984b6d672fe9"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}