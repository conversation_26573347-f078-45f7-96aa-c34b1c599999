from utiles.utiles import *
from stage0_agent_intent import call_intent_agent
from stage2_agent_topk import call_topk_agent
from stage1_web_search import call_web_search
from stage2_reranker_topk import call_reranker_topk
from stage3_web_crawling import WebContentFetcher
from stage4_llm_summary import call_llm_summary
# from utiles.summary import GPTAnswer
from utiles.web_crawler import WebCrawler
import json


from stage_offline_search import call_offline_search



# current_dir = os.path.dirname(__file__)
# prompts_path = os.path.join(current_dir,'','prompts')

if __name__ == "__main__":
    prompts_dic = load_prompts("src/prompts")
    output_format = "" # User can specify output format
    profile = "" # User can define the role for LLM
    reranker = True
    # model_name = "gpt-4o-mini"
    # model_name = "gpt-4o"
    model_name = "qwen72b"
    
    k = "3"


    queries = ''

    while True:
        query = input("用户: ")
        # query='我在深圳南山，找个有卖喜茶的能看电影的购物中心'
        # query='我在东方明珠，想去麦当劳东川路店'
        # query='找个有卖喜茶的能看电影的购物中心'
        
        queries= queries+ "###用户: "+query

        engine=''
        # engine = input(f"Input search engine(e.g. google, bing, baidu for SerpApi(100 free limit), default enter is Serper google): ")


        phrase_result = call_intent_agent([queries], model_name)
        print('phrase_result ', phrase_result)
        # action_input = phrase_result[queries[0]]
        action_ = phrase_result["Action"]
        action_input = phrase_result["Action_input"]

        import pdb;pdb.set_trace()


        # if len(action_input) >0:
        if 'offline_search' in action_:

            # result = ''.join(word.strip() for word in action_input)
            # phrase_result = call_offline_search([result], model_name)
            print('queries ', queries)

            phrase_result = call_offline_search([queries], model_name)

            print(phrase_result[0])

            queries = ''

            continue

            # break


            # print("This is a real time question")
            # # import pdb;pdb.set_trace()
            # query_with_link = call_web_search(action_input, engine)
            # print(query_with_link)
            # with open("search_result.json", 'w', encoding="utf-8") as json_file:
            #     json.dump(query_with_link, json_file, indent=4, ensure_ascii=False)

            
            # # import pdb;pdb.set_trace()
            # agent_topk = call_topk_agent(action_input, model_name, query_with_link, k=k)
            # print("#" * 15,"agent topk result","#" * 15)
            # print(agent_topk)

            # # import pdb;pdb.set_trace()
            # query_links_to_open = get_idx_link(query_with_link, agent_topk)
            # # import pdb;pdb.set_trace()

            # crawler = WebCrawler(user_agent='windows')
            # web_fetch = WebContentFetcher(crawler)
            # res_for_llm = web_fetch.update_dic_with_content(query_links_to_open)
            # # import pdb;pdb.set_trace()

            # print(res_for_llm)
            # llm_response = call_llm_summary(query, res_for_llm, model_name, output_format, profile)
            # print("\n\nGPT Answer:\n", llm_response)



            # import pdb;pdb.set_trace()
             

            # print("#" * 15,"reranker topk result","#" * 15)
            # reranker_topk = call_reranker_topk(action_input, query_with_link,topk=int(k))
            # print(reranker_topk)
        else:
            # print("This is NOT a real-time question")

            print('LLM:', action_input[0])
            # queries.append('LLM:'+ action_input[0])
            queries= queries+ '###LLM:'+action_input[0]



