import json
import time

def city_search(keyword:str):
    with open('src/v8_010//city_list.json', 'r', encoding='utf-8') as file:
        city_list = json.load(file)
    city_name_end_list = ["省","市","自治州","自治州直辖","区","自治区","盟","县","自治县","旗","左旗","中旗","右旗","自治旗","镇","地区","回族区"]
    candidate_city_list = []
    city_list.append({"name":"中国","adcode":"100000","citycode":"","city":"","province":""})
    for item in city_list:
        city_name = item["name"]
        if keyword in city_name:
            if keyword == city_name:
                return city_name
            else:
                city_name_res = city_name.replace(keyword,"")
                for index in range(len(city_name_end_list)):
                    city_name_end = city_name_end_list[index]
                    if city_name_res == city_name_end:
                        city_item = {
                            "name": city_name,
                            "level": index,
                        }
                        candidate_city_list.append(city_item)
                        break
    if len(candidate_city_list) == 0:
        return ""
    else:
        sorted_city_list = sorted(candidate_city_list, key=lambda x: x["level"])
        print(sorted_city_list)
        return sorted_city_list[0]["name"]


if __name__ == "__main__":
    # keyword = "西安"
    # keyword = "安宁"
    keyword = "朝阳"
    # keyword = "大同"
    # keyword = "山东"

    time_start = time.time()
    print(city_search(keyword))
    time_end = time.time()
    print(time_end - time_start)