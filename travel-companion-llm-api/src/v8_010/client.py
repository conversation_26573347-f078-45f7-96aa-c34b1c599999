import time

import httpx
import loguru
import sensenova
from openai import OpenAI
from loguru import logger
from configuration import config

def client_openai(messages: list, model_name="qwen", is_stream=False, temperature=0):
    model_exist = True
    config_key = None
    if model_name == 'SenseAutoChat-30B':
        config_key = "SenseAutoChat-30B"
    elif model_name == 'POI-rewrite-7B':
        config_key = "POI-rewrite-7B"
    elif "qwen" in model_name.lower():
        config_key = "qwen"
    elif "senseauto" in model_name.lower():
        config_key = "senseauto"
    elif "mapping" in model_name.lower():
        config_key = "mapping"

    if config_key and config_key in config["llm_models"]:
        model_config = config["llm_models"][config_key]
        openai_api_key = model_config["api_key"]
        openai_api_base = model_config["base_url"]
        model_name_to_use = model_config["model_name"]
    else:
        loguru.logger.info("没有相关的模型名字")
        model_exist = False

    if model_exist:
        client = OpenAI(
            api_key=openai_api_key,
            base_url=openai_api_base,
            http_client=httpx.Client(verify=False)
        )
        # print('------------------------begin')
        # print(messages)
        # print('-------------------------end')
        response = client.chat.completions.create(
            model=model_name_to_use,
            messages=messages,
            logprobs=True,
            stream=is_stream,
            top_p=0.8,
            temperature=0.6,
            stream_options={"include_usage": True} if is_stream else None,
            # temperature=temperature
        )
        if is_stream:
            for chunk in response:
                # ['data']['choices'][0]['delta']
                # yield chunk

                chunk_dict = {}
                chunk_dict['data'] = {}
                chunk_dict['data']['choices'] = []
                chunk_delta = {}
                if chunk.choices and chunk.choices[0].delta.content is not None:
                    chunk_str = chunk.choices[0].delta.content
                    if isinstance(chunk_str, str):


                        chunk_delta['delta'] = chunk_str
                        chunk_dict['data']['choices'].append(chunk_delta)

                        yield chunk_dict
                elif chunk.usage is not None:
                     chunk_dict['data']['usage'] = dict(chunk.usage)
                     chunk_delta['delta'] = ""
                     chunk_dict['data']['choices'].append(chunk_delta)

                     yield chunk_dict




        else:
            content = response.choices[0].message.content
            yield content
    else:
        yield None


def client_sensenova(messages: list, model_name="senseauto", is_ori_trunk=False, is_stream=False, temperature=0.001,
                     nova_ak: str = "", nova_sk: str = ""):
    if "senseauto" in model_name.lower():
        model_name = "SenseAuto-Chat"
    #model_name = "SenseAuto-Chat"
    # sensenova.access_key_id = "2V3uatcedTJPhW9srmCt5TsCfmp"
    # sensenova.secret_access_key = "XzoVqkGWjVONZx5G8jWfVPluXuRkuGlI"

    #sensenova.api_base='https://api.sensenova.cn/v1'
    # sensenova.api_base='https://api.sensenova.cn/v1/llm/chat-completions'
    # sensenova.access_key_id = "2VNRG84V6WdPX3hjiH70lJtXYYf"
    # sensenova.secret_access_key = "UGUMIdOHCy8zsIAmQsWQBnYPmhBibb0i"

    sensenova.access_key_id = nova_ak
    sensenova.secret_access_key = nova_sk

    response = sensenova.ChatCompletion.create(
        model=model_name,
        max_new_tokens=1024,
        messages=messages,
        # repetition_penalty=1.05,
        top_p=0.3,
        temperature=0.001,
        # temperature=temperature
        # top_k=3, #3或5
        stream=is_stream,
    )

    if is_stream:
        for chunk in response:
            # 输出原字符串
            if is_ori_trunk == True:
                yield chunk
            else:
                chunk_str = chunk['data']['choices'][0]['delta']
                if isinstance(chunk_str, str):
                    yield chunk_str
    else:
        content = response['data']['choices'][0]['message']
        yield content


def client(messages: list, id: str, model_name="qwen", is_stream=False, is_ori_trunk=False, temperature=0.001,
           nova_ak: str = "", nova_sk: str = ""):
    # history_dir = "client_log"
    # os.makedirs(history_dir, exist_ok=True)
    # max_number = 0
    # for filename in os.listdir(history_dir):
    #     filename = filename.replace("_in","")
    #     filename = filename.replace("_out", "")
    #     match = re.fullmatch(r'^\d+\.txt$', filename)
    #     if match:
    #         # 将匹配到的数字转换为整数
    #         number = int(match.group().replace(".txt", ""))
    #         # 更新最大编号
    #         if number > max_number:
    #             max_number = number
    # max_number += 1
    # save_path = history_dir + "\\" + f"{id}_{get_timestamp()}"

    if model_name == 'SenseAutoChat-30B':
        client_type = "openai"
    elif model_name == 'POI-rewrite-7B':
        client_type = "openai"
    elif "sense" in model_name.lower() or "mapping" in model_name.lower():
        model_name = "senseauto"
        client_type = "sensenova"
        #client_type = "openai"
    elif "qwen" in model_name.lower() or "gpt" in model_name.lower():
        client_type = "openai"
    else:
        client_type = ""
    loguru.logger.info(f"client model_name:{model_name} client_type:{client_type}")
    # save_content = ""
    # client_type = "sensenova"
    if is_stream:
        logger.info("llm start")
    start_time = time.perf_counter()
    llm_first_time = 0
    is_first_token = True
    if client_type == "sensenova":
        response = client_sensenova(messages, model_name, is_ori_trunk=is_ori_trunk, is_stream=is_stream,
                                    temperature=temperature, nova_ak=nova_ak, nova_sk=nova_sk)
        for chunk in response:
            # save_content += chunk
            yield chunk

    elif client_type == "openai":
        response = client_openai(messages, model_name, is_stream, temperature)
        for chunk in response:
            if is_stream:
                if is_first_token:
                    is_first_token = False
                    llm_first_time = round(time.perf_counter() - start_time, 4)
            # save_content += chunk
            yield chunk
        if is_stream:
            logger.info(
                f"llm end----cost time:{llm_first_time}-----total time:{round(time.perf_counter() - start_time, 4)}")
    else:
        yield "无匹配模型名称"

    # with open(save_path + "_in.txt", 'w', encoding='utf-8') as file:
    #     json.dump(messages,file,ensure_ascii=False,indent=4)
    # with open(save_path + "_out.txt", 'w', encoding='utf-8') as file:
    #     file.write(save_content)


if __name__ == "__main__":
    system_prompt = "你是一个助手"
    history = "请讲一个笑话"

    # with open("D:\data_for_work\\baoma_POI\\travelcompanion-poi-v8_display\client_log\\74_in.txt","r",encoding="utf-8") as file:
    #     messages = json.load(file)

    messages = [
        {"role": "system", "content": "你是一个助手"},
        {"role": "user", "content": "你知道去哪里买盲盒吗"},
    ]

    # response = client(messages,model_name="qwen",is_stream=False)
    # content = response.choices[0].message.content

    # content = client(messages, model_name="senseauto", is_stream=False)
    # print(content)

    response = client(messages, id="", model_name="SenseAutoChat-30B", is_stream=False)
    for chunk_str in response:
        # chunk_str = chunk['data']['choices'][0]['delta']
        print(chunk_str, end="")

    # response = client(messages,model_name="gpt4o", is_stream=True)
    #
    # for chunk in response:
    #     chunk_str = chunk.choices[0].delta.content
    #     print(chunk_str)
