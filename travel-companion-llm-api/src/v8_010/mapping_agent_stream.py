from .client import client
from pydantic import BaseModel
import json
import time
from .history_management import History
from typing import Generator
from .city_search import city_search
from loguru import logger

from src.utiles.constants import *

class MappingInput(BaseModel):
    # model_name: str = "qwen72b"
    model_name: str = "mapping"
    history: str
    id: str = "-1"
    poi_search:str
    latitude: str = "40.081839"
    longitude:str = "116.586733"
    is_stream: bool = False
    nova_ak: str = ""
    nova_sk: str = ""
    city: str = ""
    user_like: str = ""
    location_infos: dict={}
    

class MappingOutput(BaseModel):
    results: str
    success: bool

def mapping_agent_call(item: MappingInput) -> Generator:
    model_name = item.model_name
    history = item.history
    id = item.id
    poi_search = item.poi_search

    system_prompt_path = "src/v8_010/prompts/mapping_system_prompt.txt"
    with open(system_prompt_path, 'r', encoding='utf-8') as file:
        system_prompt = file.read()
    usr_input = {
        "query": poi_search,
        "profileText": item.user_like
    }
    usr_query = json.dumps(usr_input, ensure_ascii=False)
    usr_msg = history + f"输入: {usr_query}\n"
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": usr_msg}
    ]
    # logger.info(f"model_name:{model_name}")
    response = client(messages, id, model_name=model_name, is_stream=item.is_stream, nova_ak=item.nova_ak, nova_sk=item.nova_sk)
    return response

def mapping_agent_process(item: MappingInput,response: Generator) -> MappingOutput:
    content = ""
    for chunk in response:
        content += chunk
    logger.info(f"map_model_output:{content}")
    # response = response.choices[0].message.content
    # print(response)
    results = content.split("```json")[-1].split("```")[0]
    poi_json = json.loads(results)
    if not poi_json["intent"]:
        results = json.dumps(poi_json, ensure_ascii=False)
        return MappingOutput(results=results, success=True)
    poi_json["latitude"] = item.latitude
    poi_json["longitude"] = item.longitude
    if "targetLocation" in poi_json.keys():
        targetLocation = poi_json['targetLocation']
        if targetLocation in item.location_infos:
            location = item.location_infos[targetLocation]
            poi_json["latitude"] = location.lat
            poi_json["longitude"] = location.lon
        else:
            logger.info(f"not find targetLocation in location_infos, targetLocation is {targetLocation}, location_infos is {item.location_infos}")
            poi_json["code"] = error_code_location_missing_error
            poi_json["response_str"] = f"缺少搜索地点的位置信息，搜索地点为：{targetLocation}, 提供位置信息为{item.location_infos}"
    
    poi_search = item.poi_search
    poi_json["pageSize"] = 20
    # if "city" in poi_json.keys():
    #     city_keyword = poi_json["city"]
    #     city_name = city_search(city_keyword)
    #     if len(city_name) > 0:
    #         poi_json["city"] = city_name
    #     else:
    #         # poi_json["filter"]["otherPois"].append({"keyword": city_keyword})
    #         poi_json["poiArea"] = city_keyword
    #         del poi_json["city"]
    if "city" not in poi_json or poi_json["city"] == "":
        if item.city != "":
            # print('DEBUG: city:', item.city)
            poi_json["city"] = item.city

    results = json.dumps(poi_json, ensure_ascii=False)
    return MappingOutput(results=results, success=True)

if __name__ == "__main__":
    import sys
    history = History(max_save_poi_list_num=2, max_save_history_num=20)
    user_info = "我想吃青团？"
    history.user_record(user_info)
    intent_search_info = {
            "recommendPois": ["中式餐馆"],
            "Constraints": ["麻花"],
            "pageIndex": 1,
            "tips": "正在查找，请稍等。"
    }
    intent_search_info = json.dumps(intent_search_info, ensure_ascii=False)
    history = history.mapping_get_history_prompt()
    test_input = MappingInput(history=history, poi_search=intent_search_info)
    mapping_output = mapping_agent_call(test_input)
    content = ""
    for chunk in mapping_output:
        content += chunk
        print(chunk,end="")
    