import time

def duration_return_value1(func):
    def fun_duration(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        duration_time = time.time() - start_time
        end_time_perf_counter = time.perf_counter()
        duration_msg = {}
        duration_msg[func.__name__ + '_time'] = duration_time
        duration_msg["end_time_perf_counter"] = end_time_perf_counter
        return result, duration_msg
    return fun_duration

def duration_return_value2(func):
    def fun_duration(*args, **kwargs):
        start_time = time.time()
        result, duration_ret = func(*args, **kwargs)
        duration_time = time.time() - start_time
        duration_msg = {}
        duration_msg[func.__name__] = duration_ret
        duration_msg[func.__name__ + '_time'] = duration_time
        return result, duration_msg
    return fun_duration

def duration_return_value3(func):
    def fun_duration(*args, **kwargs):
        start_time = time.time()
        result1, result2, duration_ret = func(*args, **kwargs)
        duration_time = time.time() - start_time
        duration_msg = {}
        duration_msg[func.__name__] = duration_ret
        duration_msg[func.__name__ + '_time'] = duration_time
        return result1, result2, duration_msg
    return fun_duration