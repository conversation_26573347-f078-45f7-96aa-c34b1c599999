from .client import client
from pydantic import BaseModel
import json
import time
from .history_management import History
from typing import Generator
from .duration import duration_return_value1

from .intent_agent_stream import (
    intent_agent_call,
    intent_agent_process,
    IntentInput,
    IntentOutput,
)

@duration_return_value1
def intent_agent(item:IntentInput, start_time = -1) ->IntentOutput:
    response = intent_agent_call(item,start_time)
    if item.is_stream == True:
        return response
    else:
        response = intent_agent_call(item,start_time)
        return intent_agent_process(response,start_time)

if __name__ == "__main__":
    history = History()
    query = "导航到第二个"
    history.user_record(query)
    history_prompt = history.intent_get_history_prompt()
    intent_output = intent_agent(IntentInput(history=history_prompt))
    print(intent_output.results)
