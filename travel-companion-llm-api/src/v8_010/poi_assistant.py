import ast
import json
import re
import time

from loguru import logger
import openai
import requests

from .history_management import History
from .integrate_agent import integrate_agent, IntegrateInput
from .intent_agent import intent_agent, IntentInput
from .mapping_agent import mapping_agent, MappingInput
from .poi_search_api import poi_search_api
from .utiles import get_uuid
from src.prompts.follow_up_prompts import follow_up_prompts

from src.utiles.constants import *

class Assistant_010():
    def __init__(self, id=-1, max_save_poi_list_num=2, max_save_history_num=20, poi_list_max_len=10, user_like="", city=""):
        self.id = get_uuid() if id == -1 else str(id)
        self.history = History(self.id, max_save_poi_list_num=max_save_poi_list_num, max_save_history_num=max_save_history_num, poi_list_max_len=poi_list_max_len, user_like=user_like, city=city)

    def process_query(self, coordinate, nova_ak: str, nova_sk: str, track_id: str, model_name: str, start_time: float):
        newest_user_query, part_history = self.history.history_process()
        logger.info('model_name:{} user query:{} part_history:{}'.format(model_name, newest_user_query, part_history))
        response, poi_list, duration_msg = self.handle_poi_search(newest_user_query, coordinate, nova_ak=nova_ak,
                                                                         nova_sk=nova_sk, model_name=model_name,
                                                                         start_time = start_time,
                                                                         is_stream=False, track_id=track_id)

        return response, poi_list, duration_msg

    def handle_mapping_agent(self, coordinate, nova_ak: str, nova_sk: str, track_id: str):
        intent_history_prompt = self.history.intent_get_history_prompt()
        duration_msg_list = []
        intent_output, duration_msg = intent_agent(
            IntentInput(history=intent_history_prompt, id=self.id, nova_ak=nova_ak, nova_sk=nova_sk))
        intent_output = intent_output.results
        duration_msg_list.append(duration_msg)
        intent_output = json.loads(intent_output)
        params = intent_output["params"]
        poi_search = json.dumps(params, ensure_ascii=False)
        mapping_history_prompt = self.history.mapping_get_history_prompt()
        response, duration_msg = mapping_agent(
            MappingInput(history=mapping_history_prompt, id=self.id, poi_search=poi_search,
                         latitude=coordinate["latitude"], longitude=coordinate["longitude"], nova_ak=nova_ak,
                         nova_sk=nova_sk))
        response = response.results
        duration_msg_list.append(duration_msg)
        return response, params['Constraints'], duration_msg_list

    def handle_poi_search(self, query, coordinate, nova_ak: str, nova_sk: str, model_name: str, rewrite_model_name: str, start_time: float, is_stream=False, track_id="", location_infos={}):
        tips = ""
        response = "【助手提示】：" + tips

        mapping_history_prompt = self.history.mapping_get_history_prompt()

        duration_msg_list = {}
        non_null_user_like = "" if not self.history.user_like else self.history.user_like
        logger.info("intent start")

        try:
            mapping_output, duration_msg = mapping_agent(
                MappingInput(history=mapping_history_prompt, id=self.id, poi_search=query,
                             latitude=coordinate["latitude"], longitude=coordinate["longitude"], nova_ak=nova_ak,
                             nova_sk=nova_sk, model_name=rewrite_model_name, user_like=non_null_user_like,
                             location_infos=location_infos))
            if isinstance(duration_msg, dict) and isinstance(duration_msg.get("mapping_agent_time"), float):
                mapping_agent_cost_time = duration_msg.get("mapping_agent_time")
                logger.info(f"intent end-----cost time:{str(round(mapping_agent_cost_time, 4))}-----result:{mapping_output}")
            mapping_output = mapping_output.results
            if isinstance(duration_msg, dict) and "end_time_perf_counter" in duration_msg:
                try:
                    end_time_perf_counter = duration_msg.pop("end_time_perf_counter", start_time)
                    duration_msg["t3-intent"] = round((end_time_perf_counter - start_time), 4)
                except BaseException as e:
                    print(e)
            duration_msg_list.update(duration_msg)
            # print("【mapping_output】")
            # logger.info(f"track_id: {track_id} mapping_output {mapping_output}")

        except (requests.exceptions.RequestException, openai.APIConnectionError, openai.APIError) as e:
            # 捕获网络连接相关的错误/API错误
            logger.error(f"Mapping agent network/internal error: {e}")
            response = {"code": error_code_pre_intent_net_error, "response_str": "意图识别模型请求错误"}
            return response, [], duration_msg_list
            
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            # 捕获JSON解析错误、值错误、键错误等格式问题
            logger.error(f"Mapping agent format error: {e}")
            response = {"code": error_code_pre_intent_format_error, "response_str": "意图识别模型返回内容格式错误"}
            return response, [], duration_msg_list

        except Exception as e:
            # 捕获其他错误
            logger.error(f"Unexpected error in mapping agent stage: {e}")
            response = {"code": error_code_pre_intent_net_error, "response_str": "意图识别模型请求错误"}
            return response, [], duration_msg_list

        self.history.mapping_record(mapping_output)
        mapping_output_json = json.loads(mapping_output)
        if "code" in mapping_output_json:
            response = {}
            response["response_str"] = mapping_output_json['response_str']
            response["code"] = mapping_output_json['code']
            response["mapping_output"] = mapping_output
            return response, [], duration_msg_list
        if not json.loads(mapping_output)['intent']:
            response = {}
            response_str = "与POI不相关问题"
            response["response_str"] = response_str
            response["code"] = error_code_irrelevant
            response["mapping_output"] = mapping_output
            return response, [], duration_msg_list
        logger.info("src start")
        poi_list, duration_msg = poi_search_api(mapping_output, track_id=track_id)

         # 错误检查逻辑
        try:
            poi_list_json = json.loads(poi_list)
            if isinstance(poi_list_json, dict) and "code" in poi_list_json and "response_str" in poi_list_json:
                 return poi_list_json, [], duration_msg_list
        except json.JSONDecodeError:
            pass

        if isinstance(duration_msg, dict) and isinstance(duration_msg.get("poi_search_api_time"), float):
            poi_search_cost_time = duration_msg.get("poi_search_api_time")
            logger.info(
                f"src end-----cost time:{str(round(poi_search_cost_time, 4))}\n-----poi_list:{poi_list}")
        # logger.info(f"track_id: {track_id} poi_list {poi_list}")

        if isinstance(duration_msg, dict) and "end_time_perf_counter" in duration_msg:
            try:
                end_time_perf_counter = duration_msg.pop("end_time_perf_counter", start_time)
                duration_msg["t4-src"] = round((end_time_perf_counter - start_time), 4)
            except BaseException as e:
                print(e)
        duration_msg_list.update(duration_msg)

        self.history.poi_search_record(poi_list)
        if len(json.loads(poi_list)['data']['dataList']) == 0:
            response = {}
            response_str = "没有搜索到POI地点"
            response["response_str"] = response_str
            response["code"] = error_code_src_search_empty
            response["mapping_output"] = mapping_output
            response["poi_list"] = []
            return response, [], duration_msg_list

        # poi_list2 = json.loads(poi_list)

        integrate_history_prompt, poi_list = self.history.integrate_get_history_prompt()
        # print(poi_list)

        try:
            if not is_stream:
                logger.info("llm start-----stream:false")
            integrate_output, duration_msg = integrate_agent(
                IntegrateInput(history=integrate_history_prompt, is_stream=is_stream, id=self.id, poi_list=poi_list,
                            nova_ak=nova_ak, nova_sk=nova_sk, model_name=model_name))
            if not is_stream:
                if isinstance(duration_msg, dict) and isinstance(duration_msg.get("integrate_agent_time"), float):
                    integrate_agent_cost_time = duration_msg.get("integrate_agent_time")
                    logger.info(f"llm end-----stream:false-----total time:{str(round(integrate_agent_cost_time,4))}")
            # logger.info("track_id: {} integrate_output {} duration_msg {}".format(track_id, json.dumps(integrate_output.results), duration_msg))
            duration_msg_list.update(duration_msg)

        except (requests.exceptions.RequestException, openai.APIConnectionError) as e:
            # 捕获网络连接相关的错误
            logger.error(f"Integrate agent network error: {e}")
            response = {"code": error_code_chat_summary_http_error, "response_str": "调用整合模型网络错误"}
            return response, [], duration_msg_list
        
        except openai.APIError as e:
            # 捕获模型服务端返回的API错误
            logger.error(f"Integrate agent internal error: {e}")
            response = {"code": error_code_chat_summary_llm_error, "response_str": "整合模型内部错误"}
            return response, [], duration_msg_list
            
        except Exception as e:
            # 捕获其他错误
            logger.error(f"Unexpected error in integrate agent stage: {e}")
            response = {"code": error_code_chat_summary_llm_error, "response_str": "总结模型内部错误"}
            return response, [], duration_msg_list

        if is_stream == True:
            return integrate_output, poi_list, duration_msg_list
        else:
            integrate_output_result = integrate_output.results

            self.history.integrate_record(integrate_output_result)

            integrate_output_result = json.loads(integrate_output_result)
            recommend = integrate_output_result["recommend"]
            poi_list = json.dumps(integrate_output_result["poi_list"]["dataList"], ensure_ascii=False, indent=4)
            response = {}
            response_str = "【助手推荐】：" + recommend + "\n【推荐poi list展示】：\n"
            response["poi_list"] = integrate_output_result["poi_list"]["dataList"]
            response["recommend"] = recommend
            response["response_str"] = response_str
            response["tips"] = tips
            response["mapping_output"] = mapping_output
            response["code"] = 200
            response["title"] = integrate_output_result["title"]
            return response, poi_list, duration_msg_list

    def handle_poi_recommend(self, nova_ak: str, nova_sk: str, is_stream=False):
        integrate_history_prompt, poi_list = self.history.integrate_get_history_prompt()
        integrate_output, duration_msg = integrate_agent(
            IntegrateInput(history=integrate_history_prompt, is_stream=is_stream, id=self.id, poi_list=poi_list,
                           nova_ak=nova_ak, nova_sk=nova_sk))
        duration_msg_list = []
        duration_msg_list.append(duration_msg)
        if is_stream == True:
            return integrate_output, poi_list, duration_msg_list
        else:
            integrate_output = integrate_output.results
            self.history.integrate_record(integrate_output)

            integrate_output = json.loads(integrate_output)
            recommend = integrate_output["recommend"]
            poi_list = json.dumps(integrate_output["poi_list"]["dataList"], ensure_ascii=False, indent=4)

            response = {}
            response_str = "【助手推荐】：" + recommend + "\n【推荐poi list展示】：\n"
            response["poi_list"] = integrate_output["poi_list"]["dataList"]
            response["recommend"] = recommend
            response["response_str"] = response_str
            return response, poi_list, duration_msg_list

    async def get_follow_up_questions(self, user_query: str, assistant_response: str, nova_ak: str, nova_sk: str, model_name: str):
        from .client import client
        fup_start_time = time.perf_counter()
        no_action_history = self.history.get_no_action_history()

        if len(no_action_history) < 2:
            # 当历史记录不足两条时，使用当前这一轮的问答作为上下文
            last_user_message = user_query
            last_assistant_message = assistant_response
        else:
            # 当历史记录不为空时，使用历史的最后两条
            last_user_message = no_action_history[-2]['message']
            last_assistant_message = no_action_history[-1]['message']
        
        # 清理可能的复杂结构，只保留文本
        if isinstance(last_assistant_message, dict):
            last_assistant_message = last_assistant_message.get("recommend", "")

        # 拼接对话历史
        chat_his = str(last_user_message) + "\n" + str(last_assistant_message)
        
        # 使用导入的 prompt
        prompt = follow_up_prompts.format(chat_his=chat_his)
        messages = [{"role": "user", "content": prompt}]

        try:
            # 调用LLM并返回原始字符串
            response_gen = client(messages, self.id, model_name=model_name, is_stream=False, nova_ak=nova_ak, nova_sk=nova_sk)
            
            response_str = ""
            for chunk in response_gen:
                response_str += chunk

            # 检查返回内容是否符合要求
            match = re.search(r'\[.*?\]', response_str)
            if not match:
                # 如果找不到列表格式，则抛出ValueError
                raise ValueError("LLM response for follow-ups does not contain a list.")
            
            # 尝试解析，如果失败会抛出SyntaxError
            parsed_list = ast.literal_eval(match.group())
            
            # 必须是字符串列表
            if not isinstance(parsed_list, list) or not all(isinstance(item, str) for item in parsed_list):
                raise ValueError("It is not a standard string list.")

            return response_str, round(time.perf_counter() - fup_start_time, 4) # 成功时返回原始字符串

        except (requests.exceptions.RequestException, openai.APIConnectionError, openai.APIError) as e:
            logger.error(f"Follow-ups network error/model internal error: {e}")
            return {"code": error_code_relative_http_error, "response_str": "调用推荐问题模型网络错误/模型内部错误"}, round(time.perf_counter() - fup_start_time, 4)
            
        except (ValueError, SyntaxError) as e:
            logger.error(f"Follow-ups format error: {e} - Response: {response_str}")
            return {"code": error_code_relative_format_error, "response_str": "推荐问题模型返回内容不符合要求"}, round(time.perf_counter() - fup_start_time, 4)
            
        except Exception as e:
            logger.error(f"Unexpected error in follow-ups stage: {e}")
            return {"code": error_code_relative_http_error, "response_str": "推荐问题模型内部错误"}, round(time.perf_counter() - fup_start_time, 4)