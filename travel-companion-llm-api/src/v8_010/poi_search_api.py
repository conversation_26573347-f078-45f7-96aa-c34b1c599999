import copy
import json
import queue
import threading
import time

import numpy as np
import requests
from loguru import logger

from .duration import duration_return_value1
from configuration import config

from src.utiles.constants import *

def merge(responseAC: list, responseB: list) -> dict:  # [dict]
    datalistA = []
    datalistB = []
    datalistC = []

    dataset = set()
    # tot = []
    # totPages = []
    page = []
    pagesize = []

    for content in responseAC:
        if 'data' not in content and 'total' not in content['data']:
            break

        # tot.append(content['data']['total'])
        if int(content['data']['pageSize']) > 0:
            # totPages.append(content['data']['totalPages'])
            page.append(content['data']['page'] - 1)
            pagesize.append(content['data']['pageSize'])
            for data in content['data']['dataList']:
                ishash = "{}-{:.2f}-{:.2f}".format(data['name'], float(data['longitude']), float(data['latitude']))
                if ishash not in dataset:
                    dataset.add(ishash)
                    if 'otherPois' in data and isinstance(data['otherPois'], list) and len(data['otherPois']) > 0:
                        datalistA.append(data)
                    else:
                        datalistC.append(data)

    for content in responseB:
        if 'data' not in content and 'total' not in content['data']:
            break

        # tot.append(content['data']['total'])
        if int(content['data']['pageSize']) > 0:
            # totPages.append(content['data']['totalPages'])
            page.append(content['data']['page'])
            pagesize.append(content['data']['pageSize'])
            for data in content['data']['dataList']:
                ishash = "{}-{:.2f}-{:.2f}".format(data['name'], float(data['longitude']), float(data['latitude']))
                if ishash not in dataset:
                    dataset.add(ishash)
                    datalistB.append(data)

    datalistA = sorted(datalistA, key=lambda x: int(x['distance']))
    datalistB = sorted(datalistB, key=lambda x: int(x['distance']))
    datalistC = sorted(datalistC, key=lambda x: int(x['distance']))

    merge_res = {}

    merge_res['status'] = 0
    merge_res['data'] = {}
    # merge_res['data']['total'] = str(np.min(tot))
    # merge_res['data']['totalPages'] = str(np.min(totPages)) if totPages else ""

    merge_res['data']['page'] = str(np.min(page)) if page else ""

    merge_res['data']['dataList'] = datalistA + datalistB + datalistC
    merge_res['data']['pageSize'] = len(merge_res['data']['dataList'])
    return merge_res


def poi_search_base(ids, payload: str, result_queue, track_id):
    start_time = time.time()
    try:
        # 当没有city 和 poiArea代表周边搜索，默认加上搜索半径字段
        payload_dict = json.loads(payload)
        if 'city' not in payload_dict and 'poiArea' not in payload_dict:
            if 'searchRadius' not in payload_dict:
                payload_dict['searchRadius'] = 50000

        payload = json.dumps(payload_dict)
        
        url = config["get_poi"]["url"]
        client_id = config["get_poi"]["client_id"]

        headers = {
            'client-id': client_id,
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload).text
        response = json.loads(response)
        response["data"]["pageSize"] = len(response["data"]["dataList"])
        # response["data"]["total"] = 100
        # response["data"]["totalPages"] = 3
        l = len(response['data']['dataList'])
        response = json.dumps(response, ensure_ascii=False)

        logger.info(f"track-id: {track_id} poi_search {payload_dict} find_pois_len {l} duration_time = {time.time() - start_time}")

        # logger.info(f"track-id: {track_id} poi_search_response {response}")
        result_queue.put((ids, response))

    except requests.exceptions.RequestException as e:
        logger.error(f"POI search network error in thread: {e}")
        error_response = {"code": error_code_src_search_http_error, "response_str": "调用POI搜索服务网络错误"}
        # 将一个特殊的错误对象放入队列
        result_queue.put((ids, json.dumps({"error": error_response})))

    except Exception as e:
        logger.error(f"POI search internal/format error in thread: {e}")
        error_response = {"code": error_code_src_search_http_error, "response_str": "POI搜索服务内部错误"}
        # 将一个特殊的错误对象放入队列
        result_queue.put((ids, json.dumps({"error": error_response})))

@duration_return_value1
def poi_search_api(payload: str, track_id="") -> str:
    payload = json.loads(payload)
    payload["pageLimit"] = True

    ori_load = [copy.deepcopy(payload)]
    # 初始化线程队列
    result_queue = queue.Queue()
    # 创建线程列表
    threads = []
    start = time.time()
    for id, ori in enumerate(ori_load):
        ori_str = json.dumps(ori)  # 将 ori 转换为字符串
        # 创建一个线程执行 poi_search_base 任务

        t = threading.Thread(target=poi_search_base, args=(id, ori_str, result_queue, track_id))
        threads.append(t)
    # 启动所有线程
    for thread in threads:
        thread.start()

    # 等待所有线程执行完毕
    for thread in threads:
        thread.join()
    # print(otherpois2poiarea_id)
    # 从队列中收集结果
    responseAC = []
    responseB = []
    while not result_queue.empty():
        content = result_queue.get()
        id = content[0]
        # print(id)
        response_json = json.loads(content[1])

        # 检查是否是错误信号
        if "error" in response_json:
            # 如果是，立即将错误信息返回给上层调用者
            return json.dumps(response_json["error"])

        responseB.append(response_json)

    # print('tot_time:{:.2f}'.format(time.time()-start))
    megedres = merge(responseAC, responseB)

    return json.dumps(megedres, ensure_ascii=False)


if __name__ == "__main__":
    # 现在不会出现没有poiName只有PoiType的情况。

    # results = {"poiType": ["咖啡馆", ], "latitude": "40.081839", "longitude": "116.586733",
    #            "poiName": ["咖啡馆"], "poiTags": [""], "page": 1, "pageSize": 5,
    #            "searchRadius": 25000, "filter": {"otherPois": [{'keyword': "颐和园"}]}}
    # results = {"poiType": ["体育休闲服务"], "latitude": "40.081839", "longitude": "116.586733",
    #              "poiName": ["体育休闲"], "poiTags": [""], "page": 1, "pageSize": 5,
    #              "searchRadius": 15000, "filter": {"otherPois": []}}
    results = {
      "keywords": "青团",
      "latitude": "39.984488",
      "longitude": "116.310072",
      "city": "北京市",
      "page": 1,
      "pageSize": 10,
      "searchRadius": 2000
    #   "poiArea": "西单"
    }
    # results = {"poiType": ["餐饮相关"], "latitude": "40.081839", "longitude": "116.586733", "poiName": ["肯德基"], "page": 1,
    #    "pageSize": 5, "searchRadius": 15000, "filter": {"otherPois": []}}
    payload = json.dumps(results, ensure_ascii=False, indent=4)
    start = time.time()
    response = poi_search_base("", payload, queue.Queue(), "debug")
    # print(response)
    # response = poi_search_api2(payload)
    # print(type(response))
    # print(json.loads(response))
    # print(f'{time.time() - start} sec')
    # poi_list_info = json.loads(response)
