from .client import client
from pydantic import BaseModel
import json
import time
from .history_management import History
from .mapping_agent_stream import (
    mapping_agent_call,
    mapping_agent_process,
    MappingInput,
    MappingOutput,
)
from .duration import duration_return_value1
from loguru import logger


@duration_return_value1
def mapping_agent(item: MappingInput) -> MappingOutput:
    item.is_stream =False

    response = mapping_agent_call(item)
    
    output = mapping_agent_process(item, response)
    logger.info(output)
    return output

if __name__ == "__main__":
    import sys
    user_info = sys.argv[1]
    history = History(0, max_save_poi_list_num=2, max_save_history_num=20)
    # user_info = "帮我搜索一家附近的火锅店"
    history.user_record(user_info)

    history = history.mapping_get_history_prompt()
    print('history:', history)
    test_input = MappingInput(
        model_name="SenseAutoChat-30B",
        history=history,
        poi_search=user_info,
        latitude="39.984488",
        longitude="116.310072",
        is_stream=False
    )
    mapping_output = mapping_agent(test_input)
    print(mapping_output)
    # print(history.integrate_get_history_prompt())
    # print(history.mapping_get_history_prompt())
