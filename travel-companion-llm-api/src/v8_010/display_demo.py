import time
import traceback

from poi_assistant import Assistant
from utiles import save_interaction_results

def save_if_needed(assistant, conversation): 
    save_path = ""
    if conversation:
        save_path = assistant.history.save_content()
        save_interaction_results(conversation, assistant.id)
        print(f'ID {assistant.id} saved!')
    return save_path


if __name__ == "__main__":

    assistant = Assistant()

    conversation = []

    print("【系统提示】：输入 exit 可以结束本轮对话; 输入 clear 可以清空历史记录")
    try:
        while True:

            query = input("【用户】：")

            time_start = time.time()

            if query == "exit":
                save_if_needed(assistant, conversation)
                break
            elif query == "clear":
                save_if_needed(assistant, conversation)

                assistant = Assistant()
                conversation = []
                print("【系统提示】：历史记录已清除！")
                continue
            
            response = assistant.process_query(query)
            print(response)

            time_end = time.time()
            print("【系统提示】：响应耗时 - ",time_end-time_start)

            conversation.append({
            "query": query,
            "response": response
            })

    except Exception as e:
        save_path = save_if_needed(assistant, conversation)
        if "KeyboardInterruptException" in str(e):
            print("\n【会话结束】")
            print("log数据保存地址：", save_path)
        else:
            print("\n【异常中断】")
            print("log数据保存地址：", save_path)
            print(e)
            print("Stack trace:")
            traceback.print_exc()