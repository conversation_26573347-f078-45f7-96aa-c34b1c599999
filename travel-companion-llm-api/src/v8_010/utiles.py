import os
import uuid
from datetime import datetime

def get_uuid():
    return str(uuid.uuid4())

def get_timestamp():
    return datetime.now().strftime('%Y%m%d_%H%M%S')

def save_interaction_results(results, id):
    history_dir = "inter_result"
    os.makedirs(history_dir, exist_ok=True)

    output_file = f'{id}_{get_timestamp()}_interaction.txt'

    save_path = history_dir + "\\" + output_file
    results_string = ""
    for item in results:
        for key in item:
            if key == "query":
                results_string += "【用户】 " + item[key] + "\n\n"
            else:
                results_string += item[key] + "\n"
    with open(save_path, 'w', encoding='utf-8') as file:
        file.write(results_string)