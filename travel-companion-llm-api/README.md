# online_search_framework

### 环境安装
python3.11

`pip install -r requirements.txt`

### 执行：
```
python main.py --port 8080

# 新开一个窗口调用刚刚起的服务，is_stream可以改成true，则支持流式，新增了rewrite模型的输入
curl --location 'http://localhost:8080/gac/travel-companion/v2/chat' \
--header 'client-id: 1s3963nw8802M4O55yMuU6x37tOYQ682' \
--header 'Content-Type: application/json' \
--data '{
    "location": {
        "lat": "23.02965",
        "lon": "113.49027"
    },
    "model_name": "SenseAutoChat-30B",
    "rewrite_model_name": "POI-rewrite-7B",
    "messages": [
        {
            "role": "user",
            "content": "帮我搜一下家五公里内的健身房"
        }
    ],
    "is_stream": false,
    "user_info":{
        "car_id":"demoCar82951",
        "user_id":"2",
        "category":["natural_landscape_preference", "human_landscape_preference", "entertainment_landscape_preference", "travel_activity"]
    },
    "location_home": {
        "lat": "40.098467",
        "lon": "116.362006"
    }
}'
```