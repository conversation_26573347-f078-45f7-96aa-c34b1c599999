import json
from argparse import ArgumentParser

import requests
import time

def travel_companion(url, input_data):
    payload = json.dumps(input_data)

    response = requests.request("POST", url, data=payload)

    response = json.loads(response.text)
    response = json.dumps(response, indent=4, separators=(',', ':'), ensure_ascii=False)
    print(response)

def parse_args():
    parser = ArgumentParser()
    parser.add_argument("hostname", nargs="?", default="localhost")
    parser.add_argument("--port", type=int, default=8080)
    return parser.parse_args()

if __name__ == "__main__":
    # event_type = sys.argv[1]
    args = parse_args()
    hostname = args.hostname
    port = args.port
    url = f"http://{hostname}:{port}/v6/travel_companion/intent"
    print(f"url: {url}")

    input_data = {
        "messages": [
            {
                "role": "user",
                # "content": ""
                "content": "找一家颐和园附近的咖啡厅，不要瑞幸"
            }
        ]
    }

    start = time.time()
    travel_companion(url, input_data)
    print(f"intent cost: {time.time() - start}")


    # url = f"http://{hostname}:{port}/v6/travel_companion/filter_chat"
    # print(f"url: {url}")

    # input_data = {
    #     "filter_keyword": ["知春路"],
    #     "poi_list": ["麦当劳(知春路店)", "肯德基(上地店)", "川渝麻辣香锅(知春路店)"]
    # }

    # start = time.time()
    # travel_companion(url, input_data)
    # print(f"filter cost: {time.time() - start}")
