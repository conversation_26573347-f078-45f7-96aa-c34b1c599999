import json
from argparse import ArgumentParser

import requests
import time

def travel_companion(url, input_data):
    payload = json.dumps(input_data)

    response = requests.request("POST", url, data=payload)

    response = json.loads(response.text)
    response = json.dumps(response, indent=4, separators=(',', ':'), ensure_ascii=False)
    return response

def parse_args():
    parser = ArgumentParser()
    parser.add_argument("hostname", nargs="?", default="localhost")
    parser.add_argument("--port", type=int, default=8080)
    return parser.parse_args()

if __name__ == "__main__":
    # event_type = sys.argv[1]
    args = parse_args()
    hostname = args.hostname
    port = args.port
    url = f"http://{hostname}:{port}/v6/travel_companion/intent"
    print(f"url: {url}")

    with open("test_query.txt", "r") as f:
        lines = f.readlines()
    
    results = []
    for line in lines:
        query = line.strip()
        input_data = {
            "messages": [
                {
                    "role": "user",
                    # "content": "车子没电了开不走了，怎么办"
                    "content": query
                }
            ]
        }

        start = time.time()
        response = travel_companion(url, input_data)
        elapsed_time = round((time.time() - start), 2)
        print(f"intent cost: {elapsed_time}")
        response = json.loads(response)
        res = response["results"]
        new_res = []
        for s_res in res:
            s_res_ = json.loads(s_res)
            new_res.append(s_res_)
        
        response["results"] = new_res
        response["query"] = query
        response["elapsed_time"] = elapsed_time

        results.append(response)
    print(results)
    with open("result1.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=4, ensure_ascii=False)
