import faulthandler
import os
import warnings
import uvicorn
from fastapi import FastAPI
from loguru import logger
import logfire


import configuration  # import before DB related
from service.logger_manager import fire_logger
from src.utils.actions.tool_actions.bing import Bing
from src.utils.actions.tool_actions.serper import Ser<PERSON>
from src.utils.actions.tool_actions.tencent import Tencent

faulthandler.enable()  # capture underlying C errors

warnings.filterwarnings("ignore", message="Unverified HTTPS request")
global_config = configuration.config
selected_llm_config = global_config["llm_server_set"][
    global_config["selected_llm_server"]
]
serper_se = Serper(global_config["search_engine"]["serper"], "google")
bing_se = Bing(bing_config=global_config["search_engine"]["bing"], engine_name="bing")
tencent_se = Tencent(config=global_config["search_engine"]["tencent"], engine_name="tencent")
tencent_se_wsa = Tencent(config=global_config["search_engine"]["tencent_wsa"], engine_name="tencent_wsa")
rewrite_llm_config = selected_llm_config.copy()  # shallow copy
for k in global_config["rewrite"].keys():  # some override parameters
    rewrite_llm_config[k] = global_config["rewrite"][k]

get_poi_config = global_config["get_poi"]

logger.info(
    f"selected_llm_server: {global_config['selected_llm_server']}: {selected_llm_config['base_url']}"
)
logger.info(f"rewrite_llm: {rewrite_llm_config['base_url']}")
# logger.info(f"rewrite_llm: {rewrite_llm_config}")


mode2model_name = {
    "prime": global_config["selected_llm_server"],
    "turbo": global_config["selected_llm_server"],
    "light": global_config["selected_llm_server"],
}
mode2topk_strategy = {"prime": "embedding", "turbo": "reranker", "light": ""}

service_name = "gac-ai-traveler"
app = FastAPI(title=service_name)


# service_name = os.environ.get("SERVICE_NAME", "c")
logfire.configure(
    send_to_logfire=False,
    service_name=service_name
)
logfire.instrument_fastapi(app, excluded_urls="/health")
fire_logger.addHandler(logfire.LogfireLoggingHandler())

logfire.instrument_openai()

from api.router import router_for_gac_dify
app.include_router(router_for_gac_dify)

if __name__ == "__main__":


    uvicorn.run(
        app,
        host="0.0.0.0",
        port=global_config["settings"]["port"],
        workers=1,
        use_colors=True,
    )
