import asyncio, aiohttp
from typing_extensions import Optional
from configuration import config
from aiohttp import ClientTimeout
from loguru import logger
from schemas.models import ErrorDataModel
from src.traveler_exception import error_code_mem_http_error, error_code_mem_return_error



url=config["user_memory"]["url"]

async def _request_habit_from_user_memory_raw(body: dict, time_limit: float = 0.8) -> [Optional[dict], Optional[ErrorDataModel]]:
    header={'Content-Type': 'application/json'}
    timeout = ClientTimeout(total=time_limit)
    try:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(url=url, headers=header, json=body) as response:
                if response.status == 200:
                    return await response.json(), None
                else:
                    return None, ErrorDataModel(error_code=error_code_mem_return_error, error_message=f"Return status: {response.status}")
    except (aiohttp.ClientError) as e:
        logger.error(f"_request_habit_from_user_memory_raw error [aiohttp.ClientError] e: {e}")
        return None, ErrorDataModel(error_code=error_code_mem_http_error, error_message=f"Return status: {response.status}")

    except asyncio.TimeoutError as e:
        logger.error(f"_request_habit_from_user_memory_raw error [asyncio.TimeoutError] e: {e}")
        return None, ErrorDataModel(error_code=error_code_mem_http_error, error_message=f"Request Time out, time_limit: {time_limit}")


# realtion_filter 表示需要筛选的关系类型，如 LIKES 、 INTRERSTED_IN 等
async def request_habit_from_user_memory_text(body: dict, relation_filter: str, time_limit: float = 0.8) -> [Optional[str], Optional[ErrorDataModel]]:
    error_info = None
    try:
        all_info, error_info = await asyncio.wait_for(
            _request_habit_from_user_memory_raw(body=body, time_limit=time_limit),
            timeout=time_limit
        )
    except asyncio.TimeoutError:
        logger.error("request_habit_from_user_memory_text timeout")
        return None, ErrorDataModel(error_code=error_code_mem_http_error, error_message=f"Process Request Time out, time_limit: {time_limit}")
    if not all_info:
        return None, error_info
    print(all_info)
    status = all_info.get("status", {})
    status_code = status.get("code", "")
    if "200" != status_code:

        return None, ErrorDataModel(error_code=error_code_mem_return_error, error_message=f"Return status: {status_code}")
    data = all_info.get("data", {})
    data_list = data.get("dataList", [])
    # relation_filter 不再使用，返回完整的用户画像
    filtered_data_list = data_list
    # filtered_data_list = [temp for temp in data_list if isinstance(temp, str) and relation_filter in temp]
    if 0 >= len(filtered_data_list):
        return None, None
    else:
        try:
            temp_care = "User "
            cares = ", ".join(filtered_data_list)
            return temp_care + cares + ".", None
        except BaseException as e:
            print(e)
            return None, ErrorDataModel(error_code=error_code_mem_return_error, error_message=f"cant format result")
        