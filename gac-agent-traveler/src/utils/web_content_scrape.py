import asyncio
import concurrent.futures
import re
from concurrent.futures import <PERSON>PoolExecutor, ThreadPoolExecutor, Future

import aiohttp
import html2text
from fake_useragent import UserAgent
from loguru import logger
from readability import Document
from starlette.concurrency import run_in_threadpool

from src.utils.url_crawl_helper import CrawlHelper


class WebScrape:
    def __init__(
            self, scrape_config, limit_scraping_time: str = "1200ms", min_extract_length=200
    ):
        self.limit_scraping_time = limit_scraping_time

        self.min_extract_length = min_extract_length
        self.scrape_url = scrape_config["go-readability"]["url"]

    async def scrape(self, ori_query: str, queries_links: dict):
        self.errors = []
        query_scrape_res = {query: [] for query in queries_links}
        all_urls = []
        url_query_map = {}

        for query, urls in queries_links.items():
            for url in urls:
                url_query_map[url] = query
                all_urls.append(url)

        crawler = CrawlHelper(self.scrape_url, timeout=self.limit_scraping_time)
        results = await crawler.batch_process(all_urls)

        for i, url in enumerate(all_urls):
            try:
                result = results[i]
                clean_text = ""
                summary = result["summary"]
                if summary:
                    if len(summary) <= self.min_extract_length:
                        # self.errors.append(f"Fail: web length too short for {url}")
                        pass
                    else:
                        text_main = re.sub(r"\s*\n+", "\n", summary).strip()
                        lines = text_main.splitlines()
                        clean_text = "\n".join(
                            line.strip() for line in lines if len(line.strip()) > 50
                        )
                else:
                    self.errors.append(f"Fail: nothing received from {url}")
            except Exception:
                self.errors.append(f"Fail: except from {url}")
                clean_text = ""

            query = url_query_map[url]
            query_scrape_res[query].append({"url": url, "cleaned_text": clean_text})

        # for error in self.errors:
        #     logger.error(error)

        total_urls = len(all_urls)
        logger.info(f"Query {ori_query} Error rate: {len(self.errors)}/{total_urls}")
        return query_scrape_res, f"Error scrape rate: {len(self.errors)}/{total_urls}"


def extract_html_content_in_separate_process(url, html, min_text_length=200):
    if not html:
        return ""

    if "scarping_error" in html:
        return html
    try:
        doc = Document(html)
        html_main = doc.summary()
        image_sources = re.findall(r'<img[^>]+src="([^">]+)"', html_main)
        image_sources = [
            src for src in image_sources if not src.lower().endswith((".gif", ".svg"))
        ]
        config = html2text.HTML2Text()
        config.ignore_links = True
        config.ignore_images = True
        config.body_width = min_text_length
        config.ignore_emphasis = True
        config.ignore_tables = False
        text_main = re.sub(r"\s*\n+", "\n", config.handle(html_main)).strip()
        lines = text_main.splitlines()
        text_main = "\n".join(line for line in lines if len(line.strip()) > 50)

        if len(text_main) < min_text_length:
            return ""
        return text_main
    except Exception as _:
        return ""


class WebScrapePy:
    def __init__(self, user_agent="macOS", limit_scarping_time=0.3):
        self.headers = self._get_headers(user_agent)
        self.limit_scarping_time = limit_scarping_time

    def _get_headers(self, user_agent: str) -> dict:
        return {
            "User-Agent": UserAgent().random,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            # "Referer": "https://www.google.com/",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
        }

    async def get_html(self, session: aiohttp.ClientSession, url):
        try:
            async with session.get(
                    url,
                    headers=self.headers,
                    ssl=False,
                    timeout=aiohttp.ClientTimeout(self.limit_scarping_time),
            ) as response:
                response.raise_for_status()
                try:
                    return await response.text()
                except UnicodeDecodeError:
                    return await response.text(encoding="gbk")

        except Exception as e:
            if isinstance(e, asyncio.TimeoutError):
                self.errors.append(f"Fail {str(e)})")
                return ""
            else:
                self.errors.append(f"Fail {str(e)})")
                return ""

    def extract_content(self, urls, htmls):  # run in threadpool
        with (
            ProcessPoolExecutor(max_workers=1) as executor
        ):  # 我知道设置为1非常疑惑，但是经过反复测试max_workers调高速度反而下降非常严重
            results = []
            futures = [
                executor.submit(extract_html_content_in_separate_process, url, html)
                for url, html in zip(urls, htmls)
            ]

            def wait_future_result(future: Future):
                try:
                    r = future.result(timeout=1)
                    return r
                except concurrent.futures.process.BrokenProcessPool as e:
                    # fire_logger.error("%s", str(e))  # TODO: 可以加上重试机制
                    self.errors.append(f"Fail {str(e)})")
                except concurrent.futures.TimeoutError as _:
                    logger.warning("concurrent.futures.TimeoutError")

                except Exception as e:
                    logger.warning("%s", str(e))
                    # fire_logger.warning("%s", str(e))
                return ""

            try:
                with ThreadPoolExecutor() as thread_executor:
                    results = thread_executor.map(wait_future_result, futures)
                    results = [res for res in results]

                    for r in results:
                        if not r:
                            self.errors.append("Fail)")
            except Exception as e:
                logger.warning(f"Fail {str(e)}")
                # fire_logger.error("%s", str(e))
            executor.shutdown(wait=False, cancel_futures=True)
        return results

    async def scrape(self, ori_query, queries_links):
        self.errors = []
        query_scrape_res = {query: [] for query in queries_links}
        all_urls = []
        url_query_map = {}

        for query, urls in queries_links.items():
            for url in urls:
                url_query_map[url] = query
                all_urls.append(url)

        async with aiohttp.ClientSession() as session:
            tasks = [self.get_html(session, url) for url in all_urls]

            html_results = await asyncio.gather(*tasks)
            # fire_logger.info("fetch html finish")
        logger.debug(all_urls)
        extract_html_results = await run_in_threadpool(
            self.extract_content, all_urls, html_results
        )
        logger.debug(extract_html_results)
        # fire_logger.info("extract html finish")

        for url, cleaned_text in zip(all_urls, extract_html_results):
            query = url_query_map[url]
            query_scrape_res[query].append({"url": url, "cleaned_text": cleaned_text})

        total_urls = len(all_urls)
        logger.info(
            f"Query '{ori_query}' with {len(queries_links)} queries scraped. "
            + "Error rate: "
            + f"{len([0 for r in extract_html_results if not r])}/{total_urls}"
        )
        for error in self.errors:
            logger.error(error)
        return (
            query_scrape_res,
            f"Error scrape rate: {len([0 for r in extract_html_results if not r])}/{total_urls}",
        )


if __name__ == "__main__":
    queries_links = {
        "example_query": [
            "https://www.thepaper.cn/newsDetail_forward_29168991",
            "http://cc.bingj.com/cache.aspx?q=%E8%96%9B%E4%B9%8B%E8%B0%A6+%E8%8B%B1%E6%96%87%E5%90%8D&d=5022041301476454&mkt=zh-HK&setlang=zh-HK&w=26_gHKFSXFPrwudfMW8eikwntsVtWMzs",
            "http://cc.bingj.com/cache.aspx?q=%E8%96%9B%E4%B9%8B%E8%B0%A6+%E8%8B%B1%E6%96%87%E5%90%8D&d=4757750493830510&mkt=zh-HK&setlang=zh-HK&w=twzZh39AuMJzk1GuYcfUSgKA4iEglm2L",
        ]
    }

    scraper = WebScrapePy(limit_scarping_time=0.8)

    asyncio.run(scraper.scrape("ori_query", queries_links))
