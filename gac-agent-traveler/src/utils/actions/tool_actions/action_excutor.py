from typing import Dict

from loguru import logger

from src.utils.actions.tool_actions.action_base import BaseAction


class ActionExecutor:
    def __init__(self, actions_classes: list):
        self.actions_call_map = {}
        self.actions_info_map: Dict[str, dict] = {}
        for action in actions_classes:
            logger.debug(f"Executing action : {action}")
            self.actions_call_map.update(action.get_action_names())
            logger.debug(f"actions_call_map : {self.actions_call_map}")

    def add_action(self, action: BaseAction):
        """Adds all actions from a BaseAction object to the actions_map."""
        new_actions = action.get_action_names()
        self.actions_call_map.update(new_actions)

    def delete_action(self, action_name: str):
        """Deletes an action from the actions_map by its name."""
        if action_name in self.actions_call_map:
            del self.actions_call_map[action_name]
        else:
            raise ValueError(f"Action '{action_name}' not found in actions map.")

    def __call__(self, action_name: str, action_input: str, search_nums: int):
        return self.actions_call_map[action_name](action_input, search_nums)
