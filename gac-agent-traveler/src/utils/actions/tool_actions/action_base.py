from abc import <PERSON><PERSON><PERSON>

from _griffe.enumerations import DocstringSec<PERSON>K<PERSON>
from griffe import Docstring

try:
    from typing import Annotated
except ImportError:
    from typing_extensions import Annotated
from typing import Callable, Optional, get_args, get_origin, Dict
from class_registry import AutoRegister, ClassRegistry
# from griffe.enumerations import Docstring<PERSON>ec<PERSON>Kind
from functools import wraps
import re
import inspect

TOOL_REGISTRY = ClassRegistry("__tool_name__", unique=True)


def tool_api(func: Optional[Callable] = None, **kwargs):
    func.is_tool_api = True

    def _detect_type(string):
        field_type = "STRING"
        if "list" in string:
            field_type = "Array"
        elif "str" not in string:
            if "float" in string:
                field_type = "FLOAT"
            elif "int" in string:
                field_type = "NUMBER"
            elif "bool" in string:
                field_type = "BOOLEAN"
        return field_type

    def _parse_tool(function):
        # remove rst syntax
        docs = Docstring(re.sub(":(.+?):`(.+?)`", "\\2", function.__doc__ or "")).parse(
            "google", returns_named_value=False, **kwargs
        )
        desc = dict(
            tool_name=function.__name__,  # Change 'name' to 'tool_name'
            description=docs[0].value
            if docs[0].kind is DocstringSectionKind.text
            else "",
            parameters=[],
            required=[],
        )
        args_doc, returns_doc = {}, []
        for doc in docs:
            if doc.kind is DocstringSectionKind.parameters:
                for d in doc.value:
                    d = d.as_dict()
                    d["type"] = _detect_type(d.pop("annotation").lower())
                    args_doc[d["name"]] = d
            if doc.kind is DocstringSectionKind.returns:
                for d in doc.value:
                    d = d.as_dict()
                    if not d["name"]:
                        d.pop("name")
                    if not d["annotation"]:
                        d.pop("annotation")
                    else:
                        d["type"] = _detect_type(d.pop("annotation").lower())
                    returns_doc.append(d)

        sig = inspect.signature(function)
        for name, param in sig.parameters.items():
            if name == "self":
                continue
            parameter = dict(
                name=param.name,
                type="STRING",
                description=args_doc.get(param.name, {}).get("description", ""),
            )
            annotation = param.annotation
            if annotation is inspect.Signature.empty:
                parameter["type"] = args_doc.get(param.name, {}).get("type", "STRING")
            else:
                if get_origin(annotation) is Annotated:
                    annotation, info = get_args(annotation)
                    if info:
                        parameter["description"] = info
                while get_origin(annotation):
                    annotation = get_args(annotation)
                parameter["type"] = _detect_type(str(annotation))
            desc["parameters"].append(parameter)
            if param.default is inspect.Signature.empty:
                desc["required"].append(param.name)

        return_data = []
        if return_data:
            desc["return_data"] = return_data
        return desc

    if callable(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            return func(self, *args, **kwargs)

        wrapper.api_description = _parse_tool(func)
        return wrapper

    def decorate(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            return func(self, *args, **kwargs)

        wrapper.api_description = _parse_tool(func)
        return wrapper

    return decorate


class ToolMeta(ABCMeta):
    """Metaclass of tools."""

    def __new__(mcs, name, base, attrs):
        tool_desc = {
            "name": attrs.setdefault("__tool_name__", name),
            "description": Docstring(attrs.get("__doc__", "")).parse("google")[0].value,
            "api_list": [],
        }

        for key, value in attrs.items():
            if callable(value) and hasattr(value, "api_description"):
                api_desc = getattr(value, "api_description")
                tool_desc["api_list"].append(api_desc)

        is_toolkit = len(tool_desc["api_list"]) > 1

        if not tool_desc["api_list"]:
            is_toolkit = False

        attrs["_is_toolkit"] = is_toolkit
        attrs["__tool_description__"] = tool_desc

        return super().__new__(mcs, name, base, attrs)


class BaseAction(metaclass=AutoRegister(TOOL_REGISTRY, ToolMeta)):
    def __init__(self):
        self._class_action_name = self.__class__.__name__
        super(BaseAction, self).__init__()

    @property
    def is_toolkit(self):
        return self._is_toolkit

    @property
    def class_action_name(self):
        return self._class_action_name

    def get_action_names(self) -> Dict[str, Callable]:
        return {
            func: getattr(self, func)
            for func in dir(self)
            if callable(getattr(self, func))
               and getattr(getattr(self, func), "is_tool_api", False)
        }
