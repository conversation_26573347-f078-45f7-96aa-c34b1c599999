from abc import ABC, abstractmethod
from typing import Dict, Union
from src.utils.actions.tool_actions.action_base import BaseAction


class WebSearchBase(ABC, BaseAction):
    def __init__(self):
        super().__init__()

    @abstractmethod
    def search(self, query: str, engine: Union[str, None] = None) -> dict:
        """
        Abstract method to perform a search using the specified query and engine.

        This method must be implemented by any subclass of WebSearchBase. It takes a search query and optionally
        a search engine identifier, then returns the search results in the form of a dictionary.

        Args:
            query (str): The search query string that will be used to perform the search.
            engine (Union[str, None], optional): The search engine to use. If not specified, the default engine will be used.

        Returns:
            dict: A dictionary containing the search results.
            e.g. if there is no answer box:
            {'answerBox': {'title': '', 'snippet': '', 'answer': '', 'link': ''},
             1: {'title': '深圳政府在线',
              'snippet': '“深圳政府在线”是由深圳市人民政府办公厅主办、各区人民政府（新区管委会）和市政府直属各单位协办、深圳市电子政务资源中心负责日常技术支持的政府门户网站，是深圳市各级政府机关在国际...',
              'link': 'http://www.sz.gov.cn/'},
             2: {'title': '深圳',
              'snippet': '澎湃，澎湃新闻，澎湃新闻网，新闻与思想，澎湃是植根于中国上海的时政思想类互联网平台，以最活跃的原创新闻与最冷静的思想分析为两翼，是互联网技术创新与新闻价值传承的结合体，致力于问答式...',
              'link': 'https://m.thepaper.cn/tag/93'},
             3: {'title': '深圳新闻网',
              'snippet': '福田 罗湖 南山 盐田 宝安 龙岗 光明 坪山 龙华 大鹏 前海 深汕视点 食药 交委 宝安教育 工务署 工会 市监 公安 公积金 共青团 人才 安监 财政 高层人才 人社 深圳发布 深圳政协 文博会 健康...',
              'link': 'http://m.sznews.com/'},
             4: {'title': '深圳政府在线_深圳市人民政府门户网站',
              'snippet': '01-26 深圳市市场监督管理局关于依法取消有关企业第二类医疗器械经营备案、医疗器械网络销售备案的公告（2023年第19批） 01-26 市文化广电旅游体育局关于深圳市2023年阶段性稳经济运行若...',
              'link': 'http://www.sz.gov.cn/zwgk/zwgk.html'}}

        Raises:
            NotImplementedError: If the method is not implemented in the subclass.

        Example usage (in a subclass):
            def search(self, query: str, engine: Union[str, None] = None) -> dict:
                # Implementation details
                return search_results
        """
        raise NotImplementedError

    @abstractmethod
    def format_search_res(self, search_res: Union[Dict, str]):
        raise NotImplementedError
