import asyncio
import queue
import time
from typing import Any, Coroutine

import aiohttp
from typing_extensions import Optional
from loguru import logger
from configuration import config
from schemas.models import ErrorDataModel, PoiInfoResultModel

get_poi_config = config["get_poi"]
url = get_poi_config["url"]
client_id = get_poi_config["client_id"]

headers = {
    'client-id': client_id,
    'Content-Type': 'application/json'
}


class CityAndName:
    def __init__(self, name: str, city: Optional[str] = None):
        self.city = city
        self.name = name

async def get_all_brief_info(poi_list: list, lat: str, lon: str, interceptor_queue: queue.Queue, message_id: str) -> list:
    logger.info("brief start")
    start_time = time.perf_counter()
    city_and_name_list: list[CityAndName] = []
    for poi in poi_list:
        if isinstance(poi, str):
            if " " in poi:
                words = poi.split(" ", 1)
                city_and_name_list.append(CityAndName(city=words[0], name=words[1]))
            else:
                city_and_name_list.append(CityAndName(name=poi))
    all_brief_info = []
    all_brief_model_info = None
    try:
        tasks = [ asyncio.create_task(get_poi_info(city=item.city, poi_name=item.name, lat=lat, lon=lon)) for item in city_and_name_list]
        all_brief_model_info = await asyncio.gather(*tasks)
    except BaseException as e:
        from src.traveler_exception import error_code_amap_api_return_error
        return_error = ErrorDataModel(error_code=error_code_amap_api_return_error, error_message=f"Request location failed: {str(e)}")
        interceptor_queue.put(return_error)
    if isinstance(all_brief_model_info, list):
        for item in all_brief_model_info:
            if isinstance(item, PoiInfoResultModel):
                if item.result:
                    all_brief_info.append(item.result)
                elif item.error_info:
                    interceptor_queue.put(item.error_info)
    print(f"!!!!!!!!!!!!!!!!!!!!    {all_brief_info}")
    interceptor_queue.put({
        "type" : "brief_info",
        "data" : all_brief_info,
        "message_id" : message_id
    })
    logger.info(f"brief end-----cost time:{round(time.perf_counter() - start_time, 4)}s-----result: {all_brief_info}")
    return all_brief_info

async def get_poi_info(poi_name: str, lat: str, lon: str, city: Optional[str] = None) -> PoiInfoResultModel:
    body = {
        "latitude": lat,
        "longitude": lon,
        "keywords": poi_name,
        "pageSize": 5,
        "page": 1
    }
    if city:
        body["city"] = city
    async with aiohttp.ClientSession() as session:
        async with session.post(url=url, headers=headers, json=body) as response:
            logger.info(
                f"get poi finish: status {response.status}"
            )
            if response.status == 200:
                response_json = await response.json()
                return PoiInfoResultModel(result=await format_search_res(poi_name, response_json), error_info=None)
            else:
                from src.traveler_exception import error_code_amap_api_http_error
                return PoiInfoResultModel(result=None, error_info=ErrorDataModel(error_code=error_code_amap_api_http_error, error_message=f"require poi info fail, status: {response.status}"))



async def format_search_res(poi_name: str, response_json: dict) -> dict:
    target = {}
    dataList = response_json.get("data", {}).get("dataList", [])
    for item in dataList:
        if isinstance(item, dict) and poi_name == item["name"]:
            target = item
            break
    if not target and 0 < len(dataList):
        target = dataList[0]
    return {"poi_name": poi_name, "poi_info": target}
