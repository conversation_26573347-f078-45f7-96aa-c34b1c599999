import re, json, ast
from typing_extensions import Optional
from loguru import logger

keyword_tts_key = "\"tts\""

class ContentHelper:

    def __init__(self):
        self.llm_total_responds = ""
        self.poi_list_str = ""
        self.has_poi_list = False
        self.tts_start = False
        self.tts_end = False
        self.tts_content = ""

    def append_content(self, part: str) -> Optional[str]:
        to_return = None
        if not self.tts_start and not self.tts_end:
            self.llm_total_responds = self.llm_total_responds + part
            current_response = self.llm_total_responds
            if current_response.count(keyword_tts_key):
                after_tts_key = current_response[current_response.find(keyword_tts_key) + len(keyword_tts_key):]
                index_colon = after_tts_key.find(":")
                index_quotation = after_tts_key.find("\"")
                if 0 <= index_colon < index_quotation:
                    self.tts_start = True
                    to_return = after_tts_key[index_quotation + 1: ]
                    self.tts_content = self.tts_content + to_return
        elif self.tts_start and not self.tts_end:
            self.llm_total_responds = self.llm_total_responds + part
            tts_pattern = r'"tts"\s*:\s*"(.*?)"'
            completed_tts_content = None
            try:
                match = re.search(tts_pattern, self.llm_total_responds)
                completed_tts_content = match.group(1)
            except Exception as e:
                pass
            if not completed_tts_content:
                to_return = part
                self.tts_content = self.tts_content + to_return
            else:
                self.tts_start = False
                self.tts_end = True
                if len(completed_tts_content) > len(self.tts_content):
                    to_return = completed_tts_content[len(self.tts_content):]
                    self.tts_content = self.tts_content + to_return
        elif self.tts_end:
            self.llm_total_responds = self.llm_total_responds + part
        return to_return


    def get_poi_list(self) -> Optional[list]:
        list_pattern = r'"view_list"\s*:\s*\[(.*?)\]'
        try:
            match = re.search(list_pattern, self.llm_total_responds)
            self.has_poi_list = True
            self.poi_list_str = match.group(1)
            return [s.strip().strip('"') for s in match.group(1).split(',')]
        except Exception as e:
            pass
        return None

    def poi_detail_dict(self) -> Optional[dict]:
        try:
            total_responds = str(self.llm_total_responds)
            total_responds = total_responds.replace("\n", "")
            # 1. 提取 ```json{...}``` 代码块
            logger.info(f"get poi detail source-total_responds : {total_responds}")
            start_index = total_responds.find("{")
            end_index = total_responds.rfind("}")
            json_block = total_responds[start_index: end_index + 1]
            logger.info(f"get poi detail json: {json_block}")
            result = None
            try:
                result = json.loads(json_block)
            except Exception as e:
                pass
            if not result:
                fixed = re.sub(r'([{,]\s*)([^"\s][^:]*)(\s*:)', r'\1"\2"\3', json_block)
                result = ast.literal_eval(fixed)
            if isinstance(result, dict):
                return result.get("view_info")
        except Exception as e:
            print(e)
        return None


    def get_tts_content_after_end(self) -> str:
        return self.tts_content