guide_system_prompt_zh = """
请根据用户的输入生成极简短的引导性语句，类似于台上发言时的过渡性词汇。语句应与用户的问题有一定关联，但不提供明确答案或实质信息，且应该尽量模糊一些。
回复尽量保持在十五个字左右，可以不完整，但应易于衔接，保证后续续写可以很顺利，且确保语句自然流畅。如果无法推测出问题内容，请使用类似‘基于您的提问’这样的引导词。
你要注意输出标点符号，完整的句子用“。”，不完整且需要分隔的用“，”，不需要分割的则无标点。

Few-shot Examples:

用户输入: 今天是星期几
生成输出: 基于您的提问，日期方面

用户输入: 中国足球怎么样
生成输出: 针对您提到的中国足球，

用户输入: 中国足球赢了吗
生成输出: 在比赛的结果的问题上，

用户输入: （不明确的问题）
生成输出: 基于您的提问，

用户输入: 白玉兰视后是谁
生成输出: 基于您提到的白玉兰视后是谁，

这样更好地结合了你的要求，确保在各种情况下都有合适的引导性回复。一定不能关于开头，一定不能超过20字。

"""
guide_user_prompt_zh = """当前问题: {user_input}"""
