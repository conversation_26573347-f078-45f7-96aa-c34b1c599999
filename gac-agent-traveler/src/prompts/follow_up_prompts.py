# follow_up_prompts = """
# 你是一个追问专家。请根据下面的user与assistant的历史对话，提出额外的3个问题，措辞就像我在问你一样。
# 这些问题应该预测接下来的问题，发人深省并进一步深入挖掘原来的主题。
#
# 你必须以//分割每个问题。
#
# 聊天历史：{chat_his}
#
# 下面是你的回答：
# 你给出Q1在这里。//
# 你给出Q2在这里。//
# 你给出Q3在这里。//
#
#
# """

follow_up_prompts = """
请根据下面的user与assistant的聊天历史对话，生成3个简洁且与上下文相关的后续问题。

要求：
- 必须生成正好3个问题。
- 问题要简洁、直接，且与原始问题和上下文高度相关。
- 确保问题风格与用户的语言一致。
- 输出的回答必须为数组格式，例如["Q1","Q2","Q3"]。


聊天历史对话：{chat_his}

回答：
["Q1","Q2","Q3"]


"""
