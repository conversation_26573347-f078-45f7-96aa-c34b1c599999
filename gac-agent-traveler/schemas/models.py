from dataclasses import dataclass
from typing_extensions import Optional


class ErrorDataModel:
    def __init__(self, error_code: int, error_message: str = "Unknown error"):
        self.error_code = error_code
        self.error_message = error_message


@dataclass
class MemResultModel:
    result: str = ""
    time_cost: float = 0
    error_info: Optional[ErrorDataModel] = None

@dataclass
class LocationResultModel:
    result: str = ""
    time_cost: float = 0
    error_info: Optional[ErrorDataModel] = None

@dataclass
class PreIntentResultModel:
    result: Optional[dict] = None
    error_info: Optional[ErrorDataModel] = None

@dataclass
class RewriteResultModel:
    result: Optional[dict] = None
    error_info: Optional[ErrorDataModel] = None

@dataclass
class MiguMatchApiResultModel:
    result: Optional[str] = None
    error_info: Optional[ErrorDataModel] = None

@dataclass
class EventSummaryResultModel:
    result: Optional[dict] = None
    error_info: Optional[ErrorDataModel] = None

@dataclass
class ConclusionTitleResultModel:
    result: Optional[str] = None
    error_info: Optional[ErrorDataModel] = None

@dataclass
class FollowUpQuestionResultModel:
    result_list: Optional[list] = None
    result_str: Optional[str] = None
    error_info: Optional[ErrorDataModel] = None

@dataclass
class PoiInfoResultModel:
    result: Optional[dict] = None
    error_info: Optional[ErrorDataModel] = None