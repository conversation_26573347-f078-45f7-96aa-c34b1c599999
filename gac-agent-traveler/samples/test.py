import asyncio
import sys
import os

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from fastapi.responses import JSONResponse
from main import (
    web_search_v2,
    web_search,
    pure_web_search,
    sensenova_wrap_api,
    WebSearchReqV2,
    WebSearchReq,
    PureWebSearchReq,
    SenseNovaReq
)
from schemas import UserInDBBase
from datetime import datetime
import json
from pprint import pprint
import sys


def test_web_search():
    payload: WebSearchReq = WebSearchReq(
        query="左航现在读高级了", mode="turbo", engine="bing"
    )
    user = UserInDBBase(email="<EMAIL>", created_at=datetime.now())
    resp: JSONResponse = asyncio.run(web_search(payload, user))
    pprint(json.loads(resp.body))


def test_web_search_v2():
    payload: WebSearchReqV2 = WebSearchReqV2(
        query="北京明天天气", engine="bing"
    )
    user = UserInDBBase(email="<EMAIL>", created_at=datetime.now())
    resp: JSONResponse = asyncio.run(web_search_v2(payload, user))
    pprint(json.loads(resp.body))


def test_pure_web_search():
    payload: PureWebSearchReq = PureWebSearchReq(
        query="左航现在读高级了", engine="bing"
    )
    user = UserInDBBase(email="<EMAIL>", created_at=datetime.now())
    resp: JSONResponse = asyncio.run(pure_web_search(payload, user))
    pprint(json.loads(resp.body))

def test_sensenova_wrap_api():
    payload: SenseNovaReq = SenseNovaReq(
        messages=[{"role": "system", "content": ""}, {"role": "user", "content": "最近特斯拉有新车吗"}]
    )
    user = UserInDBBase(email="<EMAIL>", created_at=datetime.now())
    resp: JSONResponse = asyncio.run(sensenova_wrap_api(payload, user))
    pprint(json.loads(resp.body))


if __name__ == "__main__":
    from loguru import logger

    logger.remove()
    logger.add(sys.stderr, level="INFO")

    # test_web_search()
    # test_web_search_v2()
    test_sensenova_wrap_api()
    # test_pure_web_search()
