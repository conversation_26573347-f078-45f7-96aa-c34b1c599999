#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GAC Agent Traveler 冒烟测试 01
测试服务地址: http://127.0.0.1:8080

测试内容：
1. 基础功能测试
2. 健康检查测试
3. AI旅行家接口测试
4. 无关问题识别测试
5. 流式响应测试
6. 搜索引擎测试
7. 参数边界测试
8. 性能测试（5轮）
9. 错误恢复测试
"""

import requests
import time
import json
import sys
from typing import List, Dict, Any
from statistics import mean, median
time_out_time = 300

class GacAgentTravelerSmokeTest:
    """GAC Agent Traveler 冒烟测试类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.health_url = f"{base_url}/gac-agent-traveler/v1/health"
        self.ai_traveler_url = f"{base_url}/gac-agent-traveler/v1/ai_traveler"
        self.headers = {"Content-Type": "application/json"}
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, message: str, duration: float = 0):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "duration": duration,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {test_name} | {message} | {duration:.2f}s")

    def extract_timing_info(self, response_data: Dict[str, Any]) -> Dict[str, float]:
        """从响应数据中提取时间信息"""
        timing_info = {}

        if isinstance(response_data, dict):
            # 从time_cost字段中提取时间信息
            time_cost = response_data.get("time_cost", {})
            if isinstance(time_cost, dict):
                # 提取各个时间节点（参考AI_Traveler_Test_For_Dify.py）
                time_fields = [
                    "TTFT", "t1-loc", "t2-mem", "t3-pre-intent", "t3-intent",
                    "t4-src", "t5-fetch", "t6-mft", "t7-sft",
                    "t8-list", "t9-brief", "t10-detail", "t-acft", "total_time"
                ]

                for field in time_fields:
                    if field in time_cost:
                        try:
                            timing_info[field] = float(time_cost[field])
                        except (ValueError, TypeError):
                            pass

        return timing_info

    def format_timing_summary(self, timing_info: Dict[str, float]) -> str:
        """格式化时间信息摘要"""
        if not timing_info:
            return ""

        # 时间字段对应的中文名称（参考AI_Traveler_Test_For_Dify.py）
        time_field_names = {
            "Dify-TTFB": "Dify智能体接口首字延迟",
            "TTFT": "首字首字延迟",
            "t1-loc": "高德经纬度转换成城市",
            "t2-mem": "获取用户画像",
            "t3-pre-intent": "前置落域",
            "t3-intent": "意图识别",
            "t4-src": "搜索引擎返回结果",
            "t5-fetch": "爬虫结束",
            "t6-mft": "大模型总结首字延迟",
            "t7-sft": "安全检测首字延迟",
            "t8-list": "生成景点列表的时间",
            "t9-brief": "根据景点列表请求到高德信息的时间",
            "t10-detail": "大模型生成完所有景点的详情介绍的时间",
            "t-acft": "原子能力首字延迟",
            "total_time": "总耗时"
        }
        
        summary_parts = []
        for field, duration in timing_info.items():
            chinese_name = time_field_names.get(field, field)
            summary_parts.append(f"{chinese_name}:{duration:.2f}s")

        return f"[{' '.join(summary_parts)}]" if summary_parts else ""

    def calculate_net_timing(self, timing_info: Dict[str, float]) -> Dict[str, float]:
        """计算净耗时（每个模块的单独耗时，参考AI_Traveler_Test_For_Dify.py）"""
        if not timing_info:
            return {}

        net_timing = {}

        # 直接使用的时间字段（不需要计算差值）
        direct_fields = ["Dify-TTFB", "TTFT", "t1-loc", "t-acft", "total_time"]
        for field in direct_fields:
            if field in timing_info:
                net_timing[field] = timing_info[field]

        # 需要计算差值的时间字段（参考AI_Traveler_Test_For_Dify.py的计算方式）
        try:
            # t2-mem净耗时 = t2-mem - t1-loc
            if "t2-mem" in timing_info and "t1-loc" in timing_info:
                net_timing["t2-mem"] = timing_info["t2-mem"] - timing_info["t1-loc"]

            # t3-pre-intent净耗时 = t3-pre-intent - t2-mem
            if "t3-pre-intent" in timing_info and "t2-mem" in timing_info:
                net_timing["t3-pre-intent"] = timing_info["t3-pre-intent"] - timing_info["t2-mem"]

            # t3-intent净耗时 = t3-intent - t3-pre-intent
            if "t3-intent" in timing_info and "t3-pre-intent" in timing_info:
                net_timing["t3-intent"] = timing_info["t3-intent"] - timing_info["t3-pre-intent"]

            # t4-src净耗时 = t4-src - t3-intent
            if "t4-src" in timing_info and "t3-intent" in timing_info:
                net_timing["t4-src"] = timing_info["t4-src"] - timing_info["t3-intent"]

            # t5-fetch净耗时 = t5-fetch - t4-src
            if "t5-fetch" in timing_info and "t4-src" in timing_info:
                net_timing["t5-fetch"] = timing_info["t5-fetch"] - timing_info["t4-src"]

            # t6-mft净耗时 = t6-mft - t5-fetch
            if "t6-mft" in timing_info and "t5-fetch" in timing_info:
                net_timing["t6-mft"] = timing_info["t6-mft"] - timing_info["t5-fetch"]

            # t7-sft净耗时 = t7-sft - t6-mft
            if "t7-sft" in timing_info and "t6-mft" in timing_info:
                net_timing["t7-sft"] = round(timing_info["t7-sft"] - timing_info["t6-mft"], 6)

            # t8-list净耗时 = t8-list - t6-mft（注意：这里是减去t6-mft，不是t7-sft）
            if "t8-list" in timing_info and "t6-mft" in timing_info:
                net_timing["t8-list"] = timing_info["t8-list"] - timing_info["t6-mft"]

            # t9-brief净耗时 = t9-brief - t8-list
            if "t9-brief" in timing_info and "t8-list" in timing_info:
                net_timing["t9-brief"] = timing_info["t9-brief"] - timing_info["t8-list"]

            # t10-detail净耗时 = t10-detail - t9-brief
            if "t10-detail" in timing_info and "t9-brief" in timing_info:
                net_timing["t10-detail"] = timing_info["t10-detail"] - timing_info["t9-brief"]

        except (KeyError, TypeError, ValueError) as e:
            # 如果计算过程中出现错误，记录但不中断
            print(f"   ⚠️ 计算净耗时时出现错误: {e}")

        return net_timing

    def test_service_connectivity(self) -> bool:
        """测试1: 服务连通性测试"""
        test_name = "服务连通性测试"
        start_time = time.time()
        
        try:
            response = requests.get(self.base_url, timeout=time_out_time)
            duration = time.time() - start_time
            
            # FastAPI默认会返回404，但能连通说明服务正常
            if response.status_code in [200, 404]:
                self.log_test_result(test_name, True, f"服务连通正常 (状态码: {response.status_code})", duration)
                return True
            else:
                self.log_test_result(test_name, False, f"服务响应异常 (状态码: {response.status_code})", duration)
                return False
                
        except requests.exceptions.ConnectionError:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, "无法连接到服务", duration)
            return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"连接异常: {str(e)}", duration)
            return False
    
    def test_health_endpoint(self) -> bool:
        """测试2: 健康检查端点测试"""
        test_name = "健康检查端点测试"
        start_time = time.time()
        
        try:
            response = requests.get(self.health_url, timeout=time_out_time)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    health_data = response.json()
                    if health_data.get("status") == "ok":
                        version = health_data.get("version", "未知")
                        self.log_test_result(test_name, True, f"健康检查通过 (版本: {version})", duration)
                        return True
                    else:
                        self.log_test_result(test_name, False, f"健康状态异常: {health_data}", duration)
                        return False
                except json.JSONDecodeError:
                    self.log_test_result(test_name, False, "健康检查响应格式错误", duration)
                    return False
            else:
                self.log_test_result(test_name, False, f"健康检查失败 (状态码: {response.status_code})", duration)
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"健康检查异常: {str(e)}", duration)
            return False

    def get_standard_payload(self, query: str = "北京景点推荐", stream: bool = False) -> Dict[str, Any]:
        """获取标准请求体（基于curl.sh格式）"""
        return {
            "detect": False,
            "engine": "tencent",
            "location": {
                "lat": "31.16813",
                "lon": "121.3999"
            },
            "query": query,
            "stream": stream,
            "surrounding": "当前车内有2人，具体为：在二排左位置的儿童车内成员、在主驾位置的未知年龄的车内成员。",
            "use_search_cache": False,
            "user_info": {
                "car_id": "demoCar82952",
                "category": [
                    "natural_landscape_preference",
                    "human_landscape_preference",
                    "entertainment_landscape_preference",
                    "travel_activity"
                ],
                "user_id": "1"
            }
        }

    def test_ai_traveler_basic_request(self) -> bool:
        """测试3: AI旅行家基础请求测试"""
        test_name = "AI旅行家基础请求测试"
        start_time = time.time()

        payload = self.get_standard_payload("上海有什么好玩的地方？")
        
        try:
            response = requests.post(
                self.ai_traveler_url,
                headers=self.headers,
                json=payload,
                timeout=time_out_time
            )
            duration = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    # 检查响应格式是否正确，有time_cost字段说明服务正常处理了请求
                    if isinstance(result, dict) and "time_cost" in result:
                        timing_info = self.extract_timing_info(result)
                        answer_length = len(result.get("answer", ""))
                        total_time = result.get("time_cost", {}).get("total_time", 0)

                        # 如果有答案内容，说明请求成功
                        if result.get("answer"):
                            self.log_test_result(test_name, True, f"AI旅行家请求成功 (答案长度: {answer_length}, 耗时: {total_time}s)", duration)
                            return True
                        else:
                            # 即使没有答案，但服务正常响应也算基本通过
                            self.log_test_result(test_name, True, f"AI旅行家服务正常 (无答案内容，可能是引擎配置问题, 耗时: {total_time}s)", duration)
                            return True
                    else:
                        self.log_test_result(test_name, False, f"AI旅行家响应格式异常: {result}", duration)
                        return False
                except json.JSONDecodeError:
                    self.log_test_result(test_name, False, "AI旅行家响应JSON格式错误", duration)
                    return False
            else:
                error_msg = response.text[:200] if response.text else "无错误信息"
                self.log_test_result(test_name, False, f"AI旅行家请求失败 (状态码: {response.status_code}) - {error_msg}", duration)
                return False
                
        except requests.exceptions.Timeout:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, "AI旅行家请求超时", duration)
            return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"AI旅行家请求异常: {str(e)}", duration)
            return False

    def test_irrelevant_query_detection(self) -> bool:
        """测试4: 无关问题识别测试"""
        test_name = "无关问题识别测试"
        start_time = time.time()

        payload = self.get_standard_payload("你好啊", stream=False)

        try:
            response = requests.post(
                self.ai_traveler_url,
                headers=self.headers,
                json=payload,
                timeout=time_out_time
            )
            duration = time.time() - start_time

            if response.status_code == 200:
                try:
                    result = response.json()
                    if isinstance(result, dict):
                        # 检查返回结果是否识别为无关问题
                        answer = result.get("answer", "")

                        # 如果答案为空或包含无关问题的提示，说明识别正确
                        if not answer or "无关" in answer or "不相关" in answer or "旅行" not in answer or "景点" not in answer:
                            self.log_test_result(test_name, True, "成功识别无关问题", duration)
                            return True
                        else:
                            # 如果返回了旅行内容，可能是误判，但也可能是合理的处理
                            self.log_test_result(test_name, True, f"处理无关问题 (返回内容长度: {len(answer)})", duration)
                            return True
                    else:
                        self.log_test_result(test_name, False, f"响应格式异常: {type(result)}", duration)
                        return False
                except json.JSONDecodeError:
                    self.log_test_result(test_name, False, "响应JSON格式错误", duration)
                    return False
            else:
                error_msg = response.text[:200] if response.text else "无错误信息"
                self.log_test_result(test_name, False, f"请求失败 (状态码: {response.status_code}) - {error_msg}", duration)
                return False

        except requests.exceptions.Timeout:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, "请求超时", duration)
            return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"请求异常: {str(e)}", duration)
            return False

    def test_stream_response(self) -> bool:
        """测试4: 流式响应测试"""
        test_name = "流式响应测试"
        start_time = time.time()

        payload = self.get_standard_payload("深圳有什么特色美食？", stream=True)

        try:
            response = requests.post(
                self.ai_traveler_url,
                headers=self.headers,
                json=payload,
                stream=True,
                timeout=time_out_time
            )

            if response.status_code == 200:
                chunks_received = 0

                # 读取流式数据
                for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                    if chunk:
                        chunks_received += 1
                        # 限制读取时间，避免无限等待
                        if time.time() - start_time > 45:
                            break

                duration = time.time() - start_time

                if chunks_received > 0:
                    self.log_test_result(test_name, True, f"流式响应正常 (接收{chunks_received}个数据块)", duration)
                    return True
                else:
                    self.log_test_result(test_name, False, "未接收到流式数据", duration)
                    return False
            else:
                duration = time.time() - start_time
                self.log_test_result(test_name, False, f"流式请求失败 (状态码: {response.status_code})", duration)
                return False

        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"流式响应异常: {str(e)}", duration)
            return False

    def test_different_engines(self) -> bool:
        """测试5: 不同搜索引擎测试"""
        test_name = "不同搜索引擎测试"
        start_time = time.time()

        engines = ["tencent", "bing", "serper"]
        successful_engines = []

        for engine in engines:
            try:
                payload = self.get_standard_payload("杭州西湖怎么样？")
                payload["engine"] = engine

                response = requests.post(
                    self.ai_traveler_url,
                    headers=self.headers,
                    json=payload,
                    timeout=time_out_time
                )

                if response.status_code == 200:
                    successful_engines.append(engine)
                    print(f"   ✓ 引擎 {engine}: 成功")
                else:
                    print(f"   ✗ 引擎 {engine}: 失败 (状态码: {response.status_code})")

            except Exception as e:
                print(f"   ✗ 引擎 {engine}: 异常 - {str(e)}")

        duration = time.time() - start_time

        if successful_engines:
            self.log_test_result(test_name, True, f"成功的引擎: {', '.join(successful_engines)}", duration)
            return True
        else:
            self.log_test_result(test_name, False, "所有搜索引擎都失败", duration)
            return False

    def test_parameter_boundaries(self) -> bool:
        """测试6: 参数边界值测试"""
        test_name = "参数边界值测试"
        start_time = time.time()

        test_cases = [
            {"query": "北京游玩", "desc": "最短查询"},
            {"query": "我想去北京玩请问哪里好玩，最好是休闲娱乐的地方，有什么推荐的景点和美食吗？", "desc": "长查询"},
            {"use_search_cache": False, "desc": "不使用搜索缓存"},
            {"use_search_cache": True, "desc": "使用搜索缓存"}
        ]

        successful_cases = 0

        for case in test_cases:
            try:
                payload = self.get_standard_payload("成都有什么好玩的？")
                payload.update({k: v for k, v in case.items() if k != "desc"})

                response = requests.post(
                    self.ai_traveler_url,
                    headers=self.headers,
                    json=payload,
                    timeout=time_out_time
                )

                if response.status_code == 200:
                    successful_cases += 1
                    print(f"   ✓ {case['desc']}: 成功")
                else:
                    print(f"   ✗ {case['desc']}: 失败 (状态码: {response.status_code})")

            except Exception as e:
                print(f"   ✗ {case['desc']}: 异常 - {str(e)}")

        duration = time.time() - start_time

        if successful_cases >= len(test_cases) // 2:
            self.log_test_result(test_name, True, f"边界测试通过 ({successful_cases}/{len(test_cases)})", duration)
            return True
        else:
            self.log_test_result(test_name, False, f"边界测试失败 ({successful_cases}/{len(test_cases)})", duration)
            return False

    def test_response_time_performance(self) -> bool:
        """测试7: 响应时间性能测试（5轮）"""
        test_name = "响应时间性能测试"
        start_time = time.time()

        # 多样化的旅行查询
        test_queries = [
            "成都有什么特色景点？",
            "上海有什么好吃的？",
            "北京有哪些著名景点？",
            "深圳购物中心推荐",
            "杭州周边有什么好玩的？"
        ]

        response_times = []
        successful_requests = 0
        test_rounds = 5
        all_timing_data = []

        print(f"   执行 {test_rounds} 轮性能测试...")

        for i in range(test_rounds):
            query = test_queries[i % len(test_queries)]
            payload = self.get_standard_payload(query)

            try:
                req_start = time.time()
                response = requests.post(
                    self.ai_traveler_url,
                    headers=self.headers,
                    json=payload,
                    timeout=time_out_time
                )
                req_duration = time.time() - req_start

                if response.status_code == 200:
                    response_times.append(req_duration)
                    successful_requests += 1

                    # 解析响应中的时间信息
                    try:
                        result = response.json()
                        timing_info = self.extract_timing_info(result)
                        # 计算净耗时（每个模块的单独耗时）
                        net_timing_info = self.calculate_net_timing(timing_info)
                        all_timing_data.append(net_timing_info)
                        answer_length = len(result.get("answer", ""))
                        timing_summary = self.format_timing_summary(net_timing_info)
                        print(f"   第{i+1}轮: {req_duration:.2f}s (答案长度: {answer_length}) {timing_summary}")
                    except Exception as e:
                        print(f"   第{i+1}轮: {req_duration:.2f}s (响应解析失败: {str(e)})")
                else:
                    print(f"   第{i+1}轮: 失败 (状态码: {response.status_code})")

            except Exception as e:
                print(f"   第{i+1}轮: 异常 - {str(e)}")

        duration = time.time() - start_time

        # 生成性能统计报告
        if response_times and all_timing_data:
            avg_time = mean(response_times)
            median_time = median(response_times)
            max_time = max(response_times)
            min_time = min(response_times)
            success_rate = successful_requests / test_rounds

            print(f"\n   📊 性能测试统计报告:")
            print(f"   ================================")
            print(f"   总体响应时间:")
            print(f"     平均: {avg_time:.2f}s, 中位数: {median_time:.2f}s")
            print(f"     最快: {min_time:.2f}s, 最慢: {max_time:.2f}s")
            print(f"     成功率: {success_rate:.1%}")

            # 计算各模块的平均耗时（参考AI_Traveler_Test_For_Dify.py）
            time_field_names = {
                "Dify-TTFB": "Dify智能体接口首字延迟",
                "TTFT": "首字首字延迟",
                "t1-loc": "高德经纬度转换成城市",
                "t2-mem": "获取用户画像",
                "t3-pre-intent": "前置落域",
                "t3-intent": "意图识别",
                "t4-src": "搜索引擎返回结果",
                "t5-fetch": "爬虫结束",
                "t6-mft": "大模型总结首字延迟",
                "t7-sft": "安全检测首字延迟",
                "t8-list": "生成景点列表的时间",
                "t9-brief": "根据景点列表请求到高德信息的时间",
                "t10-detail": "大模型生成完所有景点的详情介绍的时间",
                "t-acft": "原子能力首字延迟",
                "total_time": "总耗时"
            }

            module_stats = {}
            for net_timing_info in all_timing_data:
                for field, duration_val in net_timing_info.items():
                    if field not in module_stats:
                        module_stats[field] = []
                    if duration_val > 0:
                        module_stats[field].append(duration_val)

            print(f"\n   各模块净耗时统计信息（单独耗时，非累加）:")
            sorted_modules = sorted(module_stats.items(),
                                  key=lambda x: mean(x[1]) if x[1] else 0, reverse=True)

            for field, durations in sorted_modules:
                if durations:
                    avg_duration = mean(durations)
                    min_duration = min(durations)
                    max_duration = max(durations)
                    median_duration = median(durations)
                    chinese_name = time_field_names.get(field, field)
                    # 计算字符串的实际显示宽度，中文字符宽度为2，英文字符宽度为1
                    display_width = sum(2 if ord(c) > 127 else 1 for c in chinese_name)
                    padding = max(0, 20 - display_width)
                    print(f"     {chinese_name}{' ' * padding}: "
                          f"平均 {avg_duration:6.2f}s, "
                          f"中位数 {median_duration:6.2f}s, "
                          f"最快 {min_duration:6.2f}s, "
                          f"最慢 {max_duration:6.2f}s "
                          )

            # 性能标准：平均响应时间 < 30秒，成功率 > 60%
            performance_ok = avg_time < time_out_time and success_rate > 0.6

            message = f"平均:{avg_time:.2f}s 中位数:{median_time:.2f}s 最大:{max_time:.2f}s 最小:{min_time:.2f}s 成功率:{success_rate:.1%}"
            self.log_test_result(test_name, performance_ok, message, duration)
            return performance_ok
        else:
            self.log_test_result(test_name, False, "所有请求都失败", duration)
            return False

    def test_error_recovery(self) -> bool:
        """测试8: 错误恢复能力测试"""
        test_name = "错误恢复能力测试"
        start_time = time.time()

        # 先发送一个错误请求
        invalid_payload = {
            "query": "",  # 空查询
            "invalid_field": "test"
        }

        try:
            # 发送无效请求
            response = requests.post(
                self.ai_traveler_url,
                headers=self.headers,
                json=invalid_payload,
                timeout=time_out_time
            )
            print(f"   无效请求响应: {response.status_code}")
        except Exception as e:
            print(f"   无效请求异常: {str(e)}")

        # 短暂等待
        time.sleep(1)

        # 然后发送正常请求，测试服务是否能恢复
        valid_payload = self.get_standard_payload("天津有什么特色小吃？")

        recovery_successful = False

        try:
            response = requests.post(
                self.ai_traveler_url,
                headers=self.headers,
                json=valid_payload,
                timeout=time_out_time
            )

            if response.status_code == 200:
                try:
                    result = response.json()
                    if ("answer" in result and result["answer"]) or result.get("code") == 0:
                        recovery_successful = True
                        print("   服务成功从错误中恢复")
                    else:
                        print(f"   恢复失败，服务返回错误: {result.get('code', '未知错误')}")
                except json.JSONDecodeError:
                    print("   恢复失败，响应格式错误")
            else:
                print(f"   恢复失败，状态码: {response.status_code}")

        except Exception as e:
            print(f"   恢复测试异常: {str(e)}")

        duration = time.time() - start_time

        message = "服务能够从错误中恢复" if recovery_successful else "服务无法从错误中恢复"
        self.log_test_result(test_name, recovery_successful, message, duration)
        return recovery_successful

    def run_smoke_tests(self) -> bool:
        """运行所有冒烟测试"""
        print("=" * 80)
        print("🧪 GAC Agent Traveler 冒烟测试 01")
        print(f"🎯 测试目标: {self.base_url}")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

        # 执行测试序列
        tests = [
            self.test_service_connectivity,
            self.test_health_endpoint,
            self.test_ai_traveler_basic_request,
            self.test_irrelevant_query_detection,
            self.test_stream_response,
            self.test_different_engines,
            self.test_parameter_boundaries,
            self.test_response_time_performance,
            self.test_error_recovery
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_func in tests:
            if test_func():
                passed_tests += 1
            print("-" * 80)

        # 输出测试总结
        success_rate = (passed_tests / total_tests) * 100
        overall_success = passed_tests == total_tests

        print("📊 测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   成功率: {success_rate:.1f}%")

        if overall_success:
            print("🎉 冒烟测试全部通过！服务功能正常")
        elif passed_tests >= total_tests * 0.75:
            print("✅ 大部分冒烟测试通过，服务基本正常")
        else:
            print("⚠️  多项冒烟测试失败，请检查服务状态")

        print("=" * 80)
        return overall_success


def main():
    """主函数"""
    # 可以通过命令行参数指定服务地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"

    smoke_test = GacAgentTravelerSmokeTest(base_url)
    success = smoke_test.run_smoke_tests()

    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
