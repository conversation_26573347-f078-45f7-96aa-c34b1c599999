# GAC Agent Traveler 冒烟测试

本目录包含 GAC Agent Traveler 服务的冒烟测试脚本，用于验证服务的基本功能和性能。

## 文件说明

### 测试脚本

- **`smoke_test_01.py`** - 主要的冒烟测试脚本（编号01）
  - 使用 `curl.sh` 中的标准请求体格式
  - 参考 `online_search_framework` 的性能输出格式
  - 包含9个测试用例：连通性、健康检查、基础请求、无关问题识别、流式响应、搜索引擎、参数边界、性能测试、错误恢复

- **`ci_smoke_test.sh`** - CI环境专用的冒烟测试脚本
  - 自动等待服务启动
  - 包含服务健康检查
  - 提供详细的诊断信息

- **`test_smoke_test_02.py`** - 验证脚本（编号02）
  - 用于验证冒烟测试脚本的正确性
  - 检查语法、格式和配置

### 配置文件

- **`curl.sh`** - 标准请求示例
- **`smoke_test_combined.py`** - 原有的冒烟测试（保留）

## 使用方法

### 本地运行

1. 启动 GAC Agent Traveler 服务：
```bash
cd gac-agent-traveler
python3 main.py
```

2. 在另一个终端运行冒烟测试：
```bash
cd gac-agent-traveler/test
python3 smoke_test_01.py
```

3. 指定服务地址（可选）：
```bash
python3 smoke_test_01.py http://localhost:8080
```

### CI环境运行

CI环境会自动运行冒烟测试，配置在 `.gitlab-ci.yml` 中：

```yaml
smoke_test_job:
  stage: smoke_test
  script:
    - cd test
    - chmod +x ci_smoke_test.sh
    - ./ci_smoke_test.sh
```

## 测试内容

### 1. 服务连通性测试
- 验证服务是否可以访问
- 检查基本的HTTP响应

### 2. 健康检查端点测试
- 测试 `/gac-agent-traveler/v1/health` 端点
- 验证服务状态和版本信息

### 3. AI旅行家基础请求测试
- 使用标准请求体格式
- 验证 `/gac-agent-traveler/v1/ai_traveler` 端点
- 检查响应格式和内容

### 4. 无关问题识别测试
- 发送无关问题（如"你好啊"）
- 验证系统能否正确识别非旅行相关问题
- 检查返回结果是否合理处理无关问题

### 5. 流式响应测试
- 测试流式输出功能
- 验证数据块接收

### 5. 不同搜索引擎测试
- 测试 tencent、bing、serper 引擎
- 验证引擎切换功能

### 6. 参数边界值测试
- 测试不同查询长度
- 验证各种参数组合

### 7. 性能测试（5轮）
- 执行5轮性能测试
- 提供详细的性能统计报告
- 包含各模块耗时分析

### 8. 错误恢复能力测试
- 发送错误请求后测试服务恢复
- 验证服务的稳定性

## 性能输出格式

测试会输出详细的性能分析，包括：

```
📊 性能测试统计报告:
================================
总体响应时间:
  平均: 12.34s, 中位数: 11.89s
  最快: 9.87s, 最慢: 15.67s
  成功率: 100.0%

各模块统计信息:
  位置解析        : 平均   0.12s, 中位数   0.11s, 最快   0.10s, 最慢   0.15s
  用户画像        : 平均   0.23s, 中位数   0.22s, 最快   0.20s, 最慢   0.28s
  意图识别        : 平均   1.45s, 中位数   1.40s, 最快   1.20s, 最慢   1.80s
  搜索执行        : 平均   3.21s, 中位数   3.15s, 最快   2.80s, 最慢   3.90s
  内容爬取        : 平均   2.67s, 中位数   2.60s, 最快   2.30s, 最慢   3.20s
  内容总结        : 平均   4.12s, 中位数   4.05s, 最快   3.70s, 最慢   4.80s
  结构化处理      : 平均   0.89s, 中位数   0.85s, 最快   0.75s, 最慢   1.10s
  首字延迟        : 平均   0.56s, 中位数   0.54s, 最快   0.45s, 最慢   0.70s
```

## 成功标准

- **连通性测试**: 服务可访问（状态码200或404）
- **健康检查**: 返回 `{"status": "ok"}`
- **基础功能**: 正常返回响应且包含 `time_cost` 字段
- **流式响应**: 能接收到数据块
- **搜索引擎**: 至少一个引擎工作正常
- **参数边界**: 至少50%的测试用例通过
- **性能测试**: 平均响应时间 < 30秒，成功率 > 60%
- **错误恢复**: 服务能从错误请求中恢复

## 故障排除

### 常见问题

1. **连接失败**
   - 检查服务是否启动
   - 确认端口8080是否被占用
   - 检查防火墙设置

2. **超时错误**
   - 增加超时时间
   - 检查网络连接
   - 查看服务日志

3. **性能测试失败**
   - 检查系统资源使用情况
   - 确认依赖服务（如Redis、搜索引擎）是否正常
   - 查看详细的错误日志

### 日志查看

CI环境中的日志位置：
- 服务日志: `service.log`
- 测试结果: `test/smoke_test_results.log`

## 更新历史

- **2024-01-XX**: 创建 smoke_test_01.py，使用标准请求格式和改进的性能输出
- **2024-01-XX**: 添加 ci_smoke_test.sh CI环境支持
- **2024-01-XX**: 更新 .gitlab-ci.yml 添加冒烟测试阶段
