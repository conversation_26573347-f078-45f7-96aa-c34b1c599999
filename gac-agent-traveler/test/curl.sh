curl --location 'http://127.0.0.1:8080/gac-agent-traveler/v1/ai_traveler' \
--header 'Content-Type: application/json' \
--data '{
    "detect": false,
    "engine": "tencent",
    "location": {
      "lat": "31.16813",
      "lon": "121.3999"
    },
    "query": "北京景点推荐",
    "stream": false,
    "surrounding": "当前车内有2人，具体为：在二排左位置的儿童车内成员、在主驾位置的未知年龄的车内成员。",
    "use_search_cache": false,
    "user_info": {
      "car_id": "demoCar82952",
      "category": [
        "natural_landscape_preference",
        "human_landscape_preference",
        "entertainment_landscape_preference",
        "travel_activity"
      ],
      "user_id": "1"
    }
}'