# Default values for license.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
application:
  name: "cabin-agent-traveler-service"
  version:
  sensitiveVariable:
    - name: SENSEAUTO_KEY
      valueFrom:
        secretKeyRef:
          name: gac-agent-traveler-service-secret
          key: SENSEAUTO_KEY
          optional: false
    - name: BING_KEY
      valueFrom:
        secretKeyRef:
          name: gac-agent-traveler-service-secret
          key: BING_KEY
          optional: false
    - name: TENCENT_AK
      valueFrom:
        secretKeyRef:
          name: gac-agent-traveler-service-secret
          key: TENCENT_AK
          optional: false
    - name: TENCENT_SK
      valueFrom:
        secretKeyRef:
          name: gac-agent-traveler-service-secret
          key: TENCENT_SK
          optional: false
    - name: GAODE_API_KEY
      valueFrom:
        secretKeyRef:
          name: gac-agent-traveler-service-secret
          key: GAODE_API_KEY
          optional: false

  variables:
    - name: ACTIVE_ENV
      value: sit
    - name: SELECTED_MODEL
      value: senseauto
    - name: REDIS_HOST
      value: auto-cloud-redis-cluster-sit.9uzhp7.clustercfg.cnw1.cache.amazonaws.com.cn
    - name: SENSEAUTO_URL
      value: http://************:8099/v1
    - name: RERANK_URL
      value: https://**************:8077/rerank
    - name: REWRITE_URL
      value: https://**************:8090/v1
replicaCount: 1

image:
  repository: ************.dkr.ecr.cn-northwest-1.amazonaws.com.cn/cabin-agent-traveler-service
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag:

nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 8080

podProbe:
  startUp:
    disabled: true
    path: /gac-agent-traveler/v1/health
    port: 8080
    failureThreshold: 60
    periodSeconds: 10
    timeoutSeconds: 10
  readiness:
    disabled: true
    path: /gac-agent-traveler/v1/health
    port: 8080
    initialDelaySeconds: 10
    periodSeconds: 10
    failureThreshold: 2
    timeoutSeconds: 10
  liveness:
    disabled: true
    path: /gac-agent-traveler/v1/health
    port: 8080
    initialDelaySeconds: 10
    periodSeconds: 10
    failureThreshold: 2
    timeoutSeconds: 10

ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 400m
    memory: 1024Mi
  requests:
    cpu: 200m
    memory: 512Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations:
  - key: "project"
    operator: "Equal"
    value: "autocloud"
    effect: "NoExecute"
affinity: {}
