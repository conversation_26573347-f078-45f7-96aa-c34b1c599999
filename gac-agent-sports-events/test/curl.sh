#!/bin/bash

# 设置变量
API_URL="http://127.0.0.1:8080/gac/sports-events/v1/chat"  # 替换为实际的API地址
QUERY="姚明最近怎么样"               # 可替换为实际查询内容

# 构建 JSON payload
PAYLOAD='{
    "detect": false,
    "engine": "tencent",
    "location": {
      "lat": "31.16813",
      "lon": "121.39987"
    },
    "messages": [
      {
        "content": "'"$QUERY"'",
        "role": "user"
      }
    ],
    "stream": false,
    "use_search_cache": false,
    "user_info": {
      "car_id": "aaaa",
      "category": [
        "sport_team",
        "athlete",
        "sport_type"
      ],
      "user_id": "abc-1246"
    }
  }'
# 使用 curl 发送请求
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -d "$PAYLOAD"