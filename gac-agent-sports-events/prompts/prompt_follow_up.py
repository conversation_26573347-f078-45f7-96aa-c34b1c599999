follow_up_prompts = """
请根据下面的user与assistant的聊天历史对话，生成3个简洁且与上下文相关的后续问题。

要求：
- 必须生成正好3个问题。
- 第一个问题必须引导用户查看相关比赛视频，或去观看相关比赛。
- 问题要简洁、直接，且与原始问题和上下文高度相关。
- 确保问题风格与用户的语言一致。
- 输出的回答必须为数组格式，例如["Q1","Q2","Q3"]。


聊天历史对话：{chat_his}

回答：
["Q1","Q2","Q3"]


"""


follow_up_prompts_event = """
请根据下面的user与assistant的聊天历史对话，生成3个简洁且与上下文相关的后续问题。

要求：
- 必须生成正好3个问题。
- 问题要简洁、直接，且与原始问题和上下文高度相关。
- 确保问题风格与用户的语言一致。
- 输出的回答必须为数组格式，例如["Q1","Q2","Q3"]。


聊天历史对话：{chat_his}

回答：
["Q1","Q2","Q3"]


"""