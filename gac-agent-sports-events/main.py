import uvicorn
from fastapi import FastAPI
import configuration

global_config = configuration.config
chat_config = global_config['sport_chat']
llm_config = global_config['llm_server']
current_version = global_config['settings']['version']
app = FastAPI(title="gac_agent_sports_svc")


if __name__ == "__main__":
    from api.sports_events_router import sports_events_router
    app.include_router(sports_events_router)
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=global_config["settings"]["port"],
        workers=1,
        use_colors=True,
    )