from bad_case_base_function import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    code_middle_fail,
    code_complete,
    ExcelResultWriter,
)
from typing_extensions import Optional
from dataclasses import dataclass
import os
import aiohttp
import time
import asyncio
import json
import logging
import colorlog

# 本脚本专用日志配置（不依赖项目其他部分）


def get_logger(level=logging.INFO):
    # 创建logger对象
    logger = logging.getLogger()
    logger.setLevel(level)
    # 创建控制台日志处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    # 定义颜色输出格式
    color_formatter = colorlog.ColoredFormatter(
        "%(log_color)s%(levelname)s: %(message)s",
        log_colors={
            "DEBUG": "cyan",
            "INFO": "green",
            "WARNING": "yellow",
            "ERROR": "red",
            "CRITICAL": "red,bg_white",
        },
    )
    # 将颜色输出格式添加到控制台日志处理器
    console_handler.setF<PERSON>atter(color_formatter)
    # 移除默认的handler
    for handler in logger.handlers:
        logger.removeHandler(handler)
    # 将控制台日志处理器添加到logger对象
    logger.addHandler(console_handler)
    return logger


logger = get_logger(logging.DEBUG)

file_path_in = os.path.dirname(os.path.abspath(__file__)) + "/" + "GACCLM-缺陷.xlsx"

# 使用可读的时间格式作为文件名后缀，例如 2025-09-04_16-30-00
human_read_time = time.strftime("%Y-%m-%d_%H-%M-%S", time.localtime())
file_path_out = (
    os.path.dirname(os.path.abspath(__file__))
    + "/"
    + f"Bad_Case_Result_Ai_Sport_{human_read_time}.xlsx"
)

excelHelper = ExcelHelper(file_path=file_path_in)
judgeHelper = JudgeHelper()

def extract_types_from_response(response_text):
    types_of_interest = {'event_detail_reply', 'phase_match_source', 'follow_up'}
    results = {t: [] for t in types_of_interest}
    for line in response_text.splitlines():
        line = line.strip()
        if not line.startswith("data:"):
            continue
        data_str = line[len("data:"):].strip()
        try:
            obj = json.loads(data_str)
        except Exception:
            continue
        t = obj.get("type")
        if t in types_of_interest:
            results[t].append(obj)
    return results


def extract_phase_match_source_scores(response_text: str) -> str:
    """从流式文本中抽取第一场可用的比赛比分，返回格式化字符串。

    优先格式: "主队 分数 - 分数 客队"；若缺失分数但有队伍，则 "主队 vs 客队"。
    若完全找不到，返回空字符串 ""。
    """
    if not response_text:
        return ""
    phase_obj = None
    for line in response_text.splitlines():
        line = line.strip()
        if not line.startswith("data:"):
            continue
        data_str = line[len("data:"):].strip()
        try:
            obj = json.loads(data_str)
        except Exception:
            continue
        if isinstance(obj, dict) and obj.get("type") == "phase_match_source":
            phase_obj = obj
            break
    if not phase_obj:
        return ""
    data_section = phase_obj.get("data") or {}
    matches = data_section.get("matches") or []
    score_array = data_section.get("score") or []
    # 建立 match 映射
    match_map = {}
    for m in matches:
        pid = m.get("pendantId") or m.get("pendant_id")
        if pid:
            match_map[str(pid)] = m
    # 遍历 score，生成第一个可读比分
    for s in score_array:
        pid = s.get("pendantId") or s.get("pendant_id")
        if not pid:
            continue
        match_meta = match_map.get(str(pid), {})
        score_info = s.get("scoreInfo") or []
        homeTeam = awayTeam = None
        homeScore = awayScore = None
        for item in score_info:
            homed = item.get("homed")
            team_name = item.get("teamName") or item.get("team")
            raw_score = item.get("score")
            try:
                score_int = int(raw_score) if raw_score is not None else None
            except Exception:
                score_int = None
            if homed == 1:
                homeTeam, homeScore = team_name, score_int
            elif homed == 2:
                awayTeam, awayScore = team_name, score_int
        # 回退: 如果未识别 homed 但有两个记录
        if (homeTeam is None or awayTeam is None) and len(score_info) == 2:
            first, second = score_info[0], score_info[1]
            if homeTeam is None:
                homeTeam = first.get("teamName")
                try:
                    homeScore = int(first.get("score")) if first.get("score") is not None else None
                except Exception:
                    homeScore = None
            if awayTeam is None:
                awayTeam = second.get("teamName")
                try:
                    awayScore = int(second.get("score")) if second.get("score") is not None else None
                except Exception:
                    awayScore = None
        if homeTeam and awayTeam:
            if homeScore is not None and awayScore is not None:
                return f"{homeTeam} {homeScore} - {awayScore} {awayTeam}"
            return f"{homeTeam} vs {awayTeam}"
    return ""


def extract_event_detail_reply_messages(response_text: str):
    """提取所有 event_detail_reply 的 message 字段，返回列表。

    若行格式不合法或没有 message 字段则忽略。"""
    if not response_text:
        return []
    messages = []
    for line in response_text.splitlines():
        line = line.strip()
        if not line.startswith("data:"):
            continue
        data_str = line[len("data:"):].strip()
        try:
            obj = json.loads(data_str)
        except Exception:
            continue
        if isinstance(obj, dict) and obj.get("type") == "event_detail_reply":
            data_part = obj.get("data") or {}
            msg = data_part.get("message") or data_part.get("msg")
            if isinstance(msg, str) and msg.strip():
                messages.append(msg.strip())
    return messages


def extract_follow_up_items(response_text: str):
    """提取所有 follow_up 的 data 数组与 conclusion_title。

    返回: {
        'items': [ ['问题1','问题2',...], ... ],  # 每个 follow_up 的 data 列表
        'conclusion_titles': ['标题1', '标题2', ...]
    }
    若没有则返回空结构。
    """
    result = {"items": [], "conclusion_titles": []}
    if not response_text:
        return result
    for line in response_text.splitlines():
        line = line.strip()
        if not line.startswith("data:"):
            continue
        data_str = line[len("data:"):].strip()
        try:
            obj = json.loads(data_str)
        except Exception:
            continue
        if isinstance(obj, dict) and obj.get("type") == "follow_up":
            data_part = obj.get("data")
            if isinstance(data_part, list) and data_part:
                # 仅保留 str 项
                filtered = [str(x) for x in data_part if isinstance(x, (str, int, float))]
                if filtered:
                    result["items"].append(filtered)
            title = obj.get("conclusion_title")
            if isinstance(title, str) and title.strip():
                result["conclusion_titles"].append(title.strip())
    return result


def extract_event_detail_reply_jump_urls(response_text: str):
    """提取 event_detail_reply 中的 jump_url 列表。

    若不存在返回空列表。"""
    if not response_text:
        return []
    urls = []
    for line in response_text.splitlines():
        line = line.strip()
        if not line.startswith("data:"):
            continue
        data_str = line[len("data:"):].strip()
        try:
            obj = json.loads(data_str)
        except Exception:
            continue
        if isinstance(obj, dict) and obj.get("type") == "event_detail_reply":
            data_part = obj.get("data") or {}
            jump_url = data_part.get("jump_url") or data_part.get("jumpUrl")
            if isinstance(jump_url, str) and jump_url.strip():
                urls.append(jump_url.strip())
    return urls


@dataclass
class Result:
    code: int = code_middle_fail
    query: str = ""
    result_text: str = ""
    poi_list: str = ""


class BadCaseRunner:
    @staticmethod
    def _extract_judge_fields(judge_text: str) -> tuple[str, str]:
        """从 LLM 判定文本中提取 result 与 reason。

        期望格式：
            {
                "result": <判断结果>,
                "reason": <判断依据>
            }

        兼容以下情况：
        - 前后有说明性文本或 Markdown 代码块
        - JSON 内部换行、空格
        - 返回为空或非 JSON 时，返回空字符串
        """
        if not judge_text:
            return "", ""
        s = str(judge_text).strip()
        # 去除常见的 Markdown 代码块包裹
        if s.startswith("```"):
            # ```json\n{...}\n```
            try:
                fence_end = s.rfind("```")
                if fence_end > 0:
                    inner = s.split("\n", 1)[1][
                        : fence_end - (len(s.split("\n", 1)[0]) + 1)
                    ]
                    s = inner.strip()
            except Exception:
                pass

        # 直接尝试整体解析
        def try_parse_obj(text: str):
            try:
                obj = json.loads(text)
                if isinstance(obj, dict):
                    return obj
            except Exception:
                return None

        obj = try_parse_obj(s)
        if obj is None:
            # 尝试在文本里寻找第一个完整的大括号 JSON
            start = s.find("{")
            end = s.rfind("}")
            if start != -1 and end != -1 and end > start:
                candidate = s[start : end + 1]
                obj = try_parse_obj(candidate)

        if not isinstance(obj, dict):
            return "", ""

        result = obj.get("result")
        reason = obj.get("reason")
        return ("" if result is None else str(result)), (
            "" if reason is None else str(reason)
        )

    @staticmethod
    def build_payload(
        request_body: dict,
        query_text: str,
        round_index: int,
        history: Optional[list] = None,
    ) -> dict:
        """封装单轮请求的 payload 构造逻辑。

        入参:
        - request_body: 原始请求体（用于提取 lat/lon）
        - query_text: 本轮 query 文本
        - round_index: 轮次索引，从 0 开始
        - context: 可选的对话上下文（多轮对话携带）
        """

        logger.debug(f"request_body: {request_body}")
        logger.info(
            f"build_payload: round_index: {round_index}, query_text: {query_text}, history: {history}"
        )

        payload = dict()
        # 注意：这里必须使用列表而不是集合，集合不能包含不可哈希的 dict，会导致
        # "TypeError: unhashable type: 'dict'"。
        payload["messages"] = history
        payload["messages"].append(
            {
                "role": "user",
                "content": query_text,
            }
        )
        payload["user_info"] = {
            "car_id": "regression_car",
            "user_id": str(
                request_body.get("user_id", "regression_user") or "regression_user"
            ),
            "category": [
                "sport_team",
                "athlete",
                "sport_type",
            ],
        }
        payload["location"] = { # 默认北京市朝阳区
            "lat": str(request_body.get("lat", "39.904989") or "39.904989"),
            "lon": str(request_body.get("lon", "116.4855") or "116.4855"),
        }
        payload["use_search_cache"] = False
        if round_index > 0 and history:
            payload["history"] = history
        return payload

    @staticmethod
    async def run_single_case(request_body: dict) -> Result:
        """
        request_body的格式如下，lat与lon为可选参数，query为列表，如果是多轮对话，将存在多个元素
        request_body = {
            "query": ["xxx", "ccc"],
            "lat": 123.456,
            "lon": 78.901
        }
        """

        result = Result()
        # 收集多轮答案（仅在此函数内部使用）
        all_answers: list[str] = []

        try:
            queries = request_body.get("query")

            logger.debug(f"run_single_case: original queries: {queries}")

            # 兼容字符串或列表输入；最终转换为非空的字符串列表
            if isinstance(queries, list):
                rounds = [q for q in queries if isinstance(q, str) and q.strip()]
            elif isinstance(queries, str):
                rounds = [queries] if queries.strip() else []
            else:
                rounds = []

            if not rounds:
                logger.error("run_single_case: request_body.query is empty or invalid")
                return result

            logger.debug(f"run_single_case: processed rounds: {rounds}")

            history = []

            async with aiohttp.ClientSession() as session:
                for i, q in enumerate(rounds):
                    payload = BadCaseRunner.build_payload(
                        request_body=request_body,
                        query_text=q,
                        round_index=i,
                        history=history
                    )
                    logger.debug(f"run_single_case: payload: {payload}")
                    streamHelper = AgentStreamHelper()
                    try:
                        async with session.post(
                            "http://127.0.0.1:8080/gac/sports-events/v1/chat",
                            read_bufsize=4194304,
                            json=payload,
                        ) as response:
                            response.raise_for_status()

                            # 使用 AgentStreamHelper 的统一流式解析
                            async for raw_chunk in response.content.iter_any():
                                streamHelper.feed(raw_chunk)
                            streamHelper.flush()

                        result.code = code_complete
                        result.query = q

                        answer = streamHelper.get_json_data("answer") or ""
                        logger.debug(f"[ANSWER]: {answer}")

                        history.append({"content": f"{q}", "role": "user"})
                        history.append(
                            {"content": answer, "role": "assistant"}
                        )
                        
                        # 累积多轮答案
                        if answer:
                            all_answers.append(answer)

                    except BaseException as e:
                        logger.error(
                            f"new_run_single_case: payload: {payload}, e: {str(e)}"
                        )
                        # 继续尝试后续轮次
                        continue

            # 循环结束后，合并所有轮次的问答
            if all_answers:
                merged_blocks = []
                for idx, ans in enumerate(all_answers, start=1):
                    q_text = rounds[idx - 1] if idx - 1 < len(rounds) else ""
                    merged_blocks.append(f"--- Round {idx} ---\nQ: {q_text}\nA:\n{ans}")
                result.result_text = "\n\n".join(merged_blocks)

        except BaseException as e:
            logger.error(
                f"new_run_single_case: request_body: {request_body}, e: {str(e)}"
            )

        return result

    async def run_bad_cases(self):
        writer = ExcelResultWriter(excelHelper)
        skipped_case_list = []
        async for row in excelHelper.read_rows("观赛达人"):
            id = row.get("id")
            original_title = row.get("original_title")
            requirement = row.get("expected_output")
            request_body = row.get("request_body")
            logger.info(f"Processing case. original_title: {original_title}")
            if isinstance(request_body, str) and request_body.strip():
                try:
                    request_body_dict = json.loads(request_body)
                except Exception:
                    logger.error("request_body 解析失败")
                    skipped_case_list.append(id)
                    continue
            else:
                request_body_dict = request_body

            if not isinstance(request_body_dict, dict):
                logger.warning("request_body 不是字典，跳过该用例")
                skipped_case_list.append(id)
                continue

            logger.debug(f"request_body_dict: {request_body_dict}")
            single_case_result = await BadCaseRunner.run_single_case(request_body_dict)
            if single_case_result.code != code_complete:
                logger.error(f"run_single_case failed: {single_case_result}")

            answer = str(single_case_result.result_text or "")
            judge_result = await judgeHelper.judge_result(
                query=json.dumps(request_body_dict, ensure_ascii=False)
                if isinstance(request_body_dict, (dict, list))
                else str(request_body_dict),
                answer=answer,
                requirement=str(requirement or ""),
            )
            logger.debug(f"judge_result: {judge_result}")
            # 解析判定 JSON，拆分为 judge_result 与 judge_reason
            judge_result, judge_reason = BadCaseRunner._extract_judge_fields(
                judge_result or ""
            )
            # 收集并缓存在 writer 中
            await writer.append_row(
                row,
                regression_output=answer,
                judge_result=judge_result or "",
                judge_reason=judge_reason,
            )

        # 所有用例处理结束后，统一写入新 Excel
        writer.write_to(file_path_out)

        # 记录跳过的用例，写入到txt文件
        if len(skipped_case_list) > 0:
            with open("skipped_cases.txt", "w") as f:
                f.writelines([f"{case_id}\n" for case_id in skipped_case_list])


if __name__ == "__main__":
    loop = asyncio.new_event_loop()
    badCaseRunner = BadCaseRunner()
    try:
        asyncio.set_event_loop(loop=loop)
        loop.run_until_complete(badCaseRunner.run_bad_cases())
    finally:
        loop.close()
