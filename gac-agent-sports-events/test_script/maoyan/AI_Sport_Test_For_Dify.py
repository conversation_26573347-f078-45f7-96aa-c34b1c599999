import requests
import time
import json
import os
import pandas as pd



# ===== 配置区域 =====

DIFY_TTFB_THRESHOLD = 3  # Dify-TTFB耗时阈值
T0_MEM_THRESHOLD = 0.1 # t0-mem耗时阈值
T0_PRE_INTENT_THRESHOLD = 1 # t0-pre-intent耗时阈值
T1_SRC_THRESHOLD = 2 # t1-src耗时阈值
T1_LOC_THRESHOLD = 0.5     # t1-loc耗时阈值
T2_EVENT_DETAIL_THRESHOLD = 1 # t2-event-detail耗时阈值
T2_INTENT_THRESHOLD = 0.1     # t2-mem耗时阈值
T3_SRC_THRESHOLD = 1  # t3-intent耗时阈值
T4_FETCH_THRESHOLD = 2     # t4-src耗时阈值
T5_MFT_THRESHOLD = 1     # t5-fetch耗时阈值
T6_SFT_THRESHOLD = 0.5     # t6-mft耗时阈值
T7_FOLLOW_UP_THRESHOLD = 0.5     # t7-sft耗时阈值
ACFT_THRESHOLD = 3    # t8-acft耗时阈值

url = "https://agent-sit.senseauto.com/v1/chat-messages"
headers = {
    "Authorization": "Bearer app-mMFsf9LgFf8wnQkbLO9WrVGS",
    "Content-Type": "application/json"
}

queries = [
    "最近有哪些网球新闻",
    "湖人队最近状况怎么样",
    "最近NBA有哪些新闻",
    "皇马最近一场比赛的比分是多少",
    "我想看皇马的直播",
    "湖人最近有什么比赛",
    "我想看最近一场斯诺克比赛",
    "库里最近有没有受伤？他最近有哪些采访？",
    "拜仁第一次参加世俱杯是哪年？",
    "最近有哪些足球新闻",
    "只看亚洲的"
]
test_time = time.strftime("%Y%m%d_%H%M%S", time.localtime())
# desktop_path = os.path.expanduser("~/Desktop")
# output_file = os.path.join(desktop_path, "AI新闻_Dify_20250713_tencent.txt")

desktop_path = os.path.join(os.path.expanduser("~"),"桌面")
output_file = os.path.join("C:\\zhongyuanfeng_vendor\\桌面", "AI新闻_Dify_20250713_tencent.txt")
print(desktop_path)

# ===== 开始测试 =====

results = []

excelData = {
    'query': [],
    'Dify-TTFB': [],
    't0-mem': [],
    't0-pre-intent': [],
    't1-src': [],
    't2-event_detail': [],
    't1-loc': [],
    't2-intent': [],
    't3-src': [],
    't4-fetch': [],
    't5-mft': [],
    't6-sft': [],
    't7-follow_up': [],
    'ACFT': [],
    'Answer': [],
    'Remark': []
}


def output_excel(excel_name):
    df = pd.DataFrame(excelData)
    file_path = 'AI观赛达人_'+test_time+'.xlsx'
        
    # 使用ExcelWriter和xlsxwriter引擎

    writer = pd.ExcelWriter(file_path, engine='xlsxwriter')

    df.to_excel(writer, index=False, sheet_name='sport_news')
    # 获取工作表和workbook对象
    workbook = writer.book

    worksheet = writer.sheets['sport_news']
    # 定义格式
    header_format = workbook.add_format({
        'bold': True,
        'text_wrap': True,
        'valign': 'center',
        'fg_color': '#D7E4BC',
        'border': 1})
    data_format = workbook.add_format({'align': 'center', "valign": "vcenter"})
    # 设置列宽
    worksheet.set_column(0, len(excelData) - 2, 20, data_format)
    worksheet.set_column(len(excelData) - 1, len(excelData) - 1, 60, data_format)
    for row in range(1, len(excelData) - 1):  # 假设设置前 100 行
        worksheet.set_row(row, 30)
    # 应用标题格式（第一行是标题行）
    worksheet.write_row('A1', df.columns, header_format)

    yellow_format = workbook.add_format({'bg_color': '#FFFF00'})  # 黄色背景
    
    check_columns = {
        'B': 'Dify-TTFB',
        'C': 't0-mem',
        'D': 't0-pre-intent',
        'E': 't1-src',
        'F': 't2-event_detail',
        'G': 't1-loc',
        'H': 't2-intent',
        'I': 't3-src',
        'J': 't4-fetch',
        'K': 't5-mft',
        'L': 't6-sft',
        "M": 't7-follow_up',
        "N": 'ACFT',
    }
    
    # 应用条件格式到每一列
    for col_letter, col_name in check_columns.items():
        start_row = 1
        end_row = len(excelData[col_name]) - 1
        
        if col_letter == 'B':
            worksheet.conditional_format(
                f'{col_letter}{start_row + 1}:{col_letter}{end_row + 1}',
                {
                    'type': 'formula',
                    'criteria': f'={col_letter}2="N/A"',
                    'format': yellow_format
                }
            )
            worksheet.conditional_format(
                f'{col_letter}{start_row + 1}:{col_letter}{end_row + 1}',
                {
                    'type': 'formula',
                    'criteria': f'=AND(ISNUMBER(VALUE({col_letter}2)), VALUE({col_letter}2)>={DIFY_TTFB_THRESHOLD})',
                    'format': yellow_format
                }
            )
        else:
            threshold_var = globals()[f"{col_name.replace('-', '_').upper()}_THRESHOLD"]
            worksheet.conditional_format(
                f'{col_letter}{start_row + 1}:{col_letter}{end_row + 1}',
                {
                    'type': 'formula',
                    'criteria': f'=AND(ISNUMBER({col_letter}2), {col_letter}2>={threshold_var})',
                    'format': yellow_format
                }
            )

    writer.close()


def fill_excel_data():
    t0_mem = float(sense_time_info.get("t0-mem", 0)) if type(sense_time_info) is dict else 0
    t0_pre_intent = float(sense_time_info.get("t0-pre-intent", 0)) if type(sense_time_info) is dict else 0

    t1_src = float(sense_time_info.get("t1-src", 0)) if type(sense_time_info) is dict else 0
    t2_event_detail = float(sense_time_info.get("t2-event-detail", 0)) if type(sense_time_info) is dict else 0
    

    t1_loc = float(sense_time_info.get("t1-loc", 0)) if type(sense_time_info) is dict else 0
    t2_intent = float(sense_time_info.get("t2-intent", 0)) if type(sense_time_info) is dict else 0
    t3_src = float(sense_time_info.get("t3-src", 0)) if type(sense_time_info) is dict else 0
    t4_fetch = float(sense_time_info.get("t4-fetch", 0)) if type(sense_time_info) is dict else 0
    t5_mft = float(sense_time_info.get("t5-mft", 0)) if type(sense_time_info) is dict else 0
    t6_sft = float(sense_time_info.get("t6-sft", 0)) if type(sense_time_info) is dict else 0
    t7_follow_up = float(sense_time_info.get("t7-follow_up", 0)) if type(sense_time_info) is dict else 0

    acft = float(sense_time_info.get("actf", 0)) if type(sense_time_info) is dict else 0

    ttfb_value = float(ttfb_display) if ttfb_display != "N/A" else 0
    t0_pre_intent_display = t0_pre_intent - t0_mem
    
    if t2_event_detail > 0:
        t2_event_detail = t2_event_detail - t1_src
    if t1_src > 0:
        t1_src = t1_src - t0_pre_intent



    if t7_follow_up > 0:
        t7_follow_up = t7_follow_up - t6_sft
    if t6_sft > 0:
        t6_sft = t6_sft - t5_mft
    if t5_mft > 0:
        t5_mft = t5_mft - t4_fetch
    if t4_fetch> 0:
        t4_fetch = t4_fetch - t3_src
    if t3_src> 0:
        t3_src = t3_src - t2_intent
    if t2_intent >0:
        t2_intent = t2_intent - t1_loc
    if t1_loc > 0:
        t1_loc = t1_loc - t0_pre_intent

    if t0_pre_intent>0:
        t0_pre_intent = t0_pre_intent - t0_mem
    
    
    failed_columns = []
    if ttfb_display == "N/A":
        failed_columns.append(f"Dify-TTFB为N/A")
    elif ttfb_value >= DIFY_TTFB_THRESHOLD:
        failed_columns.append(f"Dify-TTFB超过{DIFY_TTFB_THRESHOLD}")
    if t0_mem >= T0_MEM_THRESHOLD:
        failed_columns.append(f"t0-mem超过{T0_MEM_THRESHOLD}")
    if t0_pre_intent_display >= T0_PRE_INTENT_THRESHOLD:
        failed_columns.append(f"t0-pre-intent超过{T0_PRE_INTENT_THRESHOLD}")
    if t1_loc >= T1_LOC_THRESHOLD:
        failed_columns.append(f"t1-loc超过{T1_LOC_THRESHOLD}")
    if t2_intent >= T2_INTENT_THRESHOLD:
        failed_columns.append(f"t2-intent超过{T2_INTENT_THRESHOLD}")
    if t3_src >= T3_SRC_THRESHOLD:
        failed_columns.append(f"t3-src超过{T3_SRC_THRESHOLD}")
    if t4_fetch >= T4_FETCH_THRESHOLD:
        failed_columns.append(f"t4-fetch超过{T4_FETCH_THRESHOLD}")
    if t5_mft >= T5_MFT_THRESHOLD:
        failed_columns.append(f"t5-mft超过{T5_MFT_THRESHOLD}")
    if t6_sft >= T6_SFT_THRESHOLD:
        failed_columns.append(f"t6-sft超过{T6_SFT_THRESHOLD}")
    if t7_follow_up >= T7_FOLLOW_UP_THRESHOLD:
        failed_columns.append(f"t7-follow-up超过{T7_FOLLOW_UP_THRESHOLD}")
    if acft >= ACFT_THRESHOLD:
        failed_columns.append(f"acft超过{ACFT_THRESHOLD}")
        
    remark = "; ".join(failed_columns) if failed_columns else ""
    
    excelData['query'].append(query_text)
    excelData['Dify-TTFB'].append(ttfb_display)
    excelData['t0-mem'].append(t0_mem)
    excelData['t0-pre-intent'].append(t0_pre_intent)



    excelData['t1-src'].append(t1_src)
    excelData['t2-event_detail'].append(t2_event_detail)

    excelData['t1-loc'].append(t1_loc)
    excelData['t2-intent'].append(t2_intent)
    excelData['t3-src'].append(t3_src)
    excelData['t4-fetch'].append(t4_fetch)
    excelData['t5-mft'].append(t5_mft)
    excelData['t6-sft'].append(t6_sft)
    excelData['t7-follow_up'].append(t7_follow_up)

    excelData['ACFT'].append(acft)

    excelData['Answer'].append(final_answer)
    excelData['Remark'].append(remark)



def add_params_des():
    if excelData['query']:
        excelData['query'].append(" ")
        excelData['Dify-TTFB'].append("Dify智能体接口首字延迟")
        excelData['t0-mem'].append("获取用户画像")
        excelData['t0-pre-intent'].append("落域")
        excelData['t1-src'].append("咪咕数据获取")
        excelData['t2-event_detail'].append("用咪咕信息生成回复")

        excelData['t1-loc'].append("高德地址信息")
        excelData['t2-intent'].append("意图识别")
        excelData['t3-src'].append("搜索引擎返回结果")
        excelData['t4-fetch'].append("爬虫结束")
        excelData['t5-mft'].append("大模型总结首字延迟")
        excelData['t6-sft'].append("安全检测首字延迟")
        excelData['t7-follow_up'].append("相关推荐")

        excelData['ACFT'].append("原子能力接口首字延迟")

        excelData['Answer'].append("")
        excelData['Remark'].append("")

conversation_id_to_pass = None

for idx, query_text in enumerate(queries, 1):
    print(f"🚀 正在测试 Query {idx}/{len(queries)}：{query_text}")

    payload = {
        "query": query_text,
        "response_mode": "streaming",
        "inputs": {
            "car_id": "fake_car_id",
            "user_id": "fake_user_id",
            "lat": "31.16813",
            "lon": "121.39987",
            "detect": "false",
            "search_engine": "tencent",
            "filter_time_for_live": "false"
        },
        "user": "abc-124"
    }

    if idx == len(queries) and conversation_id_to_pass:
        payload["conversation_id"] = conversation_id_to_pass
        print(f"💡 正在为最后一条 query 传入 conversation_id: {conversation_id_to_pass}")

    start_time = time.time()
    print(f"✅ start_time ：{start_time}")
    try:
        response = requests.post(url, headers=headers, json=payload, stream=True, timeout=15)
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        continue

    if response.status_code != 200:
        print(f"❌ 请求失败，状态码: {response.status_code}")
        continue

    # 初始化状态
    first_message_time = None
    final_answer = ""
    sense_time_info = None
    region_type = None
    try:
        for raw_chunk in response.iter_lines(decode_unicode=True):
            if not raw_chunk.strip():
                continue

            if raw_chunk.startswith("data: "):
                json_str = raw_chunk[len("data: "):]
                try:
                    data = json.loads(json_str)
                except json.JSONDecodeError:
                    continue

                if idx == len(queries) - 1: # 仅在倒数第二个用例时执行
                    if data.get("event") == "workflow_started" and "conversation_id" in data:
                        conversation_id_to_pass = data.get("conversation_id")
                        print(f"⭐ 成功捕获 conversation_id: {conversation_id_to_pass}")

                event_type = data.get("event")

                if event_type == "message":
                    if first_message_time is None:
                        first_message_time = time.time()
                        print(f"✅ first_message_time ：{first_message_time}")
                        ttfb = round((first_message_time - start_time), 4)

                    if "answer" in data:
                        try:
                            answer_obj = json.loads(data["answer"])
                            region_type = answer_obj.get("region")
                            text = answer_obj.get("data", "")
                            if "sport_chat" == region_type:
                                final_answer += text
                            else:
                                final_answer = text


                        except json.JSONDecodeError:
                            continue

                elif event_type == "agent_log":
                    log_data = data.get("data")
                    if isinstance(log_data, dict) and log_data.get("label") == "Sense_Time_Info":
                        sense_time_info = log_data.get("data")

    except Exception as e:
        print(f"⚠️ 流读取异常: {e}")
        continue

    # 输出并保存结果
    ttfb_display = str(round((first_message_time - start_time),4)) if first_message_time else "N/A"
    if type(final_answer) is not dict:
        final_answer = final_answer.strip() or "无有效响应"
    sense_time_str = json.dumps(sense_time_info, ensure_ascii=False) if sense_time_info else "无"

    result_text = f"\n\nQuery: {query_text}\nTTFB(ms): {ttfb_display}\nSense_Time_Info: {sense_time_str}\nFinal Answer: {final_answer}"
    results.append(result_text)
    # print(f"results: {results}")
    fill_excel_data()

add_params_des()
if excelData['query']:
    output_excel("excelData")
print("✅ 写入完成\n")

# ===== 写入文件 =====

# try:
#     with open(output_file, "w", encoding="utf-8") as f:
#         f.write("\n".join(results))
#     print(f"📄 测试结果已保存到：{output_file}")
# except Exception as e:
#     print(f"❌ 写文件失败: {e}")
