from processor.processer_entrance import SportProcesserState
from schemas.sports_agent_req import SportsAgentReq
from service.sports_chat_service import SportChatServiceClient
from sse_starlette.sse import EventSourceResponse as StreamResponse
from main import chat_config
from loguru import logger

sports_chat_service = SportChatServiceClient(chat_config)

class SportSearchProcessor:
    def __init__(self, request: SportsAgentReq):
        self.request = request

    async def engage(self, state: SportProcesserState, pre_intent_info: dict) -> StreamResponse:
        agent_request = self.request
        input_messages = agent_request.messages
        is_stream = agent_request.stream
        history_msgs = [msg.dict() for msg in agent_request.messages]
        req_body = {
            "engine": "tencent",
            "query": input_messages[-1].content,
            "history": history_msgs,
            "stream": is_stream
        }
        if agent_request.location:
            req_body["location"] = agent_request.location.dict()
        if agent_request.user_info:
            req_body["user_info"] = agent_request.user_info.dict()
        logger.info("sports_chat start")
        generator = sports_chat_service.call_sports_chat_service(req_body, is_stream, pre_intent_info)
        http_status, http_headers = next(generator)
        return StreamResponse(generator, headers=http_headers)

