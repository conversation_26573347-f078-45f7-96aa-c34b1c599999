from openai import OpenAI
from main import llm_config


openai_api_key = llm_config["api_key"]
openai_api_base = llm_config["base_url"]
model_name = llm_config["model_name"]

def client_openai(messages: list):

    client = OpenAI(
        api_key=openai_api_key,
        base_url=openai_api_base,
    )
    response = client.chat.completions.create(
        model=model_name,
        messages=messages,
        logprobs=True,
        top_p=0.8,
        temperature=0.6,
    )
    content = response.choices[0].message.content
    return content