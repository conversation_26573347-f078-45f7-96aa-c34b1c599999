
import json
import time
from datetime import datetime, timedelta
from typing import Optional, Union
import asyncio

from processor.processer_entrance import SportProcesserState, extract_json_from_text
from sample.migu_api_sample import test_get_competition_info, test_query_matches, get_game_score_new
from urllib.parse import quote
from loguru import logger
from typing_extensions import TypedDict, Optional

from sse_starlette.sse import EventSourceResponse as StreamResponse
from utils.time_util import addDurationAndEndTime

from processor.client_openai import client_openai
from schemas.models import ErrorDataModel, EventSummaryResultModel, FollowUpQuestionResultModel

try:
    from zoneinfo import ZoneInfo
    LOCAL_TZ = ZoneInfo("Asia/Shanghai")  # 按需替换
except Exception:
    LOCAL_TZ = None

def _now_tz():
    return datetime.now(tz=LOCAL_TZ) if LOCAL_TZ else datetime.now()

def _day_floor(dt: datetime) -> datetime:
    return dt.replace(hour=0, minute=0, second=0, microsecond=0)

def _day_end(dt: datetime) -> datetime:
    # 闭区间：当天 23:59:59
    return _day_floor(dt) + timedelta(days=1) - timedelta(seconds=1)

def _safe_int(x) -> Optional[int]:
    try:
        return int(x)
    except Exception:
        return None

def _parse_ymd_dt(t: Union[int, str]) -> Optional[datetime]:
    """
    同时支持 "YYYYMMDD" 字符串与 8 位整数（如 20250825）的绝对日期解析。
    非法或越界返回 None。
    """
    if isinstance(t, int):
        s = str(t)
    elif isinstance(t, str) and t.isdigit():
        s = t
    else:
        return None

    if len(s) != 8:
        return None

    try:
        dt = datetime.strptime(s, "%Y%m%d")
        if LOCAL_TZ:
            dt = dt.replace(tzinfo=LOCAL_TZ)
        return dt
    except ValueError:
        return None

def to_timestamp(
    t: Union[int, str, None],
    *,
    is_end: bool = False,
    match_count: Optional[int] = None
) -> Optional[int]:
    """
    将 Prompt 的 start_date/end_date 统一转为 Unix 秒级时间戳（闭区间语义）。

    规则：
    - 绝对日(YYYYMMDD 字符串或等价 8 位整数)：
        * 若该绝对日==今天 且 match_count<10 -> 返回“此刻”（start/end 同样处理）
        * 否则：start -> 当天 00:00:00；end -> 当天 23:59:59
    - 相对日(整数/可转整数，以“今天”为 0 基准)：
        start -> 默认该日 00:00:00；若 v==0 且 match_count<10 -> “此刻”
        end   -> 该日 23:59:59；若 v==0 -> “此刻”
    - 其他非法输入 -> None
    """
    if t is None or t == '':
        return None

    now = _now_tz()

    # —— 绝对日期优先识别（兼容字符串与整数）——
    dt_abs = _parse_ymd_dt(t)
    if dt_abs is not None:
        same_day_as_today = _day_floor(dt_abs) == _day_floor(now)
        if same_day_as_today and (match_count is not None and match_count < 10):
            return int(now.timestamp())
        return int((_day_end(dt_abs) if is_end else _day_floor(dt_abs)).timestamp())

    # —— 相对日期 ——
    v = _safe_int(t)
    if v is None:
        return None

    target = _day_floor(now) + timedelta(days=v)
    if not is_end:
        if v == 0 and (match_count is not None and match_count < 10):
            return int(now.timestamp())
        return int(_day_floor(target).timestamp())
    else:
        if v == 0:
            return int(now.timestamp())
        return int(_day_end(target).timestamp())





def url_encode(id, type='live'):
    assert type in ['live', 'replay', 'highlights']
    base_url = "miguvideocar://miguvideo?action="
    if type == 'live':
        params = {
            "type":"JUMP_DETAIL_PAGE", # 跳转播放详情页面
            "params": {
                "mgdbId": id, # 挂件id
                "programTypeV2": "LIVE" # 固定值，正在直播的节目传
            }
        }
    elif type == 'replay' or type == 'highlights':
        params = {
            "type":"JUMP_DETAIL_PAGE", # 跳转播放详情页面
            "params": {
                "contentID": id, # 节目id
            }
        }
    return base_url + quote(json.dumps(params))

class EventQueryResult(TypedDict, total=False):
    matches_to_yield: Optional[list]
    scores_to_yield: list
    matches_info: list
    live_links: list
    replay_links: list
    highlights_links: list
    error_infos: Optional[list]


class SportEventProcessor:
    def __init__(self, intent_json: dict, query: str, habit: Optional[str], history: list = []):
        self.intent_json = intent_json
        self.query = query
        self.habit = habit
        self.history = history

    @addDurationAndEndTime
    def _process_event_query(self, state: SportProcesserState,s1_res: dict) -> EventQueryResult:
        live_links = []
        replay_links = []
        highlights_links = []
        error_infos = []
        competition_info_model = test_get_competition_info()
        competitions = {"data": []}
        if competition_info_model.error_info:
            error_infos.append(competition_info_model.error_info)
        else:
            try:
                competitions = json.loads(competition_info_model.result)
            except BaseException as e:
                from constance.constance_value import error_code_match_category_return_error
                error_infos.append(ErrorDataModel(error_code=error_code_match_category_return_error, error_message=f"fail to format competition info, raw: {competition_info_model.result}"))

        logger.info(f"competitions: {competitions}")
        competitions_filter = []
        sport_type = s1_res.get("sport_type")

        # 获取用户指定的赛事列表
        user_competitions = s1_res.get('competition', [])
        for item in competitions['data']:
            if sport_type and sport_type in item['category']:
                # 如果指定了具体赛事，只添加匹配的赛事
                if user_competitions:
                    for comp in item['competitions']:
                        if comp in user_competitions:
                            competitions_filter.append(comp)
                else:
                    competitions_filter += item['competitions']

        logger.info(f"competitions_filter: {competitions_filter}")
        matches = []
        match_competition_mapping = {}
        if 0 >= len(competitions_filter):
            from constance.constance_value import error_code_match_category_empty_error
            error_infos.append(ErrorDataModel(error_code=error_code_match_category_empty_error,
                                              error_message=f"Empty available competition list"))
        for competition in competitions_filter:
            start_time, end_time = None, None
            mc = s1_res.get('match_count', None)
            start_raw = s1_res.get('start_date')
            end_raw   = s1_res.get('end_date')

            if 'start_date' in s1_res:
                start_time = to_timestamp(start_raw, is_end=False, match_count=mc) if start_raw is not None else None
                if start_time is not None:
                    start_time = int(start_time)

            if 'end_date' in s1_res:
                end_time = to_timestamp(end_raw, is_end=True, match_count=mc) if end_raw is not None else None
                if end_time is not None:
                    end_time = int(end_time)
            match_info_model = test_query_matches(competition_name=competition,
                                            start_time=start_time,
                                            end_time=end_time,
                                            match_name=s1_res['team_name'] if 'team_name' in s1_res else None,
                                            size=s1_res['match_count'] if 'match_count' in s1_res else None,
                                            contains_highlights=s1_res[
                                                'need_highlights'] if 'need_highlights' in s1_res else None,
                                            contains_playback=s1_res[
                                                'need_replay'] if 'need_replay' in s1_res else None)
            if match_info_model.error_info:
                error_infos.append(match_info_model.error_info)
            else:
                match_info = match_info_model.result
                try:
                    competition_matches = json.loads(match_info)['data']
                    # 为每个比赛记录其所属赛事
                    for match in competition_matches:
                        match_competition_mapping[match['pendantId']] = competition
                    matches += competition_matches
                except Exception as e:
                    from constance.constance_value import error_code_match_search_return_error
                    error_infos.append(ErrorDataModel(error_code=error_code_match_search_return_error, error_message=f"fail to format match info, competition name: {competition}, raw: {match_info}"))
                    logger.error(f"fail to parse data form test_query_matches result, Error is {e}")
        print('matches:', len(matches))
        need_live = s1_res.get('need_live', False)
        need_replay = s1_res.get('need_replay', False)
        need_highlights = s1_res.get('need_highlights', False)
        need_score = s1_res.get('need_score', False)
        matches_info = []
        scores_to_yield = []
        matches_to_yield = []
        enable_time_filter_for_live = state.get("enable_time_filter_for_live", True)

        if 0 >= len(matches):
            from constance.constance_value import error_code_match_search_empty
            error_infos.append(ErrorDataModel(error_code=error_code_match_search_empty, error_message="matches result is empty"))

        for match in matches:
            # 为每个比赛生成直播链接
            live_link = url_encode(match['pendantId'])
            if need_live:
                current_time = time.time()
                if enable_time_filter_for_live and (current_time < match['liveStartTime'] or current_time > match['liveEndTime']):
                    continue
                live_links.append(live_link)
            match_info = {}
            match_info['matchTitle'] = match['matchTitle']
            match_info['matchStartTime'] = datetime.fromtimestamp(match['matchStartTime']).strftime(
                "%Y-%m-%d %H:%M:%S")
            match_info['phase'] = match['phase']
            match_info['matchGroup'] = match['matchGroup']
            match_info['round'] = match['round'] if 'round' in match else None
            match_info['teams'] = [{"teamName": item['name']} for item in match['confrontTeams']]
            match_info['competition_name'] = match_competition_mapping.get(match['pendantId'], '')
            # logger.info(f"match_info: {match_info}")

            if need_score:
                pendant_id = match['pendantId']
                score_info_model = get_game_score_new([pendant_id])
                score_info = {"data": []}
                if score_info_model.error_info:
                    error_infos.append(score_info_model.error_info)
                else:
                    try:
                        score_info = json.loads(score_info_model.result)
                    except BaseException as e:
                        from constance.constance_value import error_code_score_search_return_error
                        error_infos.append(ErrorDataModel(error_code=error_code_score_search_return_error,
                                                          error_message=f"fail to format score info, pendantId: {pendant_id}, raw: {score_info_model.result}"))


                # logger.info(f"type: {type(score_info)}, score_info: {score_info}")


                for group in score_info.get("data", []):
                    scores_to_yield.append(group)
                    for score_item in group.get("scoreInfo", []):
                        team_name = score_item.get("teamName")
                        score_val = score_item.get("score")
                        if not team_name:
                            continue
                        for t in match_info["teams"]:
                            if t.get("teamName") == team_name:
                                t["score"] = score_val
            # 目前信源中都没有回放和集锦，这里暂时fake; 新增：回放没有的时候直接用直播链接
            if need_replay:
                replay_links.append(live_link)
            if need_highlights:
                highlights_links.append(live_link)
            matches_to_yield.append(match)
            matches_info.append(match_info)

        logger.info(f"matches_info: {matches_info}")

        event_query_result = EventQueryResult()
        event_query_result["matches_to_yield"] = matches_to_yield
        event_query_result["scores_to_yield"] = scores_to_yield
        event_query_result["matches_info"] = matches_info
        event_query_result["live_links"] = live_links
        event_query_result["replay_links"] = replay_links
        event_query_result["highlights_links"] = highlights_links
        event_query_result["error_infos"] = error_infos
        return event_query_result


    @addDurationAndEndTime
    def _choose_jump_url(self, s1_res: dict, matches_info: list, event_query_result: EventQueryResult):
        need_live = s1_res.get('need_live', False)
        need_replay = s1_res.get('need_replay', False)
        need_highlights = s1_res.get('need_highlights', False)
        from prompts.prompt_integrate import prompt_choose_jump_url
        jump_prompt = prompt_choose_jump_url
        messages = [
            {"role": "system", "content": jump_prompt},
            {"role": "user", "content": json.dumps(
                {"user_profile": self.habit, "conversation_history": self.history,
                 "match_data": json.dumps(matches_info, ensure_ascii=False), "current_query": self.query},
                ensure_ascii=False, indent=4)}
        ]
        print('DEBUG: messages:', messages[1])
        jump_res = client_openai(messages)
        print('DEBUG: jump res:', jump_res)
        index = int(jump_res)
        res = "正在为您跳转播放"
        print(res)
        if need_replay:
            return event_query_result.get("replay_links")[index]
        if need_live:
            return event_query_result.get("live_links")[index]
        if need_highlights:
            return event_query_result.get("highlights_links")[index]
        return None

    @addDurationAndEndTime
    def _integrate_and_answer(self, s1_res: dict, matches_info: list) -> EventSummaryResultModel:
        error_data = None
        from prompts.prompt_integrate import prompt_integrate
        integrate_prompt = prompt_integrate
        messages = [
            {"role": "system", "content": integrate_prompt},
            {"role": "user", "content": json.dumps({"user_profile": self.habit, "conversation_history": self.history,
                                                    "match_data": json.dumps(matches_info, ensure_ascii=False),
                                                    "user_query": self.query}, ensure_ascii=False, indent=4)}
        ]
        print('messages:', messages[1])
        integrate_res = None
        summary_result = None
        try:
            integrate_res = client_openai(messages)
        except BaseException as e:
            from constance.constance_value import error_code_event_summary_http_error
            error_data = ErrorDataModel(error_code_event_summary_http_error, error_message=f"Event summary error, e: {str(e)}")

        if integrate_res:
            from constance.constance_value import error_code_event_summary_format_error
            summary_result, error_data = extract_json_from_text(integrate_res, error_code_event_summary_format_error)

        print('integrate res:', summary_result)
        return EventSummaryResultModel(result=summary_result, error_info=error_data)

    def _create_follow_up_question(self, raw_query: str, tts_content: str) -> FollowUpQuestionResultModel:
        error_data = None
        query = raw_query + "\n" + tts_content + "\n"
        from prompts.prompt_follow_up import follow_up_prompts_event as follow_up_prompts_base
        follow_up_prompt = follow_up_prompts_base.format(chat_his=query)
        # 没有system prompt
        messages = [{"role": "user", "content": follow_up_prompt}]
        # logger.info(f"messages:   {messages}")
        follow_up_question_result = None
        follow_up_questions = None
        try:
            follow_up_question_result=client_openai(messages)
        except BaseException as e:
            from constance.constance_value import error_code_relative_http_error
            error_data = ErrorDataModel(error_code=error_code_relative_http_error, error_message=f"Event follow up http error, e: {str(e)}")
        if follow_up_question_result:
            try:
                follow_up_questions = json.loads(follow_up_question_result)
                if isinstance(follow_up_questions, list):
                    if 3 == len(follow_up_questions):
                        follow_up_questions = follow_up_questions
                    else:
                        follow_up_questions_list = []
                        for question_item in follow_up_questions:
                            if "|" in question_item:
                                split_question_item = question_item.split("|")
                                follow_up_questions_list += split_question_item
                        if 3 < len(follow_up_questions):
                            follow_up_questions_list = follow_up_questions_list[0:2]
                        follow_up_questions = follow_up_questions_list

            except Exception as e:
                try:
                    follow_up_questions = follow_up_question_result.split("|")
                    if isinstance(follow_up_questions, list):
                        if 3 < len(follow_up_questions):
                            follow_up_questions = follow_up_questions[0:2]
                except BaseException as e:
                    from constance.constance_value import error_code_relative_format_error
                    error_data = ErrorDataModel(error_code=error_code_relative_format_error, error_message=f"Event follow up format error, raw data: {follow_up_question_result}")
        return FollowUpQuestionResultModel(result_list=follow_up_questions, error_info=error_data)

    async def engage(self, state: SportProcesserState, response_header: dict, pre_intent_info: dict, to_notify_errors: Optional[list] = None):
        message_id = state["message_id"]
        async def event_generator():
            if to_notify_errors and isinstance(to_notify_errors, list):
                for error_item in to_notify_errors:
                    if isinstance(error_item, ErrorDataModel):
                        yield {
                            "type": "error",
                            "code": error_item.error_code,
                            "data": error_item.error_message,
                            "messageId": str(message_id)
                        }
            from processor.phase_name import phase_pre_intent
            yield dict(data=json.dumps({"type":phase_pre_intent, "data": pre_intent_info}))
            async for result in self._real_engage(state):
                print(result)
                if isinstance(result, dict):
                    yield dict(data=json.dumps(result, ensure_ascii=False))
            message_end_json = {
                "type": "messageEnd",
                "messageId": str(message_id),
                "time_cost" : {
                    "t0-mem" : state.get("t0_mem"),
                    "t0-pre-intent" : state.get("t0_pre_intent"),
                    "t1-src" : state.get("t1_src"),
                    "t2-event-detail" : state.get("t2_event_detail"),
                    # "total_time": round(time.perf_counter() - start_time, 4)
                }
            }
            yield dict(data=json.dumps(message_end_json, ensure_ascii=False))

        return StreamResponse(event_generator(), headers=response_header)

    async def _real_engage(self, state: SportProcesserState):

        message_id = state["message_id"]
        s1_res = self.intent_json

        # 2. get all competitions
        # 3. get all games
        logger.info("event_query start")
        event_query_result, time_map = self._process_event_query(state=state, s1_res=s1_res)
        if isinstance(time_map, dict) and "end_time" in time_map:
            event_query_time_cost = round((time_map["end_time"] - state["start_time"]), 4)
            state["t1_src"] = event_query_time_cost
            logger.info(f"event_query end-----cost time:{event_query_time_cost}")
        error_infos = event_query_result["error_infos"]
        if isinstance(error_infos, list):
            for error_item in error_infos:
                if isinstance(error_item, ErrorDataModel):
                    yield {
                        "type": "error",
                        "code": error_item.error_code,
                        "data": error_item.error_message,
                        "messageId": message_id
                    }
        from processor.phase_name import phase_match_source
        yield {
            "type": phase_match_source,
            "data": {"matches": event_query_result["matches_to_yield"],
                     "score": event_query_result["scores_to_yield"]},
            "message_id": message_id,
        }

        from processor.phase_name import phase_event_detail_reply
        if len(event_query_result["matches_info"]) == 0:
            res = '没有找到对应的比赛信息'

            yield {
                "type": phase_event_detail_reply,
                "data": { "message": res },
                "message_id": message_id,
            }
            # from constance.constance_value import error_code_empty_matches_info
            # yield {
            #     "type": "error",
            #     "code": error_code_empty_matches_info,
            #     "data": "No matching match information found",
            #     "message_id": message_id,
            # }

        else:
            need_live = s1_res.get('need_live', False)
            need_replay = s1_res.get('need_replay', False)
            need_highlights = s1_res.get('need_highlights', False)
            need_score = s1_res.get('need_score', False)
            if not need_score and (need_live or need_replay or need_highlights):
                logger.info("choose_jump_url start")
                jump_url, time_map = self._choose_jump_url(s1_res, matches_info=event_query_result["matches_info"], event_query_result = event_query_result)
                if isinstance(time_map, dict) and "end_time" in time_map:
                    jump_url_time_cost = round((time_map["end_time"] - state["start_time"]), 4)
                    state["t2_event_detail"] = jump_url_time_cost
                    logger.info(f"integrate_and_answer end-----cost time:{jump_url_time_cost}")
                yield {
                    "type": phase_event_detail_reply,
                    "data": {
                        "jump_url": jump_url,
                    },
                    "message_id": message_id,
                }
            else:
                # 4. integrate and answer
                logger.info("integrate_and_answer start")
                title_and_summary_model, time_map = self._integrate_and_answer(s1_res, event_query_result["matches_info"])
                if isinstance(time_map, dict) and "end_time" in time_map:
                    summary_time_cost = round((time_map["end_time"] - state["start_time"]), 4)
                    state["t2_event_detail"] = summary_time_cost
                    logger.info(f"integrate_and_answer end-----cost time:{summary_time_cost}")
                title_and_summary = title_and_summary_model.result
                title_and_summary_error = title_and_summary_model.error_info
                if title_and_summary_error:
                    yield {
                        "type": "error",
                        "code": title_and_summary_error.error_code,
                        "data": title_and_summary_error.error_message,
                        "message_id": message_id
                    }
                else:
                    conclusion_title = title_and_summary.get("Title", "")
                    tts_summary = title_and_summary.get("summary", "")
                    yield {
                        "type": phase_event_detail_reply,
                        "data": {
                            "message": tts_summary
                        },
                        "message_id": message_id,
                    }
                    follow_up_result_model = self._create_follow_up_question(raw_query=self.query, tts_content=tts_summary)
                    if follow_up_result_model.error_info:
                        yield {
                            "type": "error",
                            "code": follow_up_result_model.error_info.error_code,
                            "data": follow_up_result_model.error_info.error_message,
                            "messageId": message_id,
                        }
                        yield {
                            "conclusion_title": conclusion_title,
                            "type": "follow_up",
                            "messageId": message_id,
                        }
                    else:
                        if follow_up_result_model.result_list:
                            yield {
                                "data": follow_up_result_model.result_list,
                                "conclusion_title": conclusion_title,
                                "type": "follow_up",
                                "messageId": message_id,
                            }
                        else:
                            yield {
                                "conclusion_title": conclusion_title,
                                "type": "follow_up",
                                "messageId": message_id,
                            }