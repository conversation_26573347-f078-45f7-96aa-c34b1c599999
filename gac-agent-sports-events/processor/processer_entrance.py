from __future__ import annotations
from starlette.responses import Response
from typing_extensions import TypedDict, Optional
from utils.time_util import addDurationAndEndTime
from sse_starlette.sse import EventSourceResponse as StreamResponse
from loguru import logger
import uuid, asyncio
from processor.client_openai import client_openai
from datetime import datetime, date, timedelta
from zoneinfo import ZoneInfo  # Python 3.9+
from schemas.models import PreIntentResultModel, ErrorDataModel
from constance.constance_value import error_code_pre_intent_format_error

import secrets, json, re

from schemas.sports_agent_req import SportsAgentReq, Message

WEEKDAY_NAME_CN = {1: "周一", 2: "周二", 3: "周三", 4: "周四", 5: "周五", 6: "周六", 7: "周日"}

def extract_json_from_text(text, error_code_type: int):

    if not text:
        return None, ErrorDataModel(error_code=error_code_type, error_message="None result")
    # 匹配 ```json ... ``` 块
    match = re.search(r"```json\s*(\{.*?\}|\[.*?\])\s*```", text, re.DOTALL)

    if match:
        json_str = match.group(1)
        try:
            return json.loads(json_str), None
        except json.JSONDecodeError as e:
            print("ERROR:解析失败：", e)
            print(json_str)
    else:
        print("ERROR:没有找到 json 块", text)
    return None, ErrorDataModel(error_code=error_code_type, error_message=f"Not valid json, text: {text}")

async def data_generator(pre_intent_info: dict, to_notify_errors: Optional[list] = None):
    message_id = uuid.uuid4()
    if to_notify_errors and isinstance(to_notify_errors, list):
        for error_item in to_notify_errors:
            if isinstance(error_item, ErrorDataModel):
                yield json.dumps({
                    "type": "error",
                    "code": error_item.error_code,
                    "data": error_item.error_message,
                    "messageId": str(message_id)
                }, ensure_ascii=False)
    from processor.phase_name import phase_pre_intent
    yield json.dumps({"type": phase_pre_intent, "data": pre_intent_info}, ensure_ascii=False)
    from constance.constance_value import error_code_irrelevant
    yield json.dumps({"type": "error", "code": error_code_irrelevant, "data": "Not sport relative query, Agent should deny it.","messageId": str(message_id)}, ensure_ascii=False)
    response_data_list = ['这个', '问题', '有点', '超出', '我', '的', '知识', '范畴', '啦', '']
    for data in response_data_list:
        # 模拟异步延迟
        await asyncio.sleep(0.1)
        yield json.dumps( { "data": data, "type": "message", "messageId": str(message_id)}, ensure_ascii=False)

def build_date(tz: str = "Asia/Shanghai") -> dict:
    """构造 date 字段。优先用传入时区；服务器在哪都不受影响。"""
    tzinfo = ZoneInfo(tz)
    now_dt = datetime.now(tzinfo)           # 带时区的当前时间
    today = now_dt.date()

    # ISO 周：周一=1 … 周日=7
    weekday_iso = today.isoweekday()

    return {
        "today_ymd": today.strftime("%Y%m%d"),
        "year": today.year,
        "month": today.month,
        "day": today.day,
        "weekday_iso": weekday_iso,     # 1..7
        "weekday_name": WEEKDAY_NAME_CN[weekday_iso],
    }

class SportProcesserState(TypedDict, total=False):
    message_id: str
    request_id: str
    start_time: float

    enable_time_filter_for_live: bool

    t0_mem: float
    t0_pre_intent: float

    t1_src: float
    t2_event_detail: float
    ai_search_time_map: dict

class EntranceResult:
    pass

class ProcessEntrance:
    def __init__(self, request: SportsAgentReq, response_header: dict):
        self.request = request
        self.response_header = response_header

    @addDurationAndEndTime
    def _pre_intent_check(self, query: str, habit: Optional[str], history: list) -> PreIntentResultModel:
        error_info: Optional[ErrorDataModel] = None
        from prompts.prompt_intent_rewrite import prompt_intent_rewrite
        intent_rewrite_prompt = prompt_intent_rewrite
        user_content = {"current_query": query, "conversation_history": history}
        if habit:
            user_content["user_profile"] = habit
        else:
            user_content["user_profile"] = ""
        user_content["date"] = build_date(tz="Asia/Shanghai") # 新增日期字段
        messages = [
            {"role": "system", "content": intent_rewrite_prompt},
            {"role": "user", "content": json.dumps(user_content, ensure_ascii=False, indent=4)}
        ]
        # logger.info(f"pre_intent messages: {messages}")
        s1_res = None
        pre_intent_check_result = None
        try:
            s1_res = client_openai(messages)
        except BaseException as e:
            from constance.constance_value import error_code_pre_intent_net_error
            error_info = ErrorDataModel(error_code_pre_intent_net_error, f"pre intent call openai error: {str(e)}")
        if s1_res:
            pre_intent_check_result, error_info = extract_json_from_text(s1_res, error_code_pre_intent_format_error)
        return PreIntentResultModel(result=pre_intent_check_result, error_info=error_info)

    async def process(self, state: SportProcesserState, habit: Optional[str], to_notify_errors: Optional[list[ErrorDataModel]] = None) -> Response:
        message_id = secrets.token_hex(7)
        state["message_id"] = message_id

        history = []
        for msg in self.request.messages[:-1]:
            if isinstance(msg, Message):
                if msg.role and "user" == msg.role.lower() and msg.content:
                    history.append(f"用户：{msg.content}")
                if msg.role and "assistant" == msg.role.lower() and msg.content:
                    history.append(f"助手：{msg.content}")
        query = self.request.messages[-1].content

        logger.info("pre_intent start")
        # 1. intent and rewrite
        pre_intent_check_result_model, time_map = self._pre_intent_check(query, habit, history)
        s1_res = pre_intent_check_result_model.result
        pre_intent_error = pre_intent_check_result_model.error_info
        if isinstance(time_map, dict) and "end_time" in time_map:
            pre_intent_time_cost = round((time_map["end_time"] - state["start_time"]), 4)
            state["t0_pre_intent"] = pre_intent_time_cost
            logger.info(f"pre_intent end-----cost time:{pre_intent_time_cost}-----pre_intent:{s1_res}")
        if pre_intent_error:
            if not to_notify_errors:
                to_notify_errors = []
            to_notify_errors.append(pre_intent_error)

        intent_result = None
        pre_intent_info = {
            "t0_mem": state.get("t0_mem"),
            "t0_pre_intent" : state.get("t0_pre_intent")
        }
        if isinstance(s1_res, dict):
            if "intent" in s1_res:
                intent_result = s1_res["intent"]
            need_live = s1_res.get('need_live', False)
            need_replay = s1_res.get('need_replay', False)
            need_highlights = s1_res.get('need_highlights', False)
            need_score = s1_res.get('need_score', False)
            pre_intent_info["require_score"] = True if need_score else False
            pre_intent_info["require_video"] = True if (need_live or need_replay or need_highlights) else False

        if intent_result and "Search" == intent_result:
            pre_intent_info["pre_intent_result"] = "sport_news"
            # 转接ai search中的体育闲聊
            # from processor.sport_search_processor import SportSearchProcessor
            # return await SportSearchProcessor(request=self.request).engage(state=state, pre_intent_info=pre_intent_info)
            # 直接使用本地化的代码的体育闲聊
            from processor.sport_search_processor_new import SportSearchProcessor
            return await SportSearchProcessor(request=self.request, habit=habit).engage(state=state,response_header=self.response_header,pre_intent_info=pre_intent_info)
        elif intent_result and "EventQuery" == intent_result:
            pre_intent_info["pre_intent_result"] = "sport_event"
            from processor.sport_event_processor import SportEventProcessor
            return await SportEventProcessor(intent_json=s1_res, query=query, habit=habit, history=history).engage(state = state, response_header=self.response_header, pre_intent_info=pre_intent_info)
        else:
            pre_intent_info["pre_intent_result"] = "unknown"
            return StreamResponse(
                data_generator(pre_intent_info, to_notify_errors),
                media_type="text/event-stream"
            )

