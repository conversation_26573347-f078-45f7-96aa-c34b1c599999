from processor.processer_entrance import SportP<PERSON><PERSON><PERSON><PERSON>tate
from schemas.sports_agent_req import SportsAgentReq
from typing_extensions import Optional, Union
from sse_starlette.sse import EventSourceResponse as StreamResponse
from configuration import config
from loguru import logger
from schemas.sports_agent_req import LocationReq
from constance.constance_value import DEFAULT_LOCATION
from search.actions.tool_actions.serper import Ser<PERSON>
from search.actions.tool_actions.bing import Bing
from search.actions.tool_actions.tencent import Tencent
from search.actions.tool_actions.web_search import WebSearch
from search.actions.other_actions.rerank_topk import Rerank
from search.actions.tool_actions.action_excutor import ActionExecutor
from search.memory.memory_base import AllMessageMemory
from search.web_content_scrape import WebScrape, WebScrapePy
from search.llms.openai_SDK import OpenAI_LLM
from search.yield_demo import WebSearchAgent as YieldSearchAgent, WebChatQueryRequest
from schemas.models import ErrorDataModel, LocationResultModel

import json, time, asyncio

sensitive_config = config["sensitive"]
# 在线搜索接口使用
user_id = "user_online_search"

# 用于敏感词检测
model_name="kami-search"
resource="/simple/web_search/v2"

from prompts.prompt_search_intent_rewrite import rewrite_prompt_user_7b, rewrite_prompt_system_7b
router_user_prompt_from_file = rewrite_prompt_user_7b
router_system_prompt_from_file = rewrite_prompt_system_7b


selected_llm_config = config["llm_server_set"][
    config["selected_llm_server"]
]
serper_se = Serper(config["search_engine"]["serper"], "google")
bing_se = Bing(bing_config=config["search_engine"]["bing"], engine_name="bing")
tencent_se = Tencent(config=config["search_engine"]["tencent"], engine_name="tencent")
rewrite_llm_config = selected_llm_config.copy()  # shallow copy
for k in config["rewrite"].keys():  # some override parameters
    rewrite_llm_config[k] = config["rewrite"][k]


async def require_text_location(source: Optional[LocationReq], start_time: float, time_limit: float) -> LocationResultModel:
    if not source:
        return LocationResultModel(result=DEFAULT_LOCATION, time_cost=round(time.perf_counter() - start_time, 4), error_info=None)

    async def require_location(source: LocationReq):
        error_data = None
        text_location = None
        from utils.cache import rd
        key = f"text_location:{source.build_coordinate_text()}"
        try:
            text_location = await rd.get(key)
            if text_location:
                text_location = text_location.decode('utf-8')
                logger.info(f"require_location from redis succeed, location: {text_location}")
        except BaseException as e:
            logger.error(f"require_location from cache failed, e: {e}")
        if not text_location:
            from utils.goe_util import request_re_geo_rough
            text_location, error_data = await request_re_geo_rough(lat=source.lat, lon=source.lon)
            if text_location:
                logger.info(f"query from regeo location: {text_location}")
                await rd.setex(key, 3600 * 3, text_location)
        return text_location, error_data

    try:
        result, error_data = await asyncio.wait_for(require_location(source), timeout=time_limit)
        if not result:
            result = DEFAULT_LOCATION
        return LocationResultModel(result=result, time_cost=round(time.perf_counter() - start_time, 4), error_info=error_data)
    except:
        logger.error("require_text_location timeout")
        return LocationResultModel(result=DEFAULT_LOCATION, time_cost=round(time.perf_counter() - start_time, 4), error_info=None)

class SportSearchProcessor:
    def __init__(self, request: SportsAgentReq, habit: Optional[str]):
        self.request = request
        self.habit = habit

    # 选取使用的搜索引擎
    @staticmethod
    def get_search_engine_tool(request: SportsAgentReq, use_search_cache: bool) -> list:
        engine = request.engine
        if engine == 'google':
            return [WebSearch(search_engines=[serper_se], use_search_cache=use_search_cache)]
        elif engine == 'bing':
            return [WebSearch(search_engines=[bing_se], use_search_cache=use_search_cache)]
        else:
            return [WebSearch(search_engines=[tencent_se], use_search_cache=use_search_cache)]

    # 处理历史
    @staticmethod
    def process_memories(request: SportsAgentReq) -> AllMessageMemory:
        public_memory = AllMessageMemory()
        # 处理历史
        # if request.history is not None:
        #     for v in request.history:
        #         logger.debug(f"{v}  type: {type(v)}")
        #         public_memory.add_message(v.content, message_type=v.role)
        if request.messages is not None:
            # 兼容OpenAI API
            for v in request.messages[:-1]:
                public_memory.add_message(v.content, message_type=v.role)
        else:
            # 没有历史对话
            pass
        return public_memory

    # 提取当前query
    @staticmethod
    def get_query_from_request(request: SportsAgentReq) -> str:
        if request.messages and 0 < len(request.messages):
            return request.messages[-1].content
        else:
            raise ValueError('query and messages is BOTH None')
        # if request.query is not None:
        #     return request.query
        # else:
        # if request.messages and 0 < len(request.messages):
        #     return request.messages[-1].content
        # else:
        #     raise ValueError('query and messages is BOTH None')

    # 构造爬虫
    @staticmethod
    def build_crawler() -> Union[WebScrape, WebScrapePy]:
        limit_scraping_time = "800ms"
        if config["scrape"]["engine-in-use"] == "go-readability":
            return WebScrape(
                config["scrape"], limit_scraping_time=limit_scraping_time
            )
        elif config["scrape"]["engine-in-use"] == "python-document":
            return WebScrapePy()
        else:
            raise ValueError(
                f"unknown scrape engine: {config['scrape']['engine-in-use']}"
            )

    async def engage(self, state: SportProcesserState, response_header: dict, pre_intent_info: dict, to_notify_errors: Optional[list] = None) -> StreamResponse:
        start_time = state["start_time"]
        request_id = state["request_id"]
        message_id = state["message_id"]

        logger.info(f"location start")
        position_result_model = await require_text_location(self.request.location, start_time, 0.5)
        position = position_result_model.result
        time_cost_position = position_result_model.time_cost
        location_error = position_result_model.error_info

        if location_error:
            if not to_notify_errors:
                to_notify_errors = []
            to_notify_errors.append(location_error)

        logger.info(f"location end, position: {position}, time_cost_position: {time_cost_position}")

        k = self.request.k
        detect = self.request.detect
        use_search_cache = self.request.use_search_cache
        # 选取使用的搜索引擎
        tools = self.get_search_engine_tool(self.request, use_search_cache)

        # logger.info(f"selected search engine: {engine}")
        rerank_module = Rerank(k, config["rerank"]["base_url"])
        tool_executor = ActionExecutor(tools)

        # 处理历史
        public_memory = self.process_memories(self.request)
        # 提取当前query
        current_query = self.get_query_from_request(self.request)

        # 构造时生成api_key，对于nova的key，有过期时间，所以需要每条request都构造一次，生成key有cache进行加速
        rewrite_client = OpenAI_LLM(rewrite_llm_config)
        summary_client = OpenAI_LLM(selected_llm_config)
        # 构造爬虫
        crawler = self.build_crawler()

        web_chat_query_request = WebChatQueryRequest(
            query=current_query,
            message_id=message_id,
            request_id=str(request_id),
            model_name="kami-search",
            resource="/simple/web_search/v2",
            user_portrait=self.habit,
            position=position,
            start_time=start_time,
        )

        web = YieldSearchAgent(
            intent_llm=rewrite_client,
            summary_llm=summary_client,
            topk_method=rerank_module,
            action_executor=tool_executor,
            public_memory=public_memory,
            crawler=crawler,
            router_system_prompt=router_system_prompt_from_file,
            router_user_prompt=router_user_prompt_from_file,
            dojo=None,
            detect=detect,
            sensitive_config=config["sensitive"],
        )

        async def _search_generator():
            if to_notify_errors and isinstance(to_notify_errors, list):
                for error_item in to_notify_errors:
                    if isinstance(error_item, ErrorDataModel):
                        yield {
                            "type": "error",
                            "code": error_item.error_code,
                            "data": error_item.error_message,
                            "message_id": message_id
                        }
            from processor.phase_name import phase_pre_intent
            yield dict(data=json.dumps({"type": phase_pre_intent, "data": pre_intent_info}))
            first_word = True
            time_cost = {"t0-mem": state["t0_mem"], "t0-pre-intent": state["t0_pre_intent"],
                         "t1-loc": time_cost_position}
            async for result in web.chat(web_chat_query_request):
                if isinstance(result, dict) and result.get("type") != "messageEnd":
                    yield dict(data=json.dumps(result, ensure_ascii=False))
                    if isinstance(result, dict) and result.get("type") == "intent":
                        time_cost["t2-intent"] = round(time.perf_counter() - start_time, 4)
                    if isinstance(result, dict) and result.get("type") == "src":
                        time_cost["t3-src"] = round(time.perf_counter() - start_time, 4)
                    if isinstance(result, dict) and result.get("type") == "fetch":
                        time_cost["t4-fetch"] = round(time.perf_counter() - start_time, 4)
                    if isinstance(result, dict) and result.get("type") == "mft":
                        time_cost["t5-mft"] = round(time.perf_counter() - start_time, 4)
                    if isinstance(result, dict) and result.get("type") == "sft":
                        time_cost["t6-sft"] = round(time.perf_counter() - start_time, 4)
                    if isinstance(result, dict) and result.get("type") == "follow_up":
                        time_cost["t7-follow_up"] = round(time.perf_counter() - start_time, 4)
                    if result["type"] == "message":
                        if first_word is True:
                            first_word = False
                            time_cost["TTFT"] = round(time.perf_counter() - start_time, 4)
                else:
                    # yield messageEnd with time_cost summary
                    time_cost["total_time"] = round(time.perf_counter() - start_time, 4)
                    result["time_cost"] = time_cost
                    yield dict(data=json.dumps(result, ensure_ascii=False))

        return StreamResponse(_search_generator(), headers=response_header)







