from openai import OpenAI
from loguru import logger
import jwt
import time

from typing import Dict, Any, Generator

class LLM_Client:
    def __init__(self, config: Dict[str, Any]):
        self.api_key = config.get("api_key", "")
        if "ak" in config:
            self.ak = config["ak"]
        if "sk" in config:
            self.sk = config["sk"]
        self.base_url = config.get("base_url", "")
        self.model_name = config.get("model_name", "")
        self.temperature = config.get("temperature", 0.1)
        self.top_p = config.get("top_p", 0.5)
        self.max_tokens = config.get("max_tokens", 4096)
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
        )
        logger.info(f"selected llm: {self.base_url}")

    def chat(self, messages: list, model_name: str, is_stream: bool = False)-> Generator:
        response = self.client.chat.completions.create(
            model=model_name if model_name else self.model_name,
            messages=messages,
            logprobs=True,
            stream=is_stream,
            top_p=self.top_p,
            temperature=self.temperature,
            stream_options={"include_usage": True} if is_stream else None,
        )
        if is_stream:
            for chunk in response:
                chunk_dict = {}
                chunk_dict['data'] = {}
                chunk_dict['data']['choices'] = []
                chunk_delta = {}
                if chunk.choices and chunk.choices[0].delta.content is not None:
                    chunk_str = chunk.choices[0].delta.content
                    if isinstance(chunk_str, str):
                        chunk_delta['delta'] = chunk_str
                        chunk_dict['data']['choices'].append(chunk_delta)

                        yield chunk_dict
                elif chunk.usage is not None:
                    chunk_dict['data']['usage'] = dict(chunk.usage)
                    chunk_delta['delta'] = ""
                    chunk_dict['data']['choices'].append(chunk_delta)

                    yield chunk_dict
        else:
            content = response.choices[0].message.content
            yield content

    def gen_api_key_nova(ak, sk):
        headers = {"alg": "HS256", "typ": "JWT"}
        payload = {
            "iss": ak,
            "exp": int(time.time()) + 15 * 60,  # 填写您期望的有效时间，此处示例代表当前时间+15分钟
            "nbf": int(time.time()) - 5,  # 填写您期望的生效时间，此处示例代表当前时间-5秒
        }
        token = jwt.encode(payload, sk, headers=headers)
        return token
