import requests, json
from loguru import logger

class SportChatServiceClient:
    def __init__(self, chat_config: dict = None):
        self.url = chat_config['url']
        self.client_id = chat_config['client-id']

    def call_sports_chat_service(self, data: dict, stream: bool, pre_intent_info: dict):
        header = {'Content-Type': 'application/json', 'client-id': self.client_id}
        try:
            with requests.post(url=self.url, headers=header, json=data, stream=stream) as response:
                yield response.status_code, response.headers
                from processor.phase_name import phase_pre_intent
                phase_pre_intent_data = {
                    "type": phase_pre_intent,
                    "data": pre_intent_info
                }
                yield json.dumps(phase_pre_intent_data, ensure_ascii=False)
                content_type = response.headers['Content-Type']
                if response.status_code == 200 and 'json' in content_type:
                    return response.json()
                elif content_type.startswith("text/event-stream"):
                    for data_byte in response.iter_content(None):
                        if data_byte:
                            yield data_byte
                else:
                    logger.error(f'call_sports_chat_service receive abnormal response, code:{response.status_code} msg: {response.json()}')
                    return None
        except Exception as e:
            logger.error(f'call_sports_chat_service occurred error:{e.__cause__}')
            return None
