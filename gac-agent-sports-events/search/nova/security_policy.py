import time
import uuid

import httpx
import jwt
import requests
import json

import toml
from loguru import logger
from starlette.responses import JSONResponse
from dataclasses import dataclass
from typing import Optional
import asyncio


# 定义结构体类，使用 dataclass 简化
@dataclass
class TextModerationRequest:
    ak: str
    sk: str
    app_id: str
    app_user_id: str
    text: str
    event_type: str
    session_id: str
    text_id: Optional[str] = None
    finish_detect: Optional[bool] = None
    ext_info: Optional[dict] = None


class JWTTokenCache:
    def __init__(self):
        self.token = None
        self.expiration_time = 0

    def get_token(self, ak, sk):
        # 检查是否需要重新生成 token
        if self.token is None or time.time() >= self.expiration_time:
            self.token = self._generate_token(ak, sk)
        return self.token

    def _generate_token(self, ak, sk):
        headers = {"alg": "HS256", "typ": "JWT"}
        # 有效期设置为30分钟
        payload = {
            "iss": ak,
            "exp": int(time.time()) + 1800,
            "nbf": int(time.time()) - 5,
        }
        token = jwt.encode(payload, sk, headers=headers)
        self.expiration_time = int(time.time()) + 1800  # 更新过期时间
        return token


# Token 缓存实例
token_cache = JWTTokenCache()


# 函数接收一个结构体类的实例
async def moderate_text(request: TextModerationRequest):
    # 获取 JWT Token，可能使用缓存
    authorization = token_cache.get_token(request.ak, request.sk)

    url = "https://api.sensenova.cn/v1/moderations/text"

    # 构建 payload
    payload = {
        "app_id": request.app_id,
        "app_user_id": request.app_user_id,
        "text": request.text,
        "event_type": request.event_type,
        "session_id": request.session_id,
    }
    if request.text_id is not None:
        payload["text_id"] = request.text_id
    if request.finish_detect is not None:
        payload["finish_detect"] = request.finish_detect  # type: ignore

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {authorization}",
    }

    try:
        # 发送异步请求
        async with httpx.AsyncClient(verify=False) as client:
            response = await client.post(url, headers=headers, json=payload)
            response.raise_for_status()  # 检查 HTTP 状态码
            res = response.json()
            logger.debug(f"API Response: {res}")
            return res
    except httpx.RequestError as e:
        logger.error(f"Request failed: {e}")
        return {"error": str(e)}  # 返回包含错误信息的字典
    except ValueError:
        logger.error("Failed to parse JSON response")
        return {"error": "Invalid JSON response"}


# 输入内容审核函数
async def moderate_text_generic(
    request_id: str, request: TextModerationRequest, mode: str
) -> bool:
    res = await moderate_text(request)

    # 检查 API 响应
    if res.get("error"):
        logger.error(f"request_id: {request_id} Error: {res['error']}")
        return True

    # 审核结果判断
    decision = res.get("decision")
    if decision == "PASS":
        logger.debug(f"request_id: {request_id} {mode}不存在敏感词")
        return False
    else:
        logger.info(f"request_id: {request_id}  {decision} {mode}存在敏感词")
        return True


async def stream_output_moderate_text(request_id: str, request: TextModerationRequest):
    res = await moderate_text(request)

    # 检查 API 响应
    if res.get("error"):
        logger.error(f"request_id: {request_id} Error: {res['error']}")
        return True

    # 审核结果判断
    decision = res.get("decision")
    tokens = res.get("tokens")
    if decision == "PASS":
        logger.debug(f"request_id: {request_id} 输出不存在敏感词")
        return decision, tokens
    elif decision == "Further":
        logger.debug(f"request_id: {request_id}  {decision}输出存在敏感词:{tokens}")
        return decision, tokens
    else:
        logger.info(f"request_id: {request_id}  {decision}输出存在敏感词:{tokens}")
        return decision, tokens


if __name__ == "__main__":
    config = toml.load("../config.toml")

    request_data = TextModerationRequest(
        config["nova"]["ak"],
        config["nova"]["sk"],
        config["nova"]["app_id"],
        "123",
        "从圆明园到天安门的距离大约为15公里左右。如果您选择乘坐地铁，可以参考以下路线：\n\n1. **从圆明园乘坐地铁4号线大兴线**，经过13站，到达西单站。\n2. **步行约400米**，换乘地铁1号线。\n3. **乘坐地铁1号线**，经过2站，到达天安门东站。\n4. **步行大约1.0公里**，到达天安门广场。\n\n整个行程大约需要1小时左右，具体时间可能会因地铁班次和换乘时间而有所不同。如果您选择打车，路程大约需要30-40分钟，具体时间取决于路况。建议您根据实际情况选择合适的出行方式,xijinping。,",
        "LLMPrompt",
        "123",
    )
    res = asyncio.run(moderate_text(request_data))
    if res["decision"] == "PASS":
        print(res["decision"])
    else:
        print("存在敏感词")
