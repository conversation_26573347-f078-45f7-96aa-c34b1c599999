# import qianfan
import inspect
import os
import re
import time
from typing import Dict, List, Any, Tuple

# def stage0_parse_intent(query: str, llm_json_output: str):
#     """
#     Parses the JSON output from an LLM to extract the "Action_input" list.
#
#     Args:
#         query (str): The query string associated with the LLM output.
#         llm_json_output (str): The JSON output string from the LLM.
#             Example:
#             '{"Action_input": ["item1", "item2", "item3"]}'
#
#     Returns:
#         dict: A dictionary with the query as the key and the extracted list as the value.
#             Example:
#             {'some query': ['item1', 'item2', 'item3']}
#     """
#     cleaned_string = re.sub(r"\\n|\\", "", llm_json_output).strip("'\"")
#
#     if match := re.search(r'"Action_input":\s*\[(.*?)\]', cleaned_string, re.DOTALL):
#         if action_input_str := match.group(1):
#             action_input_list = [item.strip().strip('"') for item in action_input_str.split(",")]
#         else:
#             action_input_list = []
#     else:
#         action_input_list = []
#     return {query: action_input_list}


def cal_time(description):
    def decorator(func):
        if inspect.iscoroutinefunction(func):

            async def wrapper(*args, **kwargs):
                start_time = time.perf_counter()
                result = await func(*args, **kwargs)
                end_time = time.perf_counter()
                elapsed_time = end_time - start_time
                print(f"{description} runs {elapsed_time:.4f} sec")
                return result
        else:

            def wrapper(*args, **kwargs):
                start_time = time.perf_counter()
                result = func(*args, **kwargs)
                end_time = time.perf_counter()
                elapsed_time = end_time - start_time
                print(f"{description} runs {elapsed_time:.4f} sec")
                return result

        return wrapper

    return decorator


def extract_tsl_query_with_link(query_with_link):
    titles = []
    snippets = []
    links = []
    dates = []
    for key, value in query_with_link.items():
        key_titles = []
        key_snippets = []
        key_links = []
        key_dates = []
        for subkey, subvalue in value.items():
            key_titles.append(subvalue["title"])
            key_snippets.append(subvalue["snippet"])
            key_links.append(subvalue["link"])

            key_dates.append(subvalue["date"])
        titles.append(key_titles)
        snippets.append(key_snippets)
        links.append(key_links)
        dates.append(key_dates)
    return titles, snippets, links, dates


def adjust_ranking_sum_to_list_minus_one(ranking_res, links_lens):
    result = []
    keys = list(ranking_res.keys())
    result.extend([int(x) - 1 for x in ranking_res[keys[0]]])
    cumulative_sum = 0
    for i in range(1, len(keys)):
        cumulative_sum += links_lens[i - 1]
        adjusted_values = [int(x) + cumulative_sum - 1 for x in ranking_res[keys[i]]]
        result.extend(adjusted_values)
    return result


def format_query_with_link(query_with_link):
    """
    Formats format_query_with_link_ into two specific string formats.

    Args:
        query_with_link (dict): Dictionary containing stock data with nested dictionaries.

    Returns:
        tuple: A tuple containing two formatted strings.

    Example:
        Input:
        {
            '商汤 市值': {
                1: {'title': '商汤-W(00020) ...', 'link': 'http://stock.finance.sina.com.cn/hkstock/quotes/00020.html', 'snippet': "xxx"},
                2: {'title': 'W (00020)_个股概要_股票价格_行情走势图- 商汤 - Moomoo', 'snippet': '00020 商汤-W. 未开盘08/06 16:08 (北京). .'}
            },
            '腾讯 市值': {
                1: {'title': '腾讯-W(00020) ...', 'link': 'http://stock.finance.sina.com.cn/hkstock/quotes/00020.html', 'snippet': "aaaa"},
                2: {'title': 'W (00020)_个股概要_股票价格_行情走势图- 商汤 - Moomoo', 'snippet': '00020 腾讯-W. 未开盘08/06 16:08 (北京). .'}
            }
        }

        Output:
        Result 1:
        [1]: xxx
        [2]: 00020 商汤-W. 未开盘08/06 16:08 (北京). .
        [3]: aaaa
        [4]: 00020 腾讯-W. 未开盘08/06 16:08 (北京). .
    """

    result1 = []
    idx = 1
    for stock_key, entries in query_with_link.items():
        for entry in entries.values():
            snippet = entry.get("snippet", "")
            result1.append(f"[{idx}]: {snippet.strip()}")
            idx += 1
    result1_str = "\n".join(result1)
    return result1_str


def stage0_parse_intent(query: str, llm_str_output: str):
    if "[" in llm_str_output:
        pattern = r'"(.*?)"'
        matches = re.findall(pattern, llm_str_output)
        return {query: matches}

    else:
        if llm_str_output == "null":
            return {query: []}
        elif "|" in llm_str_output:
            return {query: llm_str_output.split("|")}
        else:
            return {query: [llm_str_output]}


def stage1_format2query_with_link(data, cached_link_first=True, include_title=False):
    """
    Merges search engine data and extracts specific fields with optional processing.

    Parameters:
        data (dict): The input data containing search results from multiple engines.
        cached_link_first (bool):
            - If True, final_links_data prioritizes 'cached_link' over 'link'.
            - If False, final_links_data contains only 'links'.
        include_title (bool):
            - If True, snippets_data contains concatenated 'title' and 'snippet'.
            - If False, snippets_data contains only 'snippets'.

    Returns:
        tuple:
            merged_data (dict): Merged search results indexed by query and entry index.
            snippets_data (dict):
                - If include_title=True: Concatenated 'title' and 'snippet'.
                - If include_title=False: Only 'snippets'.
            final_links_data (dict):
                - If cached_link_first=True: Prioritized 'cached_link' or 'link'.
                - If cached_link_first=False: Only 'links'.
    """
    block_keywords_in_url = ["bilibili", "youtube", "v.qq.com"]
    merged_data = {}
    fields_data = {}

    existing_links = set()  # Global set to ensure link uniqueness across all queries

    # Initialize final_links_data based on cached_link_first
    final_links_data = {}

    for query, engines in data.items():
        merged_entry = {}
        fields_entry = {
            "titles": [],
            "links": [],
            "cached_link": [],
            "snippets": [],
            "dates": [],
        }

        combined_entries_unique = []

        for engine_key, engine in engines.items():
            if not engine:  # Skip if the search engine data is empty
                continue

            for key, item in engine.items():
                item_link = item.get("link", "").strip()

                # Check if the link contains any blocked keywords (case-insensitive)
                if item_link:
                    link_lower = item_link.lower()
                    blocked = any(
                        keyword.lower() in link_lower
                        for keyword in block_keywords_in_url
                    )
                else:
                    blocked = False  # No link means it's not blocked

                # If the link is blocked, print it
                if blocked:
                    # print(f"Blocked link: {item_link}")
                    pass

                # If the link is already in existing_links, print it
                if item_link in existing_links:
                    # print(f"Link already in existing_links: {item_link}")
                    pass

                if item_link and item_link not in existing_links and not blocked:
                    existing_links.add(item_link)
                    combined_entries_unique.append(item)

                    # Extract and append fields
                    fields_entry["titles"].append(item.get("title", "").strip())
                    fields_entry["links"].append(item_link)
                    fields_entry["cached_link"].append(
                        item.get("cached_link", "").strip()
                    )
                    fields_entry["snippets"].append(item.get("snippet", "").strip())
                    fields_entry["dates"].append(item.get("date", "").strip())

        # Assign each unique entry an incremental index
        for idx, item in enumerate(combined_entries_unique):
            merged_entry[idx] = item

        merged_data[query] = merged_entry
        fields_data[query] = fields_entry

        # Compute final_links_data based on cached_link_first
        if cached_link_first:
            # Prioritize cached_link over link
            final_links = []
            for link, cached in zip(fields_entry["links"], fields_entry["cached_link"]):
                if cached:
                    final_links.append(cached)
                elif link:
                    final_links.append(link)
                else:
                    final_links.append(None)  # Placeholder for missing links
            final_links_data[query] = final_links
        else:
            # Use links directly
            final_links = fields_entry["links"]
            final_links_data[query] = final_links

    # Prepare snippets_data based on include_title
    snippets_data = {}
    for query, fields in fields_data.items():
        if include_title:
            # Concatenate title and snippet
            concatenated = [
                f"标题: {title} 摘要: {snippet}"
                for title, snippet in zip(fields["titles"], fields["snippets"])
            ]
            snippets_data[query] = concatenated
        else:
            # Use snippets only
            snippets_data[query] = fields["snippets"]
    return merged_data, fields_data, snippets_data, final_links_data


def stage3_format_rest_query_with_link(query_with_link, len_front, keep_title=True):
    """
    Format search results into a single string.

    Args:
        query_with_link (dict): Dictionary of queries with their search results.
            Example:
            {
                '今日 奥运会 消息': {
                    1: {
                        'title': 'Olympic Highlights',
                        'snippet': 'The latest updates on the Tokyo Olympics.',
                        'link': 'https://example.com/olympics'
                    },
                    2: {
                        'title': 'Olympic Medals',
                        'snippet': 'Current medal standings for the Tokyo Olympics.',
                        'link': 'https://example.com/medals'
                    }
                },
                '今日 足球 消息': {
                    1: {
                        'title': 'Football Results',
                        'snippet': 'Results from the latest football matches.',
                        'link': 'https://example.com/football'
                    }
                }
            }

        keep_title (bool): Whether to include titles in the formatted output. Default is True.

    Returns:
        str: A single string with formatted search results.
            Example:
            "title: Olympic Highlights, snippet: The latest updates on the Tokyo Olympics.\n
            title: Olympic Medals, snippet: Current medal standings for the Tokyo Olympics.\n
            title: Football Results, snippet: Results from the latest football matches."
    """
    formatted_results = []

    index = len_front + 1
    for query in query_with_link:
        for i, sub_key in enumerate(query_with_link[query]):
            if index >= 30:
                return "\n".join(formatted_results)

            # title_snippet = (
            #     f"[{index}]: 网页标题-{query_with_link[query][sub_key]['title']}, " if keep_title else ""
            # )
            snippet = query_with_link[query][sub_key]["snippet"]

            if len(snippet) <= 0:
                continue
            date = query_with_link[query][sub_key]["date"]
            if not date:
                continue
            # date = "No publish date"

            formatted_results.append(
                f"[{index}]: 网页发布时间为-{date}; 网页摘要为-{snippet} \n"
            )
            index += 1

    return "\n".join(formatted_results)


def stage3_format_web_fetch(
    query_scrape_res: Dict[str, List[List[str]]],
    topk_query_with_link: Dict[str, Dict[int, Dict[str, Any]]],
    rest_query_with_link: Dict[str, Dict[int, Dict[str, Any]]],
    truncate_length: int = 3000,
    rest_k: int = 5,
) -> Tuple[str, str]:
    """
    Formats web scraping results into two structured strings: one for top-k links and another for the remaining links.

    Args:
        query_scrape_res (Dict[str, List[List[str]]]):
            example:
            {"rewrite1":[{"url":"", "cleaned_text", ""}, {"url":"", "cleaned_text", ""}...]}

            A dictionary mapping each query to a list of scraping results.
        topk_query_with_link (Dict[str, Dict[int, Dict[str, Any]]]):
            example:
            {"rewrite1":{N1(maybe 1 ): {"title":"", "snippet":"", "date"....}}}

            A dictionary mapping each query to its top-k related link results.
        rest_query_with_link (Dict[str, Dict[int, Dict[str, Any]]]):

            A dictionary mapping each query to its remaining related link results.
        truncate_length (int, optional):
            The maximum length for the `snippet`. Defaults to 3000.
        rest_k (int, optional):
            The maximum number of links per query to include in rest_llm_ref. Defaults to 5.

    Returns:
        Tuple[str, str]:
            A tuple containing two formatted strings:
            - `llm_ref`: Formatted top-k links with detailed content.
            - `rest_llm_ref`: Formatted remaining links with brief content.
    """
    llm_ref_parts = []
    rest_llm_ref_parts = []

    def format_search_info(search_info: Dict, idx, web_content):
        title = search_info.get("title", "").strip() or "无标题"
        snippet = search_info.get("snippet", "").strip() or "无摘要"
        publish_date = search_info.get("date", "").strip() or "无发布日期"
        source_from = search_info.get("source_from", "").strip() or "无来源"

        formatted_entry = f"[{idx}]: 网页标题-{title}; 发布机构-{source_from}; 网页发布日期-{publish_date}; {web_content}-{snippet}"
        return formatted_entry

    # logger.error(query_scrape_res)
    # print()
    # logger.error(topk_query_with_link)
    # print()
    # logger.error(rest_query_with_link)
    idx = 1
    failed_scrape = {}

    for rewrite_query, rank_info in topk_query_with_link.items():
        for topk_num, search_info in rank_info.items():
            topk_clawered_text = query_scrape_res[rewrite_query][topk_num][
                "cleaned_text"
            ]
            if topk_clawered_text:
                topk_query_with_link[rewrite_query][topk_num]["snippet"] = (
                    topk_clawered_text[:truncate_length]
                )
                llm_ref_parts.append(
                    format_search_info(search_info, idx, "网页详细内容")
                )
                idx += 1
            else:
                if rewrite_query not in failed_scrape:
                    failed_scrape[rewrite_query] = {}
                failed_scrape[rewrite_query][topk_num] = search_info

    for rewrite_query, rank_info in failed_scrape.items():
        for topk_num, search_info in rank_info.items():
            rest_llm_ref_parts.append(
                format_search_info(search_info, idx, "网页摘要内容")
            )
            idx += 1

    # for rewrite_query,rank_info in rest_query_with_link.items():
    #     for topk_num, search_info in rank_info.items():
    #         rest_llm_ref_parts.append(format_search_info(search_info,idx,"网页摘要内容"))
    #         idx+=1

    if rest_k:
        for rewrite_query, rank_info in rest_query_with_link.items():
            count = 0
            for topk_num, search_info in rank_info.items():
                if count <= rest_k:
                    rest_llm_ref_parts.append(
                        format_search_info(search_info, idx, "网页摘要内容")
                    )
                    idx += 1
                    count += 1
                else:
                    break

    llm_ref = "\n".join(llm_ref_parts) + "\n" if llm_ref_parts else ""
    rest_llm_ref = "\n".join(rest_llm_ref_parts) + "\n" if rest_llm_ref_parts else ""

    return llm_ref, rest_llm_ref


# from zhipuai import ZhipuAI
#
# zhipu_client = ZhipuAI(api_key="5f3e64f00d4619cee086a508bcdaae4b.rqYkx2nQhUf90QRM")
#

# def zhipu(query):
#     """
#     Uses the Zhipu API to perform a web search and generate a response based on the query.
#
#     Args:
#         query (str): The query string to be sent to the Zhipu API.
#             Example: "巴黎奥运会第九日奖牌榜"
#
#     Returns:
#         tuple: A tuple containing the LLM response and the reference response from the web search.
#             Example:
#             (
#                 "LLM response content",
#                 [
#                     {
#                         'content': '随着巴黎奥运会第九个比赛日的落幕...',
#                         'icon': 'https://sfile.chatglm.cn/searchImage/bbs_hupu_com_icon.jpg',
#                         'link': 'https://bbs.hupu.com/627492571.html',
#                         'media': '虎扑社区',
#                         'refer': 'ref_1',
#                         'title': '巴黎奥运第九日奖牌榜：中国军团19金15银11铜排名第二（发布时间：2024-08-05 06:03:49）'
#                     },
#                     {
#                         'content': '巴黎奥运会赛程已经过半...',
#                         'icon': 'https://sfile.chatglm.cn/searchImage/k_sina_com_cn_icon.jpg',
#                         'link': 'https://k.sina.com.cn/article_5970122892_163d8d88c001028k1i.html',
#                         'media': '新浪',
#                         'refer': 'ref_2',
#                         'title': '巴黎奥运最新奖牌榜：中国19枚金牌排名第二，美国首次升到榜首（发布时间：2024-08-05 23:38:59）'
#                     }
#                 ]
#             )
#     """
#     tools = [{
#         "type": "web_search",
#         "web_search": {
#             "enable": True,
#             "search_result": True  # 禁用False，启用：True，默认为禁用
#         }
#     }]
#
#     messages = [{
#         "role": "user",
#         "content": query
#     }]
#
#     response = zhipu_client.chat.completions.create(
#         model="glm-4-flash",
#         messages=messages,
#         tools=tools
#     )
#     llm_response = response.choices[0].message.content
#     ref_response = response.web_search
#     return llm_response, ref_response


def add_up_snippet(data_list, content_idx, link_idx):
    """
    Adds new snippets from data_list to the existing content and link indices.

    Args:
        data_list (list): List of dictionaries containing new snippets to be added.
            Example:
            [
                {'title': 'New Title1', 'content': 'New Content1', 'link': 'New Link1'},
                {'title': 'New Title2', 'content': 'New Content2', 'link': 'New Link2'}
            ]
        content_idx (str): Existing content index string to be appended.
            Example:
            "[1]: Existing Content1\n\n[2]: Existing Content2\n\n"
        link_idx (str): Existing link index string to be appended.
            Example:
            "[1]: Existing Title1 Existing Link1\n[2]: Existing Title2 Existing Link2\n"

    Returns:
        tuple: Updated content and link index strings.
            Example:
            (
                "[1]: Existing Content1\n\n[2]: Existing Content2\n\n[3]: New Content1\n\n[4]: New Content2\n\n",
                "[1]: Existing Title1 Existing Link1\n[2]: Existing Title2 Existing Link2\n[3]: New Title1 New Link1\n[4]: New Title2 New Link2\n"
            )
    """
    start_idx = int(re.findall(r"\[(\d+)\]", content_idx)[-1]) + 1

    for idx, item in enumerate(data_list[start_idx:], start=start_idx):
        content_idx += f"[{idx}]: {item.get('content', '')} \n \n"
        link_idx += f"[{idx}]: {item.get('title', '')}{item.get('link', '')} \n"
    return content_idx.strip(), link_idx.strip()
