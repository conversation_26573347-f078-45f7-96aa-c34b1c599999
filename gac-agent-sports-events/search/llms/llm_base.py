from abc import ABC, abstractmethod
from typing import List, Optional, Union, Dict, AsyncGenerator
from search.memory.memory_base import MemoryBase


class LLMBase(ABC):
    def __init__(
        self,
    ):
        super().__init__()
        self.memory: Union[MemoryBase, None] = None

    @abstractmethod
    async def chat(
        self,
        messages: List[Dict[str, str]],
        stop_words: Optional[List[str]] = None,
        stream=False,
    ) -> str:
        raise NotImplementedError

    @abstractmethod
    async def yield_chat(
        self, query: str, stop_words: Optional[List[str]] = None
    ) -> str:
        raise NotImplementedError

    @abstractmethod
    async def yield_chat_from_db(
        self,
        messages: List[Dict[str, str]],
        stop_words: Optional[List[str]] = None,
        guiding_res: Optional[str] = None,
    ) -> AsyncGenerator[str, None]:
        raise NotImplementedError

    def truncate_string(self, s):
        start_index_current_issue = s.find("当前问题:")
        if start_index_current_issue == -1:
            return s
        current_issue_text = s[start_index_current_issue:]
        return current_issue_text
