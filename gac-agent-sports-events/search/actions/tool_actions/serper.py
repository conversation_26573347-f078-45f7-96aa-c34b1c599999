import json
from search.actions.tool_actions.web_search_base import WebSearchBase
from typing import Optional
import aiohttp


class Serper(WebSearchBase):
    def __init__(self, config, engine_name="google"):
        super().__init__()
        if engine_name == "google":
            self.engine_name = "Serper_google"
            self.url = "https://google.serper.dev/search"
        elif engine_name == "bing":
            self.engine_name = "Serper_bing"
            self.url = "https://bing.serper.dev/search"
        self.headers = {"X-API-KEY": config["key"], "Content-Type": "application/json"}

    async def search(self, query: str, engine: Optional[str] = None):
        serper_settings = {
            "q": query,
            "num": 20,
            "gl": "cn",
            "hl": "zh-cn",
            "autocorrect": True,
        }
        payload = json.dumps(serper_settings)
        async with aiohttp.ClientSession() as session:
            async with session.post(
                self.url, headers=self.headers, data=payload
            ) as response:
                response_json = await response.json()
                if "statusCode" not in response_json.keys():
                    return self.format_search_res(response_json)
                else:
                    return str(response_json)

    def format_search_res(self, search_res):
        result = {}
        answer_box = search_res.get("answerBox")
        if answer_box:
            snippet = answer_box.get("snippet", "")
            answer = answer_box.get("answer", "")
            combined_snippet = snippet
            if answer:
                combined_snippet += f" {answer}"

            result["answerBox"] = {
                "title": answer_box.get("title", ""),
                "snippet": combined_snippet,
                "link": answer_box.get("link", ""),
                "date": answer_box.get("date", ""),
            }

        organic_results = search_res.get("organic", "")
        if organic_results:
            for idx, entry in enumerate(search_res["organic"], start=1):
                result[idx] = {
                    "title": entry.get("title", ""),
                    "snippet": entry.get("snippet", ""),
                    "link": entry.get("link", ""),
                    "date": entry.get("date", ""),
                }
        return result
