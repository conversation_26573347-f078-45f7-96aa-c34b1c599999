import aiohttp
from loguru import logger

# from service.logger_manager import fire_logger
from search.actions.tool_actions.web_search_base import WebSearchBase


class Bing(WebSearchBase):
    def __init__(self, bing_config, engine_name="bing"):
        super().__init__()
        if engine_name == "bing":
            self.engine_name = "bing"
            self.bing_url = bing_config["url"]
            self.header = {
                "Content-Type": "application/json",
                "Authorization": "Bearer " + bing_config["key"],
            }

    async def search(self, query: str, search_num: int):
        async with aiohttp.ClientSession() as session:
            async with session.get(
                    url=f"{self.bing_url}?q={query}&count={search_num}", headers=self.header
            ) as response:
                logger.info(
                    f"bing search finish: status {response.status}"
                )
                if response.status == 200:
                    response_json = await response.json()
                    return self.format_search_res(response_json)
                else:
                    return {
                        "error": f"Response failed with status code {response.status}"
                    }

    def format_search_res(self, search_res: dict):
        # print(search_res)
        result = {}
        result["answerBox"] = {
            "title": "",
            "snippet": "",
            "link": "",
            "cached_link": "",
            "date": "",
            "source_from": "",
        }
        organic_results = search_res.get("webPages", "")
        if organic_results:
            for idx, entry in enumerate(search_res["webPages"]["value"], start=1):
                date = entry.get("datePublished", "")
                if date and "T" in date:
                    date = date.split("T")[0]
                result[idx] = {
                    "title": entry.get("name", ""),
                    "snippet": entry.get("snippet", ""),
                    "link": entry.get("url", ""),
                    "cached_link": entry.get("cachedPageUrl", ""),
                    "date": date,
                    "source_from": entry.get("white_src", ""),
                }
        return result
