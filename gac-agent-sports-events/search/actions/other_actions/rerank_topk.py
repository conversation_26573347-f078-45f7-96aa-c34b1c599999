import requests
from typing import Dict
from search.actions.other_actions.topk_base import TopkBase
from loguru import logger
import copy,time
from main import global_config


class Rerank(TopkBase):
    def __init__(self, k, base_url):
        super().__init__()
        self.rerank_url = base_url
        self.k = k
        self.api_key = global_config['rerank']['api_key']

    def ranking(self, query_with_link: dict, query_snippets: dict):
        # logger.info(f"query_with_link: {query_with_link}")
        start_time = time.perf_counter()
        rerank_results = []
        for query, docs in query_snippets.items():
            rerank_results.append(self.rerank_documents(query, docs))
        topk_res = self.top_k_select(rerank_results, self.k)
        topk_query_with_link, query_with_link_copy = self.post_format(query_with_link, topk_res)
        logger.info(f'rerank response took {round(time.perf_counter() - start_time, 4)}s')
        return topk_query_with_link, query_with_link_copy


    def pre_format(self, query_with_link: Dict):
        pass

    def post_format(self, query_with_link, indices_list):
        query_with_link_copy = copy.deepcopy(query_with_link)
        topk_query_with_link = {}
        for query, query_tops in zip(query_with_link_copy.keys(), indices_list):
            topk_query_with_link[query] = {}
            for top in query_tops:
                int_p = int(top)
                topk_query_with_link[query][int_p] = query_with_link_copy[query][int_p]
                del query_with_link_copy[query][int_p]
        return topk_query_with_link, query_with_link_copy  # rest of topk

    def top_k_select(self, rerank_results, k, threshold=0.60):
        selected_indices = []

        for results in rerank_results:
            top_k_results = results[:k]

            filtered_results = [
                item["index"] for item in top_k_results if item["relevance_score"] >= threshold
            ]
            selected_indices.append(filtered_results)

        return selected_indices

    def rerank_documents(self, query: str, documents: list) -> list:
        # logger.info(f"self.rerank_url: {self.rerank_url}")
        response = requests.post(
            url=self.rerank_url,
            json={"query": query, "documents": documents},
            headers={"Content-Type": "application/json", "Accept": "application/json",
                     "Authorization": "Bearer " + self.api_key},
            verify=False
        )
        if response.status_code == 200:
            data = response.json()['results']
        else:
            logger.error(f"Error: {response.status_code}, {response.json()}")
            data = []
        return data
