from pydantic import BaseModel
from typing_extensions import Union, Optional
from pydantic import BaseModel, ConfigDict, model_validator

class Message(BaseModel):
    role: str = None
    content: str = None

class LocationReq(BaseModel):
    lat: str
    lon: str

class UserMemoryReq(BaseModel):
    car_id: str
    user_id: str
    category: list[str] = []

class OptionParam(BaseModel):
    filter_time_for_live: bool = True

class SportsAgentReq(BaseModel):
    stream: bool = False
    detect: bool = False
    k: int = 10
    engine: str = ""
    messages: list[Message] = None
    use_search_cache: Optional[bool] = False
    user_info: Optional[UserMemoryReq] = None
    location: Optional[LocationReq] = None
    option_param: Optional[OptionParam] = None
    # model_config = ConfigDict(protected_namespaces=())
