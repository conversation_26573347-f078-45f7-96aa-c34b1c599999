from openai import OpenAI
import json
import re
import time
from datetime import datetime
from sample.migu_api_sample import test_get_competition_info, test_query_matches, get_game_score_new
from urllib.parse import quote


def client_openai(messages: list):
    openai_api_key = "1s3963nw8802M4O55yMuU6x37tOYQ682"
    openai_api_base = "http://58.22.103.26:8099/v1"
    model_name = "SenseAuto-Chat"
    client = OpenAI(
        api_key=openai_api_key,
        base_url=openai_api_base,
    )
    # print('------------------------begin')
    # print(messages)
    # print('-------------------------end')
    response = client.chat.completions.create(
        model=model_name,
        messages=messages,
        logprobs=True,
        top_p=0.8,
        temperature=0.6,
    )
    content = response.choices[0].message.content
    return content

def to_timestamp(t):
    if t == '':
        return None
    if int(t) < 365:
        return time.time()+t*3600*24
    else:
        dt_obj = datetime.strptime(t, "%Y%m%d")
        # 转换为 Unix 时间戳
        unix_time = int(dt_obj.timestamp())
        return unix_time

def extract_json_from_text(text):
    # 匹配 ```json ... ``` 块
    match = re.search(r"```json\s*(\{.*?\}|\[.*?\])\s*```", text, re.DOTALL)
    
    if match:
        json_str = match.group(1)
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            print("ERROR:解析失败：", e)
            print(json_str)
    else:
        print("ERROR:没有找到 json 块", text)
    return None


def url_encode(id, type='live'):
    assert type in ['live', 'replay', 'highlights']
    base_url = "miguvideocar://miguvideo?action="
    if type == 'live':
        params = {
            "type":"JUMP_DETAIL_PAGE", # 跳转播放详情页面
            "params": {
                "mgdbId": id, # 挂件id
                "programTypeV2": "LIVE" # 固定值，正在直播的节目传
            }
        }
    elif type == 'replay' or type == 'highlights':
        params = {
            "type":"JUMP_DETAIL_PAGE", # 跳转播放详情页面
            "params": {
                "contentID": id, # 节目id
            }
        }
    return base_url + quote(json.dumps(params))


if __name__ == "__main__":
    # 1. intent and rewrite
    intent_rewrite_prompt = open('intent_rewrite_prompt.txt', 'r', encoding='utf-8').read()
    querys = [
        # "世俱杯现在有比赛吗"
        # "帮我找一场在直播的足球比赛"
        "播放一下切尔西的上一场比赛",
        # "巴萨上一场比赛结果如何",
        # "皇马上一场比赛结果如何",
        # "现在有没有足球比赛？",
        # "姆巴佩最近状态如何？",
        # "詹姆斯哪年进入NBA的？"
        # "今天股票咋样"
    ]
    user_profile = "喜欢皇马"
    history_matches_info = []
    history_qa = ""
    live_links = []
    replay_links = []
    highlights_links = []
    for query in querys:
        print('DEBUG: Query:', query)
        messages = [
            {"role": "system", "content": intent_rewrite_prompt},
            {"role": "user", "content": json.dumps({"user_profile": user_profile, "conversation_history": history_qa, "current_query": query}, ensure_ascii=False, indent=4)}
        ]
        print('DEBUG: rewrite messages:', messages[1])
        s1_res = client_openai(messages)
        s1_res = extract_json_from_text(s1_res)
        
        print('intent and rewrite res:', s1_res)
        
        if s1_res['intent'] == 'EventQuery':
            # 2. get all competitions
            competitions = json.loads(test_get_competition_info().result)
            print('DEBUG: all competitions:', competitions)
            competitions_filter = []
            sport_type = s1_res['sport_type']
            for item in competitions['data']:
                if sport_type in item['category']:
                    competitions_filter += item['competitions']
            print('DEBUG: competitions_filter:', competitions_filter)
            # 3. get all games
            matches = []
            for competition in competitions_filter:
                start_time, end_time = None, None
                if 'start_date' in s1_res:
                    start_time = to_timestamp(s1_res['start_date'])
                    if start_time:
                        print(datetime.fromtimestamp(start_time))
                if 'end_date' in s1_res:
                    end_time = to_timestamp(s1_res['end_date'])
                    if end_time:
                        print(datetime.fromtimestamp(end_time))
                match_info = test_query_matches(competition_name=competition,
                                start_time=start_time,
                                end_time=end_time,
                                match_name=s1_res['team_name'] if 'team_name' in s1_res else None,
                                size=s1_res['match_count'] if 'match_count' in s1_res else None,
                                contains_highlights=s1_res['need_highlights'] if 'need_highlights' in s1_res else None,
                                contains_playback=s1_res['need_replay'] if 'need_replay' in s1_res else None) 
                matches += json.loads(match_info.result)['data']
                # print(match_info)
            print('DEBUG: len_matches:', len(matches))
            need_live = s1_res.get('need_live', False)
            need_replay = s1_res.get('need_replay', False)
            need_highlights = s1_res.get('need_highlights', False)
            need_score = s1_res.get('need_score', False)
            matches_info = []
            for match in matches:
                # print("DEBUG: match:",match)
                if need_live:
                    current_time = time.time()
                    if current_time < match['liveStartTime']  or current_time > match['liveEndTime']:
                        continue
                    live_link = url_encode(match['pendantId'])
                    live_links.append(live_link)
                match_info = {}
                match_info['matchTitle'] = match['matchTitle']
                match_info['matchStartTime'] = datetime.fromtimestamp(match['matchStartTime']).strftime("%Y-%m-%d %H:%M:%S")
                match_info['phase'] = match['phase']
                match_info['matchGroup'] = match['matchGroup']
                match_info['round'] = match['round'] if 'round' in match else None
                match_info['teams'] = [{"teamName": item['name']} for item in match['confrontTeams']]
                if need_score:
                    pendant_id = match['pendantId']
                    score_info = json.loads(get_game_score_new([pendant_id]).result)
                    # print('score_info:', score_info)
                    for score_item in score_info['body']:
                        for i in range(len(match_info['teams'])):
                            if score_item['teamName'] == match_info['teams'][i]['teamName']:
                                match_info['teams'][i]['score'] = score_item['score']

                # 目前信源中都没有回放和集锦，这里暂时fake
                if need_replay:
                    replay_link = 'fake_replay_url' 
                    replay_links.append(replay_link)
                if need_highlights:
                    highlights_link = 'fake_replay_url' 
                    highlights_links.append(highlights_link)
                matches_info.append(match_info)
            print('DEBUG: matches info:', matches_info)
            if len(matches_info) == 0:
                res = '没有找到对应的比赛信息'
                print(res)
                history_qa += "用户：" + query + '\n'
                history_qa += "助手：" + res + '\n'
                continue
            if not need_score and (need_live or need_replay or need_highlights):
                jump_prompt = open('jump_prompt.txt', 'r').read()
                messages = [
                    {"role": "system", "content": jump_prompt},
                    {"role": "user", "content": json.dumps({"user_profile": user_profile, "conversation_history": history_qa, "match_data": json.dumps(matches_info, ensure_ascii=False), "current_query": query}, ensure_ascii=False, indent=4)}
                ]
                print('DEBUG: messages:', messages[1])
                jump_res = client_openai(messages)
                print('DEBUG: jump res:', jump_res)
                index = int(jump_res)
                res = "正在为您跳转播放"
                print(res)
                if need_replay:
                    print(replay_links[index])
                if need_live:
                    print(live_links[index])
                if need_highlights:
                    print(highlights_links[index])
                history_qa += "用户：" + query + '\n'
                history_qa += "助手：" + res + '\n'
            else:
                # 4. integrate and answer
                integrate_prompt = open('integrate_prompt.txt', 'r').read()
                messages = [
                    {"role": "system", "content": integrate_prompt},
                    {"role": "user", "content": json.dumps({"user_profile": user_profile, "conversation_history": history_qa, "match_data": json.dumps(matches_info, ensure_ascii=False), "current_query": query}, ensure_ascii=False, indent=4)}
                ]
                print('DEBUG: messages:', messages[1])
                integrate_res = client_openai(messages)
                print('DEBUG: integrate res:', integrate_res)

                history_qa += "用户：" + query + '\n'
                history_qa += "助手：" + integrate_res + '\n'
            history_matches_info += matches_info
        elif s1_res['intent'] == 'Chat':
            print('与体育赛事无关的问题，目前不支持')
        else:
            # use search api
            print("search questions")