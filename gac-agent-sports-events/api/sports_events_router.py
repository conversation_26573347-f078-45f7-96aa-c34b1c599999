from fastapi import APIRouter, Response
import time
import uuid
import json
from loguru import logger
import asyncio
from typing_extensions import Union, Optional
from schemas.models import MemResultModel, ErrorDataModel
from sse_starlette.sse import EventSourceResponse as StreamResponse
from starlette.responses import JSONResponse as RestfulResponse
from main import current_version
from schemas.sports_agent_req import SportsAgentReq, UserMemoryReq, Message

sports_events_router = APIRouter(prefix="/gac/sports-events")

api_version = current_version


@sports_events_router.get("/v1/health")
async def health() -> Response:
    """Health check."""
    response_body = {"status": "ok", "version": api_version}
    response_content = json.dumps(response_body).encode("utf-8")
    response_header = {"Content-Type": "application/json"}
    return Response(status_code=200, content=response_content, headers=response_header)


@sports_events_router.post("/v1/chat")
async def call_post_sport_chat(agent_request: SportsAgentReq):
    start_time = time.perf_counter()
    request_id = uuid.uuid4()
    response_header = {"request_id": str(request_id)}
    try:
        from processor.processer_entrance import ProcessEntrance, SportProcesserState
        state = SportProcesserState(start_time=start_time, request_id = str(request_id))
        if agent_request.option_param:
            state["enable_time_filter_for_live"] = agent_request.option_param.filter_time_for_live

        logger.info("t0_mem start")
        habit_result: MemResultModel = await require_user_habit(agent_request.user_info, start_time, 0.5)
        state["t0_mem"] = habit_result.time_cost
        logger.info(f"t0_mem end-----cost time:{habit_result.time_cost}-----habit:{habit_result.result}")

        processor = ProcessEntrance(request=agent_request, response_header = response_header)
        to_notify_errors = None
        if habit_result.error_info:
            to_notify_errors = [habit_result.error_info]
        return await processor.process(state=state, habit = habit_result.result, to_notify_errors=to_notify_errors)
    except Exception as e:
        logger.error(e)
        import traceback
        traceback.print_exc()
        response = {"error": str(e)}
        return RestfulResponse(response, headers=response_header)


async def require_user_habit(source: Optional[UserMemoryReq], start_time: float, time_limit: float) -> MemResultModel:
    if not source or not source.car_id or not source.user_id or 0 >= len(source.category):
        return MemResultModel(result="", time_cost=round(time.perf_counter() - start_time, 4), error_info=None)

    async def require_habit_from_cache(source: UserMemoryReq):
        body_json = {
            "car_id": source.car_id,
            "face_id": source.user_id,
            "categories": source.category
        }
        error_data = None
        from utils.user_habit_util import request_habit_from_user_memory_text
        result_str, error_data = await request_habit_from_user_memory_text(body=body_json, relation_filter="LIKES", time_limit=time_limit)
        if not result_str:
            result_str = ""
        return result_str, error_data

    try:
        result, error_data = await asyncio.wait_for(require_habit_from_cache(source), timeout=time_limit)
        return MemResultModel(result=result, time_cost=round(time.perf_counter() - start_time, 4), error_info=error_data)
    except BaseException as e:
        logger.error(f"require_habit_from_cache timeout, e: {e}")
        return MemResultModel(result="", time_cost=round(time.perf_counter() - start_time, 4), error_info=None)
