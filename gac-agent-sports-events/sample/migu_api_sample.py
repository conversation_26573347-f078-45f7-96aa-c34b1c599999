from typing_extensions import Optional
import requests
from datetime import datetime
from schemas.models import MiguMatchApiResultModel, ErrorDataModel

from configuration import config


migu_cache_host = config["migu_source_data"]["host"]
client_id = config["migu_source_data"]["client_id"]
# 获取全部赛事目录的url
url_get_competition = migu_cache_host + config["migu_source_data"]["url_competition"]

def test_get_competition_info() -> MiguMatchApiResultModel:
    headers = {"client-id":client_id, "Content-Type":"application/json"}
    # print(headers)
    try:
        with requests.get(url_get_competition, headers=headers) as response:
            response.raise_for_status()
            result = response.text
            # print(result)
            return MiguMatchApiResultModel(result=result, error_info=None)
    except requests.exceptions.HTTPError as e:
        print(e)
        from constance.constance_value import error_code_match_category_http_error
        return MiguMatchApiResultModel(result=None, error_info=ErrorDataModel(error_code=error_code_match_category_http_error, error_message=f"Fail to get competition info, e: {str(e)}"))

# 查询比赛的url
url_query_matched = migu_cache_host + config["migu_source_data"]["url_matches"]
def test_query_matches(competition_name: str, start_time: Optional[int] = None, end_time: Optional[int] = None
                       , match_name: Optional[str] = None, size: Optional[int] = None
                       , contains_highlights: Optional[bool]= None
                       , contains_playback: Optional[bool]= None)-> MiguMatchApiResultModel:
    headers = {"client-id": client_id, "Content-Type": "application/json"}
    # print(headers)
    time_range = {}
    if start_time:
        time_range["start_time"] = start_time
    if end_time:
        time_range["end_time"] = end_time
    body = {
        "competition_name": competition_name,
        "time_range": time_range,
    }
    if match_name:
        body["match_name"] = match_name
    if size:
        body["size"] = str(size)
    if contains_highlights:
        # body["contains_highlights"] = contains_highlights
        pass
    if contains_playback:
        # body["contains_playback"] = contains_playback
        pass

    print(body)
    if start_time:
        print("start_time:", start_time, "=>", datetime.fromtimestamp(time_range["start_time"]).strftime("%Y-%m-%d %H:%M:%S"))
    if end_time:
        print("end_time:", end_time, "=>", datetime.fromtimestamp(time_range["end_time"]).strftime("%Y-%m-%d %H:%M:%S"))
    try:
        with requests.post(url_query_matched, headers=headers, json=body) as response:
            response.raise_for_status()
            result = response.text
            print(f"test_query_matches: {result}")
            return MiguMatchApiResultModel(result=result, error_info=None)
    except requests.exceptions.HTTPError as e:
        print(e)
        from constance.constance_value import error_code_match_search_http_error
        return MiguMatchApiResultModel(result=None,
                                       error_info=ErrorDataModel(error_code=error_code_match_search_http_error,
                                                                 error_message=f"Fail to get matches, required competition_name: {competition_name}, e: {str(e)}"))

url_get_score_new = migu_cache_host + config["migu_source_data"]["url_score"]
def get_game_score_new(pendantIds: list[str]) -> MiguMatchApiResultModel:
    if not pendantIds:
        return MiguMatchApiResultModel(result=None, error_info=None)
    headers = {"client-id": client_id, "Content-Type": "application/json"}
    param = {
        "pendant_ids" : ",".join(pendantIds),
    }

    try:
        with requests.get(url_get_score_new, headers=headers, params=param) as response:
            response.raise_for_status()
            return MiguMatchApiResultModel(result=response.text, error_info=None)
    except requests.exceptions.HTTPError as e:
        print(e)
        from constance.constance_value import error_code_score_search_http_error
        return MiguMatchApiResultModel(result=None,
                                       error_info=ErrorDataModel(error_code=error_code_score_search_http_error,
                                                                 error_message=f"Fail to get game score, e: {str(e)}"))